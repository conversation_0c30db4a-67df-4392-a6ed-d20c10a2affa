{"version": 3, "sources": ["../../@capacitor/preferences/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  PreferencesPlugin,\n  ConfigureOptions,\n  GetOptions,\n  GetResult,\n  SetOptions,\n  RemoveOptions,\n  KeysResult,\n  MigrateResult,\n} from './definitions';\n\nexport class PreferencesWeb extends WebPlugin implements PreferencesPlugin {\n  private group = 'CapacitorStorage';\n\n  public async configure({ group }: ConfigureOptions): Promise<void> {\n    if (typeof group === 'string') {\n      this.group = group;\n    }\n  }\n\n  public async get(options: GetOptions): Promise<GetResult> {\n    const value = this.impl.getItem(this.applyPrefix(options.key));\n\n    return { value };\n  }\n\n  public async set(options: SetOptions): Promise<void> {\n    this.impl.setItem(this.applyPrefix(options.key), options.value);\n  }\n\n  public async remove(options: RemoveOptions): Promise<void> {\n    this.impl.removeItem(this.applyPrefix(options.key));\n  }\n\n  public async keys(): Promise<KeysResult> {\n    const keys = this.rawKeys().map(k => k.substring(this.prefix.length));\n\n    return { keys };\n  }\n\n  public async clear(): Promise<void> {\n    for (const key of this.rawKeys()) {\n      this.impl.removeItem(key);\n    }\n  }\n\n  public async migrate(): Promise<MigrateResult> {\n    const migrated: string[] = [];\n    const existing: string[] = [];\n    const oldprefix = '_cap_';\n    const keys = Object.keys(this.impl).filter(k => k.indexOf(oldprefix) === 0);\n\n    for (const oldkey of keys) {\n      const key = oldkey.substring(oldprefix.length);\n      const value = this.impl.getItem(oldkey) ?? '';\n      const { value: currentValue } = await this.get({ key });\n\n      if (typeof currentValue === 'string') {\n        existing.push(key);\n      } else {\n        await this.set({ key, value });\n        migrated.push(key);\n      }\n    }\n\n    return { migrated, existing };\n  }\n\n  public async removeOld(): Promise<void> {\n    const oldprefix = '_cap_';\n    const keys = Object.keys(this.impl).filter(k => k.indexOf(oldprefix) === 0);\n    for (const oldkey of keys) {\n      this.impl.removeItem(oldkey);\n    }\n  }\n\n  private get impl(): Storage {\n    return window.localStorage;\n  }\n\n  private get prefix(): string {\n    return this.group === 'NativeStorage' ? '' : `${this.group}.`;\n  }\n\n  private rawKeys(): string[] {\n    return Object.keys(this.impl).filter(k => k.indexOf(this.prefix) === 0);\n  }\n\n  private applyPrefix(key: string) {\n    return this.prefix + key;\n  }\n}\n"], "mappings": ";;;;;;AAaM,IAAO,iBAAP,cAA8B,UAAS;EAA7C,cAAA;;AACU,SAAA,QAAQ;EA+ElB;EA7ES,MAAM,UAAU,EAAE,MAAK,GAAoB;AAChD,QAAI,OAAO,UAAU,UAAU;AAC7B,WAAK,QAAQ;;EAEjB;EAEO,MAAM,IAAI,SAAmB;AAClC,UAAM,QAAQ,KAAK,KAAK,QAAQ,KAAK,YAAY,QAAQ,GAAG,CAAC;AAE7D,WAAO,EAAE,MAAK;EAChB;EAEO,MAAM,IAAI,SAAmB;AAClC,SAAK,KAAK,QAAQ,KAAK,YAAY,QAAQ,GAAG,GAAG,QAAQ,KAAK;EAChE;EAEO,MAAM,OAAO,SAAsB;AACxC,SAAK,KAAK,WAAW,KAAK,YAAY,QAAQ,GAAG,CAAC;EACpD;EAEO,MAAM,OAAI;AACf,UAAM,OAAO,KAAK,QAAO,EAAG,IAAI,OAAK,EAAE,UAAU,KAAK,OAAO,MAAM,CAAC;AAEpE,WAAO,EAAE,KAAI;EACf;EAEO,MAAM,QAAK;AAChB,eAAW,OAAO,KAAK,QAAO,GAAI;AAChC,WAAK,KAAK,WAAW,GAAG;;EAE5B;EAEO,MAAM,UAAO;;AAClB,UAAM,WAAqB,CAAA;AAC3B,UAAM,WAAqB,CAAA;AAC3B,UAAM,YAAY;AAClB,UAAM,OAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAO,OAAK,EAAE,QAAQ,SAAS,MAAM,CAAC;AAE1E,eAAW,UAAU,MAAM;AACzB,YAAM,MAAM,OAAO,UAAU,UAAU,MAAM;AAC7C,YAAM,SAAK,KAAG,KAAK,KAAK,QAAQ,MAAM,OAAC,QAAA,OAAA,SAAA,KAAI;AAC3C,YAAM,EAAE,OAAO,aAAY,IAAK,MAAM,KAAK,IAAI,EAAE,IAAG,CAAE;AAEtD,UAAI,OAAO,iBAAiB,UAAU;AACpC,iBAAS,KAAK,GAAG;aACZ;AACL,cAAM,KAAK,IAAI,EAAE,KAAK,MAAK,CAAE;AAC7B,iBAAS,KAAK,GAAG;;;AAIrB,WAAO,EAAE,UAAU,SAAQ;EAC7B;EAEO,MAAM,YAAS;AACpB,UAAM,YAAY;AAClB,UAAM,OAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAO,OAAK,EAAE,QAAQ,SAAS,MAAM,CAAC;AAC1E,eAAW,UAAU,MAAM;AACzB,WAAK,KAAK,WAAW,MAAM;;EAE/B;EAEA,IAAY,OAAI;AACd,WAAO,OAAO;EAChB;EAEA,IAAY,SAAM;AAChB,WAAO,KAAK,UAAU,kBAAkB,KAAK,GAAG,KAAK,KAAK;EAC5D;EAEQ,UAAO;AACb,WAAO,OAAO,KAAK,KAAK,IAAI,EAAE,OAAO,OAAK,EAAE,QAAQ,KAAK,MAAM,MAAM,CAAC;EACxE;EAEQ,YAAY,KAAW;AAC7B,WAAO,KAAK,SAAS;EACvB;;", "names": []}