<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Employee Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { color: green; }
        .error { color: red; }
        button {
            padding: 10px 15px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        #results {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <h1>Test Employee Management Fix</h1>
    
    <div class="test-section">
        <h2>Employee Storage Test</h2>
        <button onclick="runEmployeeTest()">Run Employee Test</button>
        <button onclick="clearStorage()">Clear Storage</button>
        <button onclick="viewStorage()">View Storage</button>
    </div>
    
    <div class="test-section">
        <h2>Test Results</h2>
        <div id="results"></div>
    </div>

    <script>
        // Simple localStorage-based employee storage simulation
        const EMPLOYEE_STORAGE_KEY = 'smartboutique_csv_employees';
        
        function log(message) {
            const results = document.getElementById('results');
            results.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }
        
        function clearLog() {
            document.getElementById('results').textContent = '';
        }
        
        function getEmployees() {
            const data = localStorage.getItem(EMPLOYEE_STORAGE_KEY);
            if (!data) return [];
            
            try {
                // Simple CSV parsing simulation
                const lines = data.split('\n').filter(line => line.trim());
                if (lines.length <= 1) return []; // Only header or empty
                
                const headers = lines[0].split(',');
                const employees = [];
                
                for (let i = 1; i < lines.length; i++) {
                    const values = lines[i].split(',');
                    const employee = {};
                    headers.forEach((header, index) => {
                        employee[header.trim()] = values[index] ? values[index].trim() : '';
                    });
                    employees.push(employee);
                }
                
                return employees;
            } catch (error) {
                log('Error parsing employees: ' + error.message);
                return [];
            }
        }
        
        function saveEmployees(employees) {
            try {
                // Simple CSV generation
                const headers = ['id', 'nomComplet', 'poste', 'salaireCDF', 'salaireUSD', 'dateEmbauche', 'telephone', 'adresse', 'statut', 'notes', 'creePar', 'dateCreation'];
                let csv = headers.join(',') + '\n';
                
                employees.forEach(emp => {
                    const row = headers.map(header => emp[header] || '').join(',');
                    csv += row + '\n';
                });
                
                localStorage.setItem(EMPLOYEE_STORAGE_KEY, csv);
                return true;
            } catch (error) {
                log('Error saving employees: ' + error.message);
                return false;
            }
        }
        
        function addEmployee(employee) {
            const employees = getEmployees();
            employees.push(employee);
            return saveEmployees(employees);
        }
        
        function runEmployeeTest() {
            clearLog();
            log('=== Starting Employee Management Test ===');
            
            // Test 1: Check initial state
            log('\nTest 1: Getting initial employee list...');
            const initialEmployees = getEmployees();
            log('Initial employees count: ' + initialEmployees.length);
            
            // Test 2: Add first employee
            log('\nTest 2: Adding first employee...');
            const employee1 = {
                id: 'emp_' + Date.now() + '_test1',
                nomComplet: 'Jean Dupont',
                poste: 'Vendeur',
                salaireCDF: '500000',
                salaireUSD: '178.57',
                dateEmbauche: '2025-01-23',
                telephone: '+243123456789',
                adresse: 'Kinshasa, RDC',
                statut: 'actif',
                notes: 'Employé test 1',
                creePar: 'Test System',
                dateCreation: new Date().toISOString()
            };
            
            const added1 = addEmployee(employee1);
            if (added1) {
                log('✓ First employee added successfully');
            } else {
                log('✗ Failed to add first employee');
                return;
            }
            
            // Test 3: Verify first employee was saved
            log('\nTest 3: Verifying first employee was saved...');
            const employeesAfter1 = getEmployees();
            log('Employees count after adding first: ' + employeesAfter1.length);
            
            const found1 = employeesAfter1.find(emp => emp.id === employee1.id);
            if (found1) {
                log('✓ First employee found: ' + found1.nomComplet + ' - ' + found1.poste);
            } else {
                log('✗ First employee NOT found in storage');
                return;
            }
            
            // Test 4: Add second employee
            log('\nTest 4: Adding second employee...');
            const employee2 = {
                id: 'emp_' + Date.now() + '_test2',
                nomComplet: 'Marie Kabila',
                poste: 'Caissière',
                salaireCDF: '450000',
                salaireUSD: '160.71',
                dateEmbauche: '2025-01-23',
                telephone: '+243987654321',
                adresse: 'Lubumbashi, RDC',
                statut: 'actif',
                notes: 'Employé test 2',
                creePar: 'Test System',
                dateCreation: new Date().toISOString()
            };
            
            const added2 = addEmployee(employee2);
            if (added2) {
                log('✓ Second employee added successfully');
            } else {
                log('✗ Failed to add second employee');
                return;
            }
            
            // Test 5: Verify both employees are saved
            log('\nTest 5: Verifying both employees are saved...');
            const finalEmployees = getEmployees();
            log('Final employees count: ' + finalEmployees.length);
            
            finalEmployees.forEach((emp, index) => {
                log('Employee ' + (index + 1) + ': ' + emp.nomComplet + ' - ' + emp.poste);
            });
            
            if (finalEmployees.length === 2) {
                log('\n✓ SUCCESS: Both employees are properly saved and retrieved!');
                log('✓ Employee management functionality is working correctly');
            } else {
                log('\n✗ FAILURE: Expected 2 employees, found ' + finalEmployees.length);
            }
            
            log('\n=== Test Completed ===');
        }
        
        function clearStorage() {
            localStorage.removeItem(EMPLOYEE_STORAGE_KEY);
            log('Storage cleared');
        }
        
        function viewStorage() {
            clearLog();
            const employees = getEmployees();
            log('Current employees in storage: ' + employees.length);
            employees.forEach((emp, index) => {
                log('Employee ' + (index + 1) + ': ' + JSON.stringify(emp, null, 2));
            });
        }
    </script>
</body>
</html>
