const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./web-D5ZgLrU1.js","./mui-C1SsAu0U.js","./vendor-B_Ch-B_d.js","./utils-Bt0EYNjk.js","./charts-UhR5A4U7.js","./web-D1u89MI_.js"])))=>i.map(i=>d[i]);
var e,t=Object.defineProperty,n=(e,n,i)=>((e,n,i)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i)(e,"symbol"!=typeof n?n+"":n,i);import{j as i,B as a,A as r,T as s,a as o,b as l,P as c,c as d,d as u,D as m,e as h,L as p,f as x,g,h as y,i as j,I as S,k as C,l as D,M as v,m as b,R as f,n as w,o as E,S as P,p as F,q as U,r as T,N as k,s as R,t as M,C as A,u as I,v as N,w as L,x as O,y as V,z as q,E as B,F as _,G as z,W as $,H as W,J as X,K as Q,O as J,Q as H,U as Y,V as G,X as K,Y as Z,Z as ee,_ as te,$ as ne,a0 as ie,a1 as ae,a2 as re,a3 as se,a4 as oe,a5 as le,a6 as ce,a7 as de,a8 as ue,a9 as me,aa as he,ab as pe,ac as xe,ad as ge,ae as ye,af as je,ag as Se,ah as Ce,ai as De,aj as ve,ak as be,al as fe,am as we,an as Ee,ao as Pe,ap as Fe,aq as Ue,ar as Te,as as ke,at as Re,au as Me,av as Ae,aw as Ie,ax as Ne,ay as Le,az as Oe,aA as Ve,aB as qe,aC as Be,aD as _e,aE as ze,aF as $e,aG as We,aH as Xe,aI as Qe,aJ as Je,aK as He,aL as Ye,aM as Ge,aN as Ke,aO as Ze,aP as et,aQ as tt,aR as nt,aS as it,aT as at,aU as rt,aV as st,aW as ot,aX as lt,aY as ct,aZ as dt,a_ as ut,a$ as mt,b0 as ht,b1 as pt,b2 as xt,b3 as gt,b4 as yt,b5 as jt,b6 as St,b7 as Ct}from"./mui-C1SsAu0U.js";import{c as Dt,r as vt,R as bt,a as ft}from"./vendor-B_Ch-B_d.js";import{b as wt,a as Et,c as Pt,d as Ft,f as Ut,s as Tt,e as kt,g as Rt,i as Mt,h as At,j as It,k as Nt,l as Lt,m as Ot}from"./utils-Bt0EYNjk.js";import{L as Vt,D as qt,B as Bt,C as _t,a as zt,b as $t,P as Wt,c as Xt,d as Qt,p as Jt,e as Ht,f as Yt,A as Gt}from"./charts-UhR5A4U7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var Kt,Zt,en={},tn=Dt;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function nn(){return nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},nn.apply(this,arguments)}en.createRoot=tn.createRoot,en.hydrateRoot=tn.hydrateRoot,(Zt=Kt||(Kt={})).Pop="POP",Zt.Push="PUSH",Zt.Replace="REPLACE";const an="popstate";function rn(e){return void 0===e&&(e={}),function(e,t,n,i){void 0===i&&(i={});let{window:a=document.defaultView,v5Compat:r=!1}=i,s=a.history,o=Kt.Pop,l=null,c=d();null==c&&(c=0,s.replaceState(nn({},s.state,{idx:c}),""));function d(){return(s.state||{idx:null}).idx}function u(){o=Kt.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:x.location,delta:t})}function m(e,t){o=Kt.Push;let i=cn(x.location,e,t);n&&n(i,e),c=d()+1;let u=ln(i,c),m=x.createHref(i);try{s.pushState(u,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(m)}r&&l&&l({action:o,location:x.location,delta:1})}function h(e,t){o=Kt.Replace;let i=cn(x.location,e,t);n&&n(i,e),c=d();let a=ln(i,c),u=x.createHref(i);s.replaceState(a,"",u),r&&l&&l({action:o,location:x.location,delta:0})}function p(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"==typeof e?e:dn(e);return n=n.replace(/ $/,"%20"),sn(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return o},get location(){return e(a,s)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(an,u),l=e,()=>{a.removeEventListener(an,u),l=null}},createHref:e=>t(a,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:m,replace:h,go:e=>s.go(e)};return x}(function(e,t){let{pathname:n="/",search:i="",hash:a=""}=un(e.location.hash.substr(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),cn("",{pathname:n,search:i,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),i="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");i=-1===n?t:t.slice(0,n)}return i+"#"+("string"==typeof t?t:dn(t))},function(e,t){on("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function sn(e,t){if(!1===e||null==e)throw new Error(t)}function on(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function ln(e,t){return{usr:e.state,key:e.key,idx:t}}function cn(e,t,n,i){return void 0===n&&(n=null),nn({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?un(t):t,{state:n,key:t&&t.key||i||Math.random().toString(36).substr(2,8)})}function dn(e){let{pathname:t="/",search:n="",hash:i=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),i&&"#"!==i&&(t+="#"===i.charAt(0)?i:"#"+i),t}function un(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substr(i),e=e.substr(0,i)),e&&(t.pathname=e)}return t}var mn,hn;function pn(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let i="string"==typeof t?un(t):t,a=Fn(i.pathname||"/",n);if(null==a)return null;let r=xn(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(r);let s=null;for(let o=0;null==s&&o<r.length;++o){let e=Pn(a);s=wn(r[o],e)}return s}(e,t,n)}function xn(e,t,n,i){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===i&&(i="");let a=(e,a,r)=>{let s={relativePath:void 0===r?e.path||"":r,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};s.relativePath.startsWith("/")&&(sn(s.relativePath.startsWith(i),'Absolute route path "'+s.relativePath+'" nested under path "'+i+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),s.relativePath=s.relativePath.slice(i.length));let o=Rn([i,s.relativePath]),l=n.concat(s);e.children&&e.children.length>0&&(sn(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),xn(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:fn(o,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let i of gn(e.path))a(e,t,i);else a(e,t)}),t}function gn(e){let t=e.split("/");if(0===t.length)return[];let[n,...i]=t,a=n.endsWith("?"),r=n.replace(/\?$/,"");if(0===i.length)return a?[r,""]:[r];let s=gn(i.join("/")),o=[];return o.push(...s.map(e=>""===e?r:[r,e].join("/"))),a&&o.push(...s),o.map(t=>e.startsWith("/")&&""===t?"/":t)}(hn=mn||(mn={})).data="data",hn.deferred="deferred",hn.redirect="redirect",hn.error="error";const yn=/^:[\w-]+$/,jn=3,Sn=2,Cn=1,Dn=10,vn=-2,bn=e=>"*"===e;function fn(e,t){let n=e.split("/"),i=n.length;return n.some(bn)&&(i+=vn),t&&(i+=Sn),n.filter(e=>!bn(e)).reduce((e,t)=>e+(yn.test(t)?jn:""===t?Cn:Dn),i)}function wn(e,t,n){let{routesMeta:i}=e,a={},r="/",s=[];for(let o=0;o<i.length;++o){let e=i[o],n=o===i.length-1,l="/"===r?t:t.slice(r.length)||"/",c=En({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},l),d=e.route;if(!c)return null;Object.assign(a,c.params),s.push({params:a,pathname:Rn([r,c.pathname]),pathnameBase:Mn(Rn([r,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(r=Rn([r,c.pathnameBase]))}return s}function En(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);on("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let i=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(i.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(i.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let r=new RegExp(a,t?void 0:"i");return[r,i]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let r=a[0],s=r.replace(/(.)\/+$/,"$1"),o=a.slice(1);return{params:i.reduce((e,t,n)=>{let{paramName:i,isOptional:a}=t;if("*"===i){let e=o[n]||"";s=r.slice(0,r.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[i]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:r,pathnameBase:s,pattern:e}}function Pn(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return on(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Fn(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&"/"!==i?null:e.slice(n)||"/"}function Un(e,t,n,i){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(i)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Tn(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function kn(e,t,n,i){let a;void 0===i&&(i=!1),"string"==typeof e?a=un(e):(a=nn({},e),sn(!a.pathname||!a.pathname.includes("?"),Un("?","pathname","search",a)),sn(!a.pathname||!a.pathname.includes("#"),Un("#","pathname","hash",a)),sn(!a.search||!a.search.includes("#"),Un("#","search","hash",a)));let r,s=""===e||""===a.pathname,o=s?"/":a.pathname;if(null==o)r=n;else{let e=t.length-1;if(!i&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}r=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:i="",hash:a=""}="string"==typeof e?un(e):e,r=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:r,search:An(i),hash:In(a)}}(a,r),c=o&&"/"!==o&&o.endsWith("/"),d=(s||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const Rn=e=>e.join("/").replace(/\/\/+/g,"/"),Mn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),An=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",In=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Nn=["post","put","patch","delete"];new Set(Nn);const Ln=["get",...Nn];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function On(){return On=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},On.apply(this,arguments)}new Set(Ln);const Vn=vt.createContext(null),qn=vt.createContext(null),Bn=vt.createContext(null),_n=vt.createContext(null),zn=vt.createContext({outlet:null,matches:[],isDataRoute:!1}),$n=vt.createContext(null);function Wn(){return null!=vt.useContext(_n)}function Xn(){return Wn()||sn(!1),vt.useContext(_n).location}function Qn(e){vt.useContext(Bn).static||vt.useLayoutEffect(e)}function Jn(){let{isDataRoute:e}=vt.useContext(zn);return e?function(){let{router:e}=function(){let e=vt.useContext(Vn);return e||sn(!1),e}(ti.UseNavigateStable),t=ii(ni.UseNavigateStable),n=vt.useRef(!1);return Qn(()=>{n.current=!0}),vt.useCallback(function(i,a){void 0===a&&(a={}),n.current&&("number"==typeof i?e.navigate(i):e.navigate(i,On({fromRouteId:t},a)))},[e,t])}():function(){Wn()||sn(!1);let e=vt.useContext(Vn),{basename:t,future:n,navigator:i}=vt.useContext(Bn),{matches:a}=vt.useContext(zn),{pathname:r}=Xn(),s=JSON.stringify(Tn(a,n.v7_relativeSplatPath)),o=vt.useRef(!1);return Qn(()=>{o.current=!0}),vt.useCallback(function(n,a){if(void 0===a&&(a={}),!o.current)return;if("number"==typeof n)return void i.go(n);let l=kn(n,JSON.parse(s),r,"path"===a.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:Rn([t,l.pathname])),(a.replace?i.replace:i.push)(l,a.state,a)},[t,i,s,r,e])}()}const Hn=vt.createContext(null);function Yn(e,t){return function(e,t,n,i){Wn()||sn(!1);let{navigator:a}=vt.useContext(Bn),{matches:r}=vt.useContext(zn),s=r[r.length-1],o=s?s.params:{};!s||s.pathname;let l=s?s.pathnameBase:"/";s&&s.route;let c,d=Xn();if(t){var u;let e="string"==typeof t?un(t):t;"/"===l||(null==(u=e.pathname)?void 0:u.startsWith(l))||sn(!1),c=e}else c=d;let m=c.pathname||"/",h=m;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=pn(e,{pathname:h}),x=function(e,t,n,i){var a;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===i&&(i=null);if(null==e){var r;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(r=i)&&r.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let s=e,o=null==(a=n)?void 0:a.errors;if(null!=o){let e=s.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||sn(!1),s=s.slice(0,Math.min(s.length,e+1))}let l=!1,c=-1;if(n&&i&&i.v7_partialHydration)for(let d=0;d<s.length;d++){let e=s[d];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=d),e.route.id){let{loaderData:t,errors:i}=n,a=e.route.loader&&void 0===t[e.route.id]&&(!i||void 0===i[e.route.id]);if(e.route.lazy||a){l=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight((e,i,a)=>{let r,d=!1,u=null,m=null;var h;n&&(r=o&&i.route.id?o[i.route.id]:void 0,u=i.route.errorElement||Kn,l&&(c<0&&0===a?(ai[h="route-fallback"]||(ai[h]=!0),d=!0,m=null):c===a&&(d=!0,m=i.route.hydrateFallbackElement||null)));let p=t.concat(s.slice(0,a+1)),x=()=>{let t;return t=r?u:d?m:i.route.Component?vt.createElement(i.route.Component,null):i.route.element?i.route.element:e,vt.createElement(ei,{match:i,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(i.route.ErrorBoundary||i.route.errorElement||0===a)?vt.createElement(Zn,{location:n.location,revalidation:n.revalidation,component:u,error:r,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:Rn([l,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:Rn([l,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),r,n,i);if(t&&x)return vt.createElement(_n.Provider,{value:{location:On({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Kt.Pop}},x);return x}(e,t)}function Gn(){let e=function(){var e;let t=vt.useContext($n),n=function(){let e=vt.useContext(qn);return e||sn(!1),e}(),i=ii();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[i]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return vt.createElement(vt.Fragment,null,vt.createElement("h2",null,"Unexpected Application Error!"),vt.createElement("h3",{style:{fontStyle:"italic"}},t),n?vt.createElement("pre",{style:i},n):null,null)}const Kn=vt.createElement(Gn,null);class Zn extends vt.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?vt.createElement(zn.Provider,{value:this.props.routeContext},vt.createElement($n.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ei(e){let{routeContext:t,match:n,children:i}=e,a=vt.useContext(Vn);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),vt.createElement(zn.Provider,{value:t},i)}var ti=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ti||{}),ni=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ni||{});function ii(e){let t=function(){let e=vt.useContext(zn);return e||sn(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||sn(!1),n.route.id}const ai={};function ri(e){let{to:t,replace:n,state:i,relative:a}=e;Wn()||sn(!1);let{future:r,static:s}=vt.useContext(Bn),{matches:o}=vt.useContext(zn),{pathname:l}=Xn(),c=Jn(),d=kn(t,Tn(o,r.v7_relativeSplatPath),l,"path"===a),u=JSON.stringify(d);return vt.useEffect(()=>c(JSON.parse(u),{replace:n,state:i,relative:a}),[c,u,a,n,i]),null}function si(e){return function(e){let t=vt.useContext(zn).outlet;return t?vt.createElement(Hn.Provider,{value:e},t):t}(e.context)}function oi(e){sn(!1)}function li(e){let{basename:t="/",children:n=null,location:i,navigationType:a=Kt.Pop,navigator:r,static:s=!1,future:o}=e;Wn()&&sn(!1);let l=t.replace(/^\/*/,"/"),c=vt.useMemo(()=>({basename:l,navigator:r,static:s,future:On({v7_relativeSplatPath:!1},o)}),[l,o,r,s]);"string"==typeof i&&(i=un(i));let{pathname:d="/",search:u="",hash:m="",state:h=null,key:p="default"}=i,x=vt.useMemo(()=>{let e=Fn(d,l);return null==e?null:{location:{pathname:e,search:u,hash:m,state:h,key:p},navigationType:a}},[l,d,u,m,h,p,a]);return null==x?null:vt.createElement(Bn.Provider,{value:c},vt.createElement(_n.Provider,{children:n,value:x}))}function ci(e){let{children:t,location:n}=e;return Yn(di(t),n)}function di(e,t){void 0===t&&(t=[]);let n=[];return vt.Children.forEach(e,(e,i)=>{if(!vt.isValidElement(e))return;let a=[...t,i];if(e.type===vt.Fragment)return void n.push.apply(n,di(e.props.children,a));e.type!==oi&&sn(!1),e.props.index&&e.props.children&&sn(!1);let r={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(r.children=di(e.props.children,a)),n.push(r)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise(()=>{});try{window.__reactRouterVersion="6"}catch(jr){}const ui=bt.startTransition;function mi(e){let{basename:t,children:n,future:i,window:a}=e,r=vt.useRef();null==r.current&&(r.current=rn({window:a,v5Compat:!0}));let s=r.current,[o,l]=vt.useState({action:s.action,location:s.location}),{v7_startTransition:c}=i||{},d=vt.useCallback(e=>{c&&ui?ui(()=>l(e)):l(e)},[l,c]);return vt.useLayoutEffect(()=>s.listen(d),[s,d]),vt.useEffect(()=>{return null==(e=i)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[i]),vt.createElement(li,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:s,future:i})}var hi,pi,xi,gi;(pi=hi||(hi={})).UseScrollRestoration="useScrollRestoration",pi.UseSubmit="useSubmit",pi.UseSubmitFetcher="useSubmitFetcher",pi.UseFetcher="useFetcher",pi.useViewTransitionState="useViewTransitionState",(gi=xi||(xi={})).UseFetcher="useFetcher",gi.UseFetchers="useFetchers",gi.UseScrollRestoration="useScrollRestoration";const yi={components:{MuiBreadcrumbs:{defaultProps:{expandText:"Montrer le chemin"}},MuiTablePagination:{defaultProps:{getItemAriaLabel:e=>"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente",labelRowsPerPage:"Lignes par page :",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}–${t} sur ${-1!==n?n:`plus que ${t}`}`}},MuiRating:{defaultProps:{getLabelText:e=>`${e} Etoile${1!==e?"s":""}`,emptyLabelText:"Vide"}},MuiAutocomplete:{defaultProps:{clearText:"Vider",closeText:"Fermer",loadingText:"Chargement…",noOptionsText:"Pas de résultats",openText:"Ouvrir"}},MuiAlert:{defaultProps:{closeText:"Fermer"}},MuiPagination:{defaultProps:{"aria-label":"navigation de pagination",getItemAriaLabel:(e,t,n)=>"page"===e?`${n?"":"Aller à la "}page ${t}`:"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente"}}}},ji={},Si=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),a=document.querySelector("meta[property=csp-nonce]"),r=(null==a?void 0:a.nonce)||(null==a?void 0:a.getAttribute("nonce"));i=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,n),t in ji)return;ji[t]=!0;const i=t.endsWith(".css"),a=i?'[rel="stylesheet"]':"";if(!!n)for(let n=e.length-1;n>=0;n--){const a=e[n];if(a.href===t&&(!i||"stylesheet"===a.rel))return}else if(document.querySelector(`link[href="${t}"]${a}`))return;const s=document.createElement("link");return s.rel=i?"stylesheet":"modulepreload",i||(s.as="script"),s.crossOrigin="",s.href=t,r&&s.setAttribute("nonce",r),document.head.appendChild(s),i?new Promise((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return i.then(t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)})};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var Ci,Di;(Di=Ci||(Ci={})).Unimplemented="UNIMPLEMENTED",Di.Unavailable="UNAVAILABLE";class vi extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const bi=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},i=n.Plugins=n.Plugins||{},a=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),r=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},s=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=a,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==a(),n.isPluginAvailable=e=>{const t=s.get(e);return!!(null==t?void 0:t.platforms.has(a()))||!!r(e)},n.registerPlugin=(e,o={})=>{const l=s.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=a(),d=r(e);let u;const m=i=>{let a;const r=(...r)=>{const s=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then(t=>{const s=((t,i)=>{var a,r;if(!d){if(t)return null===(r=t[i])||void 0===r?void 0:r.bind(t);throw new vi(`"${e}" plugin is not implemented on ${c}`,Ci.Unimplemented)}{const r=null==d?void 0:d.methods.find(e=>i===e.name);if(r)return"promise"===r.rtype?t=>n.nativePromise(e,i.toString(),t):(t,a)=>n.nativeCallback(e,i.toString(),t,a);if(t)return null===(a=t[i])||void 0===a?void 0:a.bind(t)}})(t,i);if(s){const e=s(...r);return a=null==e?void 0:e.remove,e}throw new vi(`"${e}.${i}()" is not implemented on ${c}`,Ci.Unimplemented)});return"addListener"===i&&(s.remove=async()=>a()),s};return r.toString=()=>`${i.toString()}() { [capacitor code] }`,Object.defineProperty(r,"name",{value:i,writable:!1,configurable:!1}),r},h=m("addListener"),p=m("removeListener"),x=(e,t)=>{const n=h({eventName:e},t),i=async()=>{const i=await n;p({eventName:e,callbackId:i},t)},a=new Promise(e=>n.then(()=>e({remove:i})));return a.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await i()},a},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?x:h;case"removeListener":return p;default:return m(t)}}});return i[e]=g,s.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},n.Exception=vi,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},fi=(e=>e.Capacitor=bi(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),wi=fi.registerPlugin;class Ei{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const i=this.listeners[e];if(i)i.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new fi.Exception(e,Ci.Unimplemented)}unavailable(e="not available"){return new fi.Exception(e,Ci.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const i=n.indexOf(t);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const Pi=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),Fi=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ui extends Ei{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,i]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=Fi(n).trim(),i=Fi(i).trim(),t[n]=i}),t}async setCookie(e){try{const t=Pi(e.key),n=Pi(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,a=(e.path||"/").replace("path=",""),r=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${i}; path=${a}; ${r};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}wi("CapacitorCookies",{web:()=>new Ui});const Ti=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),i=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,i,a)=>(n[i]=e[t[a]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(i.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,i]of Object.entries(e.data||{}))t.set(n,i);n.body=t.toString()}else if(i.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const i=new Headers(n.headers);i.delete("content-type"),n.headers=i}else(i.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class ki extends Ei{async request(e){const t=Ti(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[i,a]=n;let r,s;return Array.isArray(a)?(s="",a.forEach(e=>{r=t?encodeURIComponent(e):e,s+=`${i}=${r}&`}),s.slice(0,-1)):(r=t?encodeURIComponent(a):a,s=`${i}=${r}`),`${e}&${s}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),i=n?`${e.url}?${n}`:e.url,a=await fetch(i,t),r=a.headers.get("content-type")||"";let s,o,{responseType:l="text"}=a.ok?e:{};switch(r.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await a.blob(),s=await(async e=>new Promise((t,n)=>{const i=new FileReader;i.onload=()=>{const e=i.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},i.onerror=e=>n(e),i.readAsDataURL(e)}))(o);break;case"json":s=await a.json();break;default:s=await a.text()}const c={};return a.headers.forEach((e,t)=>{c[t]=e}),{data:s,headers:c,status:a.status,url:a.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}wi("CapacitorHttp",{web:()=>new ki});const Ri=()=>{const e=fi.getPlatform();return{isMobile:fi.isNativePlatform(),isDesktop:!fi.isNativePlatform(),isAndroid:"android"===e,isIOS:"ios"===e,isWeb:"web"===e,platform:e}},Mi=()=>fi.isNativePlatform();class Ai{static arrayToCSV(e,t,n=!0){if(!e||0===e.length){const e=t.map(e=>e.header).join(",")+"\n";return n?"\ufeff"+e:e}const i=[t.map(e=>e.header).join(","),...e.map(e=>t.map(t=>{let n=e[t.key];return null==n?n="":"date"===t.type&&n?n=new Date(n).toISOString().split("T")[0]:"boolean"===t.type?n=n?"Oui":"Non":"object"==typeof n&&(n=JSON.stringify(n)),n=String(n),(n.includes(",")||n.includes('"')||n.includes("\n"))&&(n='"'+n.replace(/"/g,'""')+'"'),n}).join(","))].join("\n");return n?"\ufeff"+i:i}static createExcelCompatibleBlob(e){return new Blob([e],{type:"text/csv;charset=utf-8"})}static downloadCSV(e,t,n){const i=Ai.arrayToCSV(e,t,!0),a=Ai.createExcelCompatibleBlob(i),r=URL.createObjectURL(a),s=document.createElement("a");s.href=r,s.download=n,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(r)}static csvToArray(e,t){if(!e||""===e.trim())return[];const n=this.parseCSVLines(e);if(n.length<=1)return[];return n.slice(1).map((e,n)=>{const i=e,a={};return t.forEach((e,t)=>{let n=i[t]||"";"number"===e.type?n=""===n?0:parseFloat(n)||0:"boolean"===e.type?n="oui"===n.toLowerCase()||"true"===n.toLowerCase()||"1"===n:"date"===e.type&&n?n=new Date(n).toISOString():e.key.includes("prix")&&"string"==typeof n&&(n=parseFloat(n)||0),a[e.key]=n}),a.id||(a.id=String(n+1)),a})}static parseCSVLines(e){const t=[],n=e.split("\n");for(const i of n){if(""===i.trim())continue;const e=[];let n="",a=!1,r=0;for(;r<i.length;){const t=i[r];'"'===t?a&&'"'===i[r+1]?(n+='"',r+=2):(a=!a,r++):","!==t||a?(n+=t,r++):(e.push(n),n="",r++)}e.push(n),t.push(e)}return t}static validateCSVData(e,t){const n=[];return e.forEach((e,i)=>{t.forEach(t=>{!t.required||void 0!==e[t.key]&&null!==e[t.key]&&""!==e[t.key]||n.push(`Ligne ${i+2}: Le champ "${t.header}" est requis`),"number"===t.type&&void 0!==e[t.key]&&isNaN(Number(e[t.key]))&&n.push(`Ligne ${i+2}: Le champ "${t.header}" doit être un nombre`)})}),{isValid:0===n.length,errors:n}}static generateTemplate(e){const t=[{}];return e.forEach(e=>{switch(e.type){case"string":t[0][e.key]="Exemple";break;case"number":t[0][e.key]=100;break;case"boolean":t[0][e.key]=!0;break;case"date":t[0][e.key]=(new Date).toISOString()}}),this.arrayToCSV(t,e)}}const Ii=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom du Produit",type:"string",required:!0},{key:"description",header:"Description",type:"string"},{key:"prixAchatCDF",header:"Prix d'achat CDF",type:"number",required:!0},{key:"prixAchatUSD",header:"Prix d'achat USD",type:"number"},{key:"prixCDF",header:"Prix de vente CDF",type:"number",required:!0},{key:"prixUSD",header:"Prix de vente USD",type:"number"},{key:"beneficeUnitaireCDF",header:"Bénéfice unitaire CDF",type:"number"},{key:"beneficeUnitaireUSD",header:"Bénéfice unitaire USD",type:"number"},{key:"codeQR",header:"Code QR",type:"string"},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"stock",header:"Stock",type:"number",required:!0},{key:"stockMin",header:"Stock Minimum",type:"number"},{key:"codeBarres",header:"Code Barres",type:"string"},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"dateModification",header:"Date de Modification",type:"date"}],Ni=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom",type:"string",required:!0},{key:"email",header:"Email",type:"string",required:!0},{key:"role",header:"Rôle",type:"string",required:!0},{key:"motDePasse",header:"Mot de Passe",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"actif",header:"Actif",type:"boolean"}],Li=[{key:"id",header:"ID",type:"string",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"client",header:"Client",type:"string"},{key:"produits",header:"Produits (JSON)",type:"string",required:!0},{key:"totalCDF",header:"Total CDF",type:"number",required:!0},{key:"totalUSD",header:"Total USD",type:"number",required:!0},{key:"typePaiement",header:"Type de Paiement",type:"string",required:!0},{key:"typeVente",header:"Type de Vente",type:"string",required:!0},{key:"vendeur",header:"Vendeur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],Oi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomClient",header:"Client",type:"string",required:!0},{key:"telephoneClient",header:"Téléphone",type:"string"},{key:"adresseClient",header:"Adresse",type:"string"},{key:"montantTotalCDF",header:"Montant Total CDF",type:"number",required:!0},{key:"montantTotalUSD",header:"Montant Total USD",type:"number"},{key:"montantPayeCDF",header:"Montant Payé CDF",type:"number",required:!0},{key:"montantPayeUSD",header:"Montant Payé USD",type:"number"},{key:"montantRestantCDF",header:"Montant Restant CDF",type:"number",required:!0},{key:"montantRestantUSD",header:"Montant Restant USD",type:"number"},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateEcheance",header:"Date d'Échéance",type:"date"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"statutPaiement",header:"Statut de Paiement",type:"string",required:!0},{key:"venteId",header:"ID de Vente",type:"string"},{key:"paiements",header:"Paiements",type:"string"},{key:"notes",header:"Notes",type:"string"}],Vi=[{key:"id",header:"ID",type:"string",required:!0},{key:"description",header:"Description",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"utilisateur",header:"Utilisateur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],qi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomComplet",header:"Nom Complet",type:"string",required:!0},{key:"poste",header:"Poste",type:"string",required:!0},{key:"salaireCDF",header:"Salaire CDF",type:"number",required:!0},{key:"salaireUSD",header:"Salaire USD",type:"number"},{key:"dateEmbauche",header:"Date d'Embauche",type:"date",required:!0},{key:"telephone",header:"Téléphone",type:"string"},{key:"adresse",header:"Adresse",type:"string"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Bi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomEmploye",header:"Nom Employé",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number"},{key:"datePaiement",header:"Date de Paiement",type:"date",required:!0},{key:"methodePaiement",header:"Méthode de Paiement",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],_i=[{key:"cle",header:"Clé",type:"string",required:!0},{key:"valeur",header:"Valeur",type:"string",required:!0},{key:"type",header:"Type",type:"string",required:!0},{key:"description",header:"Description",type:"string"}];function zi(e){var t,n;const i=[];return i.push({cle:"tauxChangeUSDCDF",valeur:(null==(t=e.tauxChangeUSDCDF)?void 0:t.toString())||"2800",type:"number",description:"Taux de change USD vers CDF"}),i.push({cle:"seuilStockBas",valeur:(null==(n=e.seuilStockBas)?void 0:n.toString())||"10",type:"number",description:"Seuil de stock bas"}),e.categories&&Array.isArray(e.categories)&&i.push({cle:"categories",valeur:JSON.stringify(e.categories),type:"json",description:"Catégories de produits"}),e.entreprise&&i.push({cle:"entreprise",valeur:JSON.stringify(e.entreprise),type:"json",description:"Informations de l'entreprise"}),e.impression&&i.push({cle:"impression",valeur:JSON.stringify(e.impression),type:"json",description:"Paramètres d'impression des reçus"}),i}const $i=new class{constructor(){n(this,"prefix","smartboutique_csv_")}getKey(e){return`${this.prefix}${e}`}setCSV(e,t,n){try{const i=Ai.arrayToCSV(t,n);localStorage.setItem(this.getKey(e),i)}catch(i){console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i)}}getCSV(e,t,n=[]){try{const i=localStorage.getItem(this.getKey(e));return i?Ai.csvToArray(i,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}set(e,t){try{const n=JSON.stringify(t);localStorage.setItem(this.getKey(e),n)}catch(n){console.error("Erreur lors de la sauvegarde:",n)}}get(e,t){try{const n=localStorage.getItem(this.getKey(e));return null===n?t:JSON.parse(n)}catch(n){return console.error("Erreur lors de la lecture:",n),t}}remove(e){localStorage.removeItem(this.getKey(e))}clear(){Object.keys(localStorage).forEach(e=>{e.startsWith(this.prefix)&&localStorage.removeItem(e)})}getUsers(){return this.getCSV("users",Ni,[])}setUsers(e){this.setCSV("users",e,Ni)}getProducts(){return this.getCSV("products",Ii,[]).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}setProducts(e){this.setCSV("products",e,Ii)}getSales(){try{const e=this.getCSV("sales",Li,[]);return e.filter(e=>e&&"object"==typeof e).map(e=>{const t={...e,datevente:e.date||e.datevente||(new Date).toISOString(),methodePaiement:e.typePaiement||e.methodePaiement||"cash",nomClient:e.client||e.nomClient||"Client"};t.totalCDF=Number(t.totalCDF)||0,t.totalUSD=t.totalUSD?Number(t.totalUSD):void 0;let n=t.produits||[];if("string"==typeof n)try{n=JSON.parse(n)}catch(i){console.warn("Error parsing produits JSON:",i),n=[]}return Array.isArray(n)?t.produits=n.map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})):t.produits=[],t})}catch(e){return console.error("Error getting sales data:",e),[]}}setSales(e){const t=e.map(e=>({...e,date:e.datevente||e.date,typePaiement:e.methodePaiement||e.typePaiement,client:e.nomClient||e.client,produits:"string"==typeof e.produits?e.produits:JSON.stringify(e.produits||[])}));this.setCSV("sales",t,Li)}getDebts(){return this.getCSV("debts",Oi,[]).map(e=>{e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0;let t=e.paiements||[];if("string"==typeof t)try{t=JSON.parse(t)}catch(n){console.warn("Error parsing paiements JSON:",n),t=[]}return Array.isArray(t)?e.paiements=t.map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})):e.paiements=[],e})}setDebts(e){this.setCSV("debts",e,Oi)}getDettes(){return this.getDebts()}setDettes(e){this.setDebts(e)}getCreances(){return this.getDebts()}setCreances(e){this.setDebts(e)}getExpenses(){return this.getCSV("expenses",Vi,[])}setExpenses(e){this.setCSV("expenses",e,Vi)}getEmployeePayments(){return this.getCSV("employee_payments",Bi,[])}setEmployeePayments(e){this.setCSV("employee_payments",e,Bi)}addEmployeePayment(e){const t=this.getEmployeePayments();t.push(e),this.setEmployeePayments(t)}updateEmployeePayment(e){const t=this.getEmployeePayments(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployeePayments(t))}deleteEmployeePayment(e){const t=this.getEmployeePayments().filter(t=>t.id!==e);this.setEmployeePayments(t)}getEmployees(){return this.getCSV("employees",qi,[])}setEmployees(e){this.setCSV("employees",e,qi)}addEmployee(e){const t=this.getEmployees();t.push(e),this.setEmployees(t)}updateEmployee(e){const t=this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployees(t))}deleteEmployee(e){const t=this.getEmployees().filter(t=>t.id!==e);this.setEmployees(t)}getSettings(){return this.get("settings",{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+243 000 000 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}})}setSettings(e){this.set("settings",e)}getCurrentUser(){return this.get("currentUser",null)}setCurrentUser(e){this.set("currentUser",e)}initializeDefaultData(){if(0===this.getUsers().length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];this.setUsers(e)}0===this.getProducts().length&&this.initializeProductCatalog();0===this.getSales().length&&this.initializeSampleSales();0===this.getDebts().length&&this.initializeSampleDebts()}clearAllData(){localStorage.clear(),console.log("All localStorage data cleared")}initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixAchatCDF:168e4,prixAchatUSD:600,prixCDF:224e4,prixUSD:800,beneficeUnitaireCDF:56e4,beneficeUnitaireUSD:200,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixAchatCDF:7e4,prixAchatUSD:25,prixCDF:98e3,prixUSD:35,beneficeUnitaireCDF:28e3,beneficeUnitaireUSD:10,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixAchatCDF:25200,prixAchatUSD:9,prixCDF:33600,prixUSD:12,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixAchatCDF:6300,prixAchatUSD:2.25,prixCDF:8400,prixUSD:3,beneficeUnitaireCDF:2100,beneficeUnitaireUSD:.75,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixAchatCDF:33600,prixAchatUSD:12,prixCDF:42e3,prixUSD:15,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];this.setProducts(e)}initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),i=[{id:"1",datevente:t.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant"},{id:"2",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70},{produitId:"4",nomProduit:"Sucre",quantite:3,prixUnitaireCDF:8400,prixUnitaireUSD:3,totalCDF:25200,totalUSD:9}],totalCDF:221200,totalUSD:79,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement mobile money"},{id:"3",datevente:n.toISOString(),nomClient:"Paul Tshisekedi",telephoneClient:"+243 900 000 003",produits:[{produitId:"5",nomProduit:"Riz",quantite:2,prixUnitaireCDF:42e3,prixUnitaireUSD:15,totalCDF:84e3,totalUSD:30}],totalCDF:84e3,totalUSD:30,methodePaiement:"card",typeVente:"cash",vendeur:"Employé",notes:"Paiement par carte"}];this.setSales(i)}initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),a=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];this.setDebts(r)}exportData(){const e=this.getUsers(),t=this.getProducts(),n=this.getSales(),i=this.getDebts(),a=this.getExpenses(),r=this.getEmployeePayments(),s=this.getSettings(),o={exportDate:(new Date).toISOString(),products:Ai.arrayToCSV(t,Ii),users:Ai.arrayToCSV(e,Ni),sales:Ai.arrayToCSV(n,Li),debts:Ai.arrayToCSV(i,Oi),expenses:Ai.arrayToCSV(a,Vi),employeePayments:Ai.arrayToCSV(r,Bi),settings:Ai.arrayToCSV(zi(s),_i)};return{csvData:`SmartBoutique - Sauvegarde Complète (Desktop)\nDate d'exportation: ${o.exportDate}\n\n=== PRODUITS ===\n${o.products}\n\n=== UTILISATEURS ===\n${o.users}\n\n=== VENTES ===\n${o.sales}\n\n=== DETTES ===\n${o.debts}\n\n=== DÉPENSES ===\n${o.expenses}\n\n=== PAIEMENTS EMPLOYÉS ===\n${o.employeePayments}\n\n=== PARAMÈTRES ===\n${o.settings}\n`,exportDate:o.exportDate}}importData(e){try{return e.csvData&&"string"==typeof e.csvData?this.importFromCSVBackup(e.csvData):(e.users&&this.setUsers(e.users),e.products&&this.setProducts(e.products),e.sales&&this.setSales(e.sales),e.debts&&this.setDebts(e.debts),e.expenses&&this.setExpenses(e.expenses),e.employeePayments&&this.setEmployeePayments(e.employeePayments),e.settings&&this.setSettings(e.settings),!0)}catch(t){return console.error("Erreur lors de l'importation:",t),!1}}importFromCSVBackup(e){try{const t=this.parseCSVBackup(e);if(t.products){const e=Ai.csvToArray(t.products,Ii);this.setProducts(e)}if(t.users){const e=Ai.csvToArray(t.users,Ni);this.setUsers(e)}if(t.sales){const e=Ai.csvToArray(t.sales,Li);this.setSales(e)}if(t.debts){const e=Ai.csvToArray(t.debts,Oi);this.setDebts(e)}if(t.expenses){const e=Ai.csvToArray(t.expenses,Vi);this.setExpenses(e)}if(t.employeePayments){const e=Ai.csvToArray(t.employeePayments,Bi);this.setEmployeePayments(e)}if(t.settings){const e=function(e){const t={};return e.forEach(e=>{const n=e.cle;let i=e.valeur;switch(e.type){case"number":i=parseFloat(i)||0;break;case"boolean":i="true"===i||"1"===i||"Oui"===i;break;case"json":try{i=JSON.parse(i)}catch(a){console.error(`Erreur lors du parsing JSON pour ${n}:`,a),i=null}}t[n]=i}),t}(Ai.csvToArray(t.settings,_i));this.setSettings(e)}return!0}catch(t){return console.error("Erreur lors de l'importation CSV:",t),!1}}parseCSVBackup(e){const t={},n=e.split("\n");let i="",a=[];for(const r of n)if(r.startsWith("=== ")&&r.endsWith(" ===")){i&&a.length>0&&(t[i]=a.join("\n"));switch(r.replace(/=== | ===/g,"").toLowerCase()){case"produits":i="products";break;case"utilisateurs":i="users";break;case"ventes":i="sales";break;case"dettes":i="debts";break;case"dépenses":i="expenses";break;case"paiements employés":i="employeePayments";break;case"paramètres":i="settings";break;default:i=""}a=[]}else i&&""!==r.trim()&&a.push(r);return i&&a.length>0&&(t[i]=a.join("\n")),t}exportCSV(e){let t=[],n=[];switch(e){case"products":t=this.getProducts(),n=Ii;break;case"users":t=this.getUsers(),n=Ni;break;case"sales":t=this.getSales(),n=Li;break;case"debts":t=this.getDebts(),n=Oi;break;case"expenses":t=this.getExpenses(),n=Vi;break;case"employee_payments":t=this.getEmployeePayments(),n=Bi}return Ai.arrayToCSV(t,n)}importCSV(e,t,n=!1){try{let i=[],a=[];switch(e){case"products":i=Ii,a=n?[]:this.getProducts();break;case"users":i=Ni,a=n?[]:this.getUsers();break;default:return{success:!1,message:"Type de données non supporté",errors:[]}}const r=Ai.csvToArray(t,i),s=Ai.validateCSVData(r,i);if(!s.isValid)return{success:!1,message:"Données invalides",errors:s.errors};let o=r;if(!n&&a.length>0){const e=new Set(a.map(e=>e.id)),t=r.filter(t=>!e.has(t.id));o=[...a,...t]}switch(e){case"products":this.setProducts(o);break;case"users":this.setUsers(o)}return{success:!0,message:`${r.length} éléments importés avec succès`,errors:[]}}catch(i){return{success:!1,message:"Erreur lors de l'importation: "+i.message,errors:[i.message]}}}},Wi=wi("Preferences",{web:()=>Si(()=>import("./web-D5ZgLrU1.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(e=>new e.PreferencesWeb)}),Xi=Object.freeze(Object.defineProperty({__proto__:null,Preferences:Wi},Symbol.toStringTag,{value:"Module"}));const Qi=new class{constructor(){n(this,"CSV_PREFIX","smartboutique_csv_")}async setCSV(e,t,n){try{const i=Ai.arrayToCSV(t,n);await Wi.set({key:this.CSV_PREFIX+e,value:i})}catch(i){throw console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i),i}}async getCSV(e,t,n=[]){try{const i=await Wi.get({key:this.CSV_PREFIX+e});return i.value?Ai.csvToArray(i.value,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}async getProducts(){return(await this.getCSV("products",Ii,[])).map(e=>{if(!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,!e.beneficeUnitaireCDF||isNaN(Number(e.beneficeUnitaireCDF))?e.beneficeUnitaireCDF=e.prixCDF-e.prixAchatCDF:e.beneficeUnitaireCDF=Number(e.beneficeUnitaireCDF),!e.beneficeUnitaireUSD||isNaN(Number(e.beneficeUnitaireUSD))){const t=2800;e.beneficeUnitaireUSD=Math.round(e.beneficeUnitaireCDF/t*100)/100}else e.beneficeUnitaireUSD=Number(e.beneficeUnitaireUSD);e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}async setProducts(e){await this.setCSV("products",e,Ii)}async getUsers(){return this.getCSV("users",Ni,[])}async setUsers(e){await this.setCSV("users",e,Ni)}async getSales(){return(await this.getCSV("sales",Li,[])).map(e=>(e.totalCDF=Number(e.totalCDF)||0,e.totalUSD=e.totalUSD?Number(e.totalUSD):void 0,e.produits=(e.produits||[]).map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})),e))}async setSales(e){await this.setCSV("sales",e,Li)}async getDebts(){return(await this.getCSV("debts",Oi,[])).map(e=>(e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0,e.paiements=(e.paiements||[]).map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})),e))}async setDebts(e){await this.setCSV("debts",e,Oi)}async getExpenses(){return this.getCSV("expenses",Vi,[])}async setExpenses(e){await this.setCSV("expenses",e,Vi)}async getEmployees(){return this.getCSV("employees",qi,[])}async setEmployees(e){await this.setCSV("employees",e,qi)}async addEmployee(e){const t=await this.getEmployees();t.push(e),await this.setEmployees(t)}async updateEmployee(e){const t=await this.getEmployees(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,await this.setEmployees(t))}async deleteEmployee(e){const t=(await this.getEmployees()).filter(t=>t.id!==e);await this.setEmployees(t)}async getSettings(){const e=await Wi.get({key:this.CSV_PREFIX+"settings"});if(e.value)try{return JSON.parse(e.value)}catch(t){console.error("Erreur lors du parsing des paramètres:",t)}return{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+243 000 000 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}}}async setSettings(e){await Wi.set({key:this.CSV_PREFIX+"settings",value:JSON.stringify(e)})}async getCurrentUser(){const e=await Wi.get({key:this.CSV_PREFIX+"currentUser"});return e.value?JSON.parse(e.value):null}async setCurrentUser(e){await Wi.set({key:this.CSV_PREFIX+"currentUser",value:JSON.stringify(e)})}async initializeDefaultData(){if(0===(await this.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await this.setUsers(e)}0===(await this.getProducts()).length&&await this.initializeProductCatalog();0===(await this.getDebts()).length&&await this.initializeSampleDebts()}async initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixCDF:224e4,prixUSD:800,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixCDF:98e3,prixUSD:35,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixCDF:33600,prixUSD:12,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixCDF:8400,prixUSD:3,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixCDF:42e3,prixUSD:15,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"11",nom:"Huile de Palme",description:"Huile de palme rouge 1 litre",prixCDF:16800,prixUSD:6,codeQR:"SB12345688OPQR",categorie:"Alimentation",stock:40,stockMin:8,codeBarres:"1234567890133",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"12",nom:"Farine de Maïs",description:"Farine de maïs blanche 2kg",prixCDF:11200,prixUSD:4,codeQR:"SB12345689STUV",categorie:"Alimentation",stock:75,stockMin:15,codeBarres:"1234567890134",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];await this.setProducts(e)}async clearAllData(){try{const e=["products","users","sales","debts","expenses","settings","currentUser"];for(const t of e)await Wi.remove({key:this.CSV_PREFIX+t});console.log("✅ All CSV data cleared from mobile storage")}catch(e){throw console.error("❌ Error clearing CSV data:",e),e}}async initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),a=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:a.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];await this.setDebts(r)}};let Ji=null,Hi=null,Yi=null;const Gi="undefined"==typeof window&&"undefined"!=typeof process&&(null==(e=process.versions)?void 0:e.electron);if(Gi)try{Ji=require("better-sqlite3"),Hi=require("electron").app,Yi=require("path").join}catch(Sr){console.warn("better-sqlite3 not available:",Sr)}class Ki{constructor(){if(n(this,"db"),n(this,"dbPath"),n(this,"isAvailable",!1),Gi&&Ji)try{const e=(null==Hi?void 0:Hi.getPath("userData"))||"./data";this.dbPath=Yi(e,"smartboutique.db"),this.initializeDatabase(),this.isAvailable=!0}catch(Sr){console.error("Failed to initialize SQLite:",Sr)}else console.warn("SQLite not available in this context")}initializeDatabase(){if(!Ji)throw new Error("better-sqlite3 not available");this.db=new Ji(this.dbPath),this.db.pragma("journal_mode = WAL"),this.db.pragma("synchronous = NORMAL"),this.db.pragma("cache_size = 1000"),this.db.pragma("temp_store = memory"),this.createTables(),this.createIndexes()}checkAvailability(){if(!this.isAvailable)throw new Error("SQLite storage not available in this context")}createTables(){this.db.exec("\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employees (\n        id TEXT PRIMARY KEY,\n        nomComplet TEXT NOT NULL,\n        poste TEXT NOT NULL,\n        salaireCDF REAL NOT NULL,\n        salaireUSD REAL,\n        dateEmbauche TEXT NOT NULL,\n        telephone TEXT,\n        adresse TEXT,\n        statut TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employee_payments (\n        id TEXT PRIMARY KEY,\n        nomEmploye TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        datePaiement TEXT NOT NULL,\n        methodePaiement TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    ")}createIndexes(){this.db.exec("\n      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);\n      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);\n      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);\n      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);\n      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);\n      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);\n      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);\n      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);\n    ")}getProducts(){this.checkAvailability();return this.db.prepare("SELECT * FROM products ORDER BY nom").all()}getProduct(e){this.checkAvailability();return this.db.prepare("SELECT * FROM products WHERE id = ?").get(e)}setProducts(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM products").run();const t=this.db.prepare("\n        INSERT INTO products (\n          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n          stockMin, codeBarres, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.description,n.prixAchatCDF,n.prixAchatUSD,n.prixCDF,n.prixUSD,n.beneficeUnitaireCDF,n.beneficeUnitaireUSD,n.codeQR,n.categorie,n.stock,n.stockMin,n.codeBarres,n.dateCreation,n.dateModification)})(e)}addProduct(e){this.db.prepare("\n      INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification, quantiteEnStock,\n        coutAchatStockCDF, coutAchatStockUSD, prixParPieceCDF, prixParPieceUSD\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification,e.quantiteEnStock,e.coutAchatStockCDF,e.coutAchatStockUSD,e.prixParPieceCDF,e.prixParPieceUSD)}updateProduct(e){this.db.prepare("\n      UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id)}deleteProduct(e){this.db.prepare("DELETE FROM products WHERE id = ?").run(e)}getUsers(){return this.db.prepare("SELECT * FROM users ORDER BY nom").all().map(e=>({...e,actif:Boolean(e.actif)}))}setUsers(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM users").run();const t=this.db.prepare("\n        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.email,n.role,n.motDePasse,n.dateCreation,n.actif?1:0)})(e)}getSales(){return this.db.prepare("SELECT * FROM sales ORDER BY date DESC").all().map(e=>({...e,produits:JSON.parse(e.produits)}))}setSales(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM sales").run();const t=this.db.prepare("\n        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.datevente,n.nomClient,JSON.stringify(n.produits),n.totalCDF,n.totalUSD,n.methodePaiement,n.typeVente,n.vendeur,n.numeroRecu)})(e)}getEmployeePayments(){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments ORDER BY datePaiement DESC").all()}getEmployeePayment(e){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments WHERE id = ?").get(e)}addEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employee_payments (\n        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n        notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.creePar,e.dateCreation,e.dateModification)}clearAllData(){this.checkAvailability();try{this.db.transaction(()=>{this.db.prepare("DELETE FROM products").run(),this.db.prepare("DELETE FROM users").run(),this.db.prepare("DELETE FROM sales").run(),this.db.prepare("DELETE FROM debts").run(),this.db.prepare("DELETE FROM expenses").run(),this.db.prepare("DELETE FROM employee_payments").run(),this.db.prepare("DELETE FROM settings").run(),this.db.prepare("DELETE FROM sqlite_sequence").run()})(),console.log("✅ All SQLite data cleared successfully")}catch(Sr){throw console.error("❌ Error clearing SQLite data:",Sr),Sr}}updateEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      UPDATE employee_payments SET\n        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,\n        methodePaiement = ?, notes = ?, dateModification = ?\n      WHERE id = ?\n    ").run(e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.dateModification,e.id)}deleteEmployeePayment(e){this.checkAvailability();this.db.prepare("DELETE FROM employee_payments WHERE id = ?").run(e)}setEmployeePayments(e){this.checkAvailability();this.db.transaction(e=>{this.db.prepare("DELETE FROM employee_payments").run();const t=this.db.prepare("\n        INSERT INTO employee_payments (\n          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n          notes, creePar, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nomEmploye,n.montantCDF,n.montantUSD,n.datePaiement,n.methodePaiement,n.notes,n.creePar,n.dateCreation,n.dateModification)})(e)}migrateFromCSV(e){console.log("Starting migration from CSV to SQLite...");this.db.transaction(()=>{var t,n,i;(null==(t=e.products)?void 0:t.length)&&this.setProducts(e.products),(null==(n=e.users)?void 0:n.length)&&this.setUsers(e.users),(null==(i=e.sales)?void 0:i.length)&&this.setSales(e.sales)})(),console.log("Migration completed successfully")}exportToCSV(){return{products:"",users:"",sales:""}}close(){this.db.close()}getStats(){const e=this.db.prepare("SELECT COUNT(*) as count FROM products").get(),t=this.db.prepare("SELECT COUNT(*) as count FROM users").get(),n=this.db.prepare("SELECT COUNT(*) as count FROM sales").get(),i=this.db.prepare("SELECT COUNT(*) as count FROM employee_payments").get();return{products:e.count,users:t.count,sales:n.count,employeePayments:i.count,dbSize:0}}getEmployees(){this.checkAvailability();return this.db.prepare("SELECT * FROM employees ORDER BY nom ASC").all()}addEmployee(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employees (\n        id, nomComplet, poste, salaireCDF, salaireUSD, dateEmbauche,\n        telephone, adresse, statut, notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.creePar,e.dateCreation,e.dateModification)}updateEmployee(e){this.checkAvailability();this.db.prepare("\n      UPDATE employees SET\n        nomComplet = ?, poste = ?, salaireCDF = ?, salaireUSD = ?,\n        dateEmbauche = ?, telephone = ?, adresse = ?, statut = ?, notes = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nomComplet,e.poste,e.salaireCDF,e.salaireUSD,e.dateEmbauche,e.telephone,e.adresse,e.statut,e.notes,e.dateModification,e.id)}deleteEmployee(e){this.checkAvailability();this.db.prepare("DELETE FROM employees WHERE id = ?").run(e)}}const Zi=new Ki,ea=Object.freeze(Object.defineProperty({__proto__:null,SQLiteStorageService:Ki,sqliteStorageService:Zi},Symbol.toStringTag,{value:"Module"}));class ta{constructor(e){this.sqlite=e,this._connectionDict=new Map}async initWebStore(){try{return await this.sqlite.initWebStore(),Promise.resolve()}catch(e){return Promise.reject(e)}}async saveToStore(e){try{return await this.sqlite.saveToStore({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async saveToLocalDisk(e){try{return await this.sqlite.saveToLocalDisk({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getFromLocalDiskToStore(e){const t=null==e||e;try{return await this.sqlite.getFromLocalDiskToStore({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async echo(e){try{const t=await this.sqlite.echo({value:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isSecretStored(){try{const e=await this.sqlite.isSecretStored();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async setEncryptionSecret(e){try{return await this.sqlite.setEncryptionSecret({passphrase:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async changeEncryptionSecret(e,t){try{return await this.sqlite.changeEncryptionSecret({passphrase:e,oldpassphrase:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async clearEncryptionSecret(){try{return await this.sqlite.clearEncryptionSecret(),Promise.resolve()}catch(e){return Promise.reject(e)}}async checkEncryptionSecret(e){try{const t=await this.sqlite.checkEncryptionSecret({passphrase:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async addUpgradeStatement(e,t){try{return e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.addUpgradeStatement({database:e,upgrade:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async createConnection(e,t,n,i,a){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.createConnection({database:e,encrypted:t,mode:n,version:i,readonly:a});const r=new na(e,a,this.sqlite),s=a?`RO_${e}`:`RW_${e}`;return this._connectionDict.set(s,r),Promise.resolve(r)}catch(r){return Promise.reject(r)}}async closeConnection(e,t){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.closeConnection({database:e,readonly:t});const n=t?`RO_${e}`:`RW_${e}`;return this._connectionDict.delete(n),Promise.resolve()}catch(n){return Promise.reject(n)}}async isConnection(e,t){const n={};e.endsWith(".db")&&(e=e.slice(0,-3));const i=t?`RO_${e}`:`RW_${e}`;return n.result=this._connectionDict.has(i),Promise.resolve(n)}async retrieveConnection(e,t){e.endsWith(".db")&&(e=e.slice(0,-3));const n=t?`RO_${e}`:`RW_${e}`;if(this._connectionDict.has(n)){const t=this._connectionDict.get(n);return void 0!==t?Promise.resolve(t):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async getNCDatabasePath(e,t){try{const n=await this.sqlite.getNCDatabasePath({path:e,database:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async createNCConnection(e,t){try{await this.sqlite.createNCConnection({databasePath:e,version:t});const n=new na(e,!0,this.sqlite),i=`RO_${e})`;return this._connectionDict.set(i,n),Promise.resolve(n)}catch(n){return Promise.reject(n)}}async closeNCConnection(e){try{await this.sqlite.closeNCConnection({databasePath:e});const t=`RO_${e})`;return this._connectionDict.delete(t),Promise.resolve()}catch(t){return Promise.reject(t)}}async isNCConnection(e){const t={},n=`RO_${e})`;return t.result=this._connectionDict.has(n),Promise.resolve(t)}async retrieveNCConnection(e){if(this._connectionDict.has(e)){const t=`RO_${e})`,n=this._connectionDict.get(t);return void 0!==n?Promise.resolve(n):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async isNCDatabase(e){try{const t=await this.sqlite.isNCDatabase({databasePath:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async retrieveAllConnections(){return this._connectionDict}async closeAllConnections(){const e=new Map;try{for(const t of this._connectionDict.keys()){const n=t.substring(3),i="RO_"===t.substring(0,3);await this.sqlite.closeConnection({database:n,readonly:i}),e.set(t,null)}for(const t of e.keys())this._connectionDict.delete(t);return Promise.resolve()}catch(t){return Promise.reject(t)}}async checkConnectionsConsistency(){try{const e=[...this._connectionDict.keys()],t=[],n=[];for(const a of e)t.push(a.substring(0,2)),n.push(a.substring(3));const i=await this.sqlite.checkConnectionsConsistency({dbNames:n,openModes:t});return i.result||(this._connectionDict=new Map),Promise.resolve(i)}catch(e){return this._connectionDict=new Map,Promise.reject(e)}}async importFromJson(e){try{const t=await this.sqlite.importFromJson({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isJsonValid(e){try{const t=await this.sqlite.isJsonValid({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async copyFromAssets(e){const t=null==e||e;try{return await this.sqlite.copyFromAssets({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async getFromHTTPRequest(e,t){const n=null==t||t;try{return await this.sqlite.getFromHTTPRequest({url:e,overwrite:n}),Promise.resolve()}catch(i){return Promise.reject(i)}}async isDatabaseEncrypted(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabaseEncrypted({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isInConfigEncryption(){try{const e=await this.sqlite.isInConfigEncryption();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isInConfigBiometricAuth(){try{const e=await this.sqlite.isInConfigBiometricAuth();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isDatabase(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabase({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async getDatabaseList(){try{const e=(await this.sqlite.getDatabaseList()).values;e.sort();const t={values:e};return Promise.resolve(t)}catch(e){return Promise.reject(e)}}async getMigratableDbList(e){const t=e||"default";try{const e=await this.sqlite.getMigratableDbList({folderPath:t});return Promise.resolve(e)}catch(n){return Promise.reject(n)}}async addSQLiteSuffix(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.addSQLiteSuffix({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(a){return Promise.reject(a)}}async deleteOldDatabases(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.deleteOldDatabases({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(a){return Promise.reject(a)}}async moveDatabasesAndAddSuffix(e,t){const n=e||"default",i=t||[];return this.sqlite.moveDatabasesAndAddSuffix({folderPath:n,dbNameList:i})}}class na{constructor(e,t,n){this.dbName=e,this.readonly=t,this.sqlite=n}getConnectionDBName(){return this.dbName}getConnectionReadOnly(){return this.readonly}async open(){try{return await this.sqlite.open({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async close(){try{return await this.sqlite.close({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async beginTransaction(){try{const e=await this.sqlite.beginTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async commitTransaction(){try{const e=await this.sqlite.commitTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async rollbackTransaction(){try{const e=await this.sqlite.rollbackTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTransactionActive(){try{const e=await this.sqlite.isTransactionActive({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async loadExtension(e){try{return await this.sqlite.loadExtension({database:this.dbName,path:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async enableLoadExtension(e){try{return await this.sqlite.enableLoadExtension({database:this.dbName,toggle:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getUrl(){try{const e=await this.sqlite.getUrl({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getVersion(){try{const e=await this.sqlite.getVersion({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getTableList(){try{const e=await this.sqlite.getTableList({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async execute(e,t=!0,n=!0){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const i=await this.sqlite.execute({database:this.dbName,statements:e,transaction:t,readonly:!1,isSQL92:n});return Promise.resolve(i)}}catch(i){return Promise.reject(i)}}async query(e,t,n=!0){let i;try{return i=t&&t.length>0?await this.sqlite.query({database:this.dbName,statement:e,values:t,readonly:this.readonly,isSQL92:!0}):await this.sqlite.query({database:this.dbName,statement:e,values:[],readonly:this.readonly,isSQL92:n}),i=await this.reorderRows(i),Promise.resolve(i)}catch(a){return Promise.reject(a)}}async run(e,t,n=!0,i="no",a=!0){let r;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(r=t&&t.length>0?await this.sqlite.run({database:this.dbName,statement:e,values:t,transaction:n,readonly:!1,returnMode:i,isSQL92:!0}):await this.sqlite.run({database:this.dbName,statement:e,values:[],transaction:n,readonly:!1,returnMode:i,isSQL92:a}),r.changes=await this.reorderRows(r.changes),Promise.resolve(r))}catch(s){return Promise.reject(s)}}async executeSet(e,t=!0,n="no",i=!0){let a;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(a=await this.sqlite.executeSet({database:this.dbName,set:e,transaction:t,readonly:!1,returnMode:n,isSQL92:i}),a.changes=await this.reorderRows(a.changes),Promise.resolve(a))}catch(r){return Promise.reject(r)}}async isExists(){try{const e=await this.sqlite.isDBExists({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTable(e){try{const t=await this.sqlite.isTableExists({database:this.dbName,table:e,readonly:this.readonly});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isDBOpen(){try{const e=await this.sqlite.isDBOpen({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async delete(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteDatabase({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async createSyncTable(){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const e=await this.sqlite.createSyncTable({database:this.dbName,readonly:!1});return Promise.resolve(e)}}catch(e){return Promise.reject(e)}}async setSyncDate(e){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.setSyncDate({database:this.dbName,syncdate:e,readonly:!1}),Promise.resolve())}catch(t){return Promise.reject(t)}}async getSyncDate(){try{const e=await this.sqlite.getSyncDate({database:this.dbName,readonly:this.readonly});let t="";return e.syncDate>0&&(t=new Date(1e3*e.syncDate).toISOString()),Promise.resolve(t)}catch(e){return Promise.reject(e)}}async exportToJson(e,t=!1){try{const n=await this.sqlite.exportToJson({database:this.dbName,jsonexportmode:e,readonly:this.readonly,encrypted:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async deleteExportedRows(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteExportedRows({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async executeTransaction(e,t=!0){let n=0,i=!1;if(this.readonly)return Promise.reject("not allowed in read-only mode");if(await this.sqlite.beginTransaction({database:this.dbName}),i=await this.sqlite.isTransactionActive({database:this.dbName}),!i)return Promise.reject("After Begin Transaction, no transaction active");try{for(const a of e){if("object"!=typeof a||!("statement"in a))throw new Error("Error a task.statement must be provided");if("values"in a&&a.values&&a.values.length>0){const e=a.statement.toUpperCase().includes("RETURNING")?"all":"no",i=await this.sqlite.run({database:this.dbName,statement:a.statement,values:a.values,transaction:!1,readonly:!1,returnMode:e,isSQL92:t});if(i.changes.changes<0)throw new Error("Error in transaction method run ");n+=i.changes.changes}else{const e=await this.sqlite.execute({database:this.dbName,statements:a.statement,transaction:!1,readonly:!1});if(e.changes.changes<0)throw new Error("Error in transaction method execute ");n+=e.changes.changes}}n+=(await this.sqlite.commitTransaction({database:this.dbName})).changes.changes;const i={changes:{changes:n}};return Promise.resolve(i)}catch(a){const e=a.message?a.message:a;return await this.sqlite.rollbackTransaction({database:this.dbName}),Promise.reject(e)}}async reorderRows(e){const t=e;if((null==e?void 0:e.values)&&"object"==typeof e.values[0]&&Object.keys(e.values[0]).includes("ios_columns")){const n=e.values[0].ios_columns,i=[];for(let t=1;t<e.values.length;t++){const a=e.values[t],r={};for(const e of n)r[e]=a[e];i.push(r)}t.values=i}return Promise.resolve(t)}}const ia=wi("CapacitorSQLite",{web:()=>Si(()=>import("./web-D1u89MI_.js"),__vite__mapDeps([5,1,2,3,4]),import.meta.url).then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});class aa{constructor(){n(this,"sqlite"),n(this,"db",null),n(this,"DB_NAME","smartboutique.db"),n(this,"DB_VERSION",1),n(this,"isInitialized",!1),this.sqlite=new ta(ia)}async initialize(){if(!this.isInitialized)try{if(console.log("Initializing mobile SQLite database..."),!fi.isNativePlatform())throw new Error("SQLite is only supported on native platforms (Android/iOS)");const e=await this.sqlite.checkConnectionsConsistency(),t=(await this.sqlite.isConnection(this.DB_NAME,!1)).result;e.result&&t?this.db=await this.sqlite.retrieveConnection(this.DB_NAME,!1):this.db=await this.sqlite.createConnection(this.DB_NAME,!1,"no-encryption",this.DB_VERSION,!1),await this.db.open(),await this.createTables(),await this.createIndexes(),this.isInitialized=!0,console.log("Mobile SQLite database initialized successfully")}catch(Sr){throw console.error("Failed to initialize mobile SQLite database:",Sr),Sr}}async createTables(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT,\n        quantiteEnStock INTEGER,\n        coutAchatStockCDF REAL,\n        coutAchatStockUSD REAL,\n        prixParPieceCDF REAL,\n        prixParPieceUSD REAL\n      );","CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      );","CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      );","CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      );"];for(const t of e)await this.db.execute(t)}async createIndexes(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);","CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);","CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);","CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);","CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);","CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);","CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);","CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);"];for(const t of e)await this.db.execute(t)}async ensureInitialized(){this.isInitialized||await this.initialize()}async getProducts(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM products ORDER BY nom")).values}async getProduct(e){var t;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return null==(t=(await this.db.query("SELECT * FROM products WHERE id = ?",[e])).values)?void 0:t[0]}async setProducts(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM products");for(const t of e)await this.db.run("INSERT INTO products (\n            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n            stockMin, codeBarres, dateCreation, dateModification\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.description,t.prixAchatCDF,t.prixAchatUSD,t.prixCDF,t.prixUSD,t.beneficeUnitaireCDF,t.beneficeUnitaireUSD,t.codeQR,t.categorie,t.stock,t.stockMin,t.codeBarres,t.dateCreation,t.dateModification]);await this.db.commitTransaction()}catch(Sr){throw await this.db.rollbackTransaction(),Sr}}async addProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification])}async updateProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?",[e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id])}async deleteProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("DELETE FROM products WHERE id = ?",[e])}async getUsers(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM users ORDER BY nom")).values.map(e=>({...e,actif:Boolean(e.actif)}))}async setUsers(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM users");for(const t of e)await this.db.run("INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.email,t.role,t.motDePasse,t.dateCreation,t.actif?1:0]);await this.db.commitTransaction()}catch(Sr){throw await this.db.rollbackTransaction(),Sr}}async getSales(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM sales ORDER BY date DESC")).values.map(e=>({...e,produits:JSON.parse(e.produits),datevente:e.date,nomClient:e.client,methodePaiement:e.typePaiement}))}async setSales(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM sales");for(const t of e)await this.db.run("INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.datevente,t.nomClient,JSON.stringify(t.produits),t.totalCDF,t.totalUSD,t.methodePaiement,t.typeVente,t.vendeur,t.numeroRecu]);await this.db.commitTransaction()}catch(Sr){throw await this.db.rollbackTransaction(),Sr}}async close(){this.db&&(await this.db.close(),await this.sqlite.closeConnection(this.DB_NAME,!1),this.db=null,this.isInitialized=!1)}async getStats(){var e,t,n,i,a,r;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");const[s,o,l]=await Promise.all([this.db.query("SELECT COUNT(*) as count FROM products"),this.db.query("SELECT COUNT(*) as count FROM users"),this.db.query("SELECT COUNT(*) as count FROM sales")]);return{products:(null==(t=null==(e=s.values)?void 0:e[0])?void 0:t.count)||0,users:(null==(i=null==(n=o.values)?void 0:n[0])?void 0:i.count)||0,sales:(null==(r=null==(a=l.values)?void 0:a[0])?void 0:r.count)||0,dbSize:0}}async clearAllData(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");try{await this.db.execute("DELETE FROM products"),await this.db.execute("DELETE FROM users"),await this.db.execute("DELETE FROM sales"),await this.db.execute("DELETE FROM debts"),await this.db.execute("DELETE FROM expenses"),await this.db.execute("DELETE FROM settings"),await this.db.execute("DELETE FROM sqlite_sequence"),console.log("✅ All mobile SQLite data cleared successfully")}catch(Sr){throw console.error("❌ Error clearing mobile SQLite data:",Sr),Sr}}}const ra=new aa,sa=Object.freeze(Object.defineProperty({__proto__:null,MobileSQLiteStorageService:aa,mobileSQLiteStorageService:ra},Symbol.toStringTag,{value:"Module"}));const oa=new class{constructor(){n(this,"MIGRATION_FLAG_KEY","smartboutique_mobile_migrated_to_sqlite")}async isMigrationCompleted(){return"true"===(await Wi.get({key:this.MIGRATION_FLAG_KEY})).value}async markMigrationCompleted(){await Wi.set({key:this.MIGRATION_FLAG_KEY,value:"true"})}async migrateToSQLite(){const e={success:!1,message:"",migratedCounts:{products:0,users:0,sales:0,debts:0,expenses:0},errors:[]};try{if(console.log("Starting mobile migration from CSV to SQLite..."),await this.isMigrationCompleted())return e.success=!0,e.message="Migration mobile déjà effectuée",e;await ra.initialize();const t=await this.extractCSVData();if(!this.validateCSVData(t))return e.errors.push("Données CSV mobiles invalides ou corrompues"),e.message="Échec de la validation des données CSV mobiles",e;await this.createBackup(t),await this.performMigration(t,e),await this.verifyMigration(t,e)?(await this.markMigrationCompleted(),e.success=!0,e.message=`Migration mobile réussie: ${e.migratedCounts.products} produits, ${e.migratedCounts.users} utilisateurs, ${e.migratedCounts.sales} ventes migrées`):e.message="Échec de la vérification de la migration mobile"}catch(Sr){console.error("Mobile migration error:",Sr),e.errors.push(`Erreur de migration mobile: ${Sr.message}`),e.message="Échec de la migration mobile"}return e}async extractCSVData(){return console.log("Extracting data from CSV Capacitor Preferences..."),{products:await Qi.getProducts()||[],users:await Qi.getUsers()||[],sales:await Qi.getSales()||[],debts:await Qi.getDebts()||[],expenses:await Qi.getExpenses()||[]}}validateCSVData(e){try{if(!e||"object"!=typeof e)return!1;const{products:t,users:n,sales:i,debts:a,expenses:r}=e;if(t&&Array.isArray(t))for(const e of t)if(!e.id||!e.nom||"number"!=typeof e.prixCDF)return console.warn("Invalid mobile product found:",e),!1;if(n&&Array.isArray(n))for(const e of n)if(!(e.id&&e.nom&&e.email&&e.role))return console.warn("Invalid mobile user found:",e),!1;if(i&&Array.isArray(i))for(const e of i)if(!e.id||!e.datevente||!Array.isArray(e.produits))return console.warn("Invalid mobile sale found:",e),!1;return!0}catch(Sr){return console.error("Mobile data validation error:",Sr),!1}}async createBackup(e){try{const t={timestamp:(new Date).toISOString(),platform:"mobile",data:e};await Wi.set({key:"smartboutique_mobile_csv_backup",value:JSON.stringify(t)}),console.log("Mobile backup created successfully")}catch(Sr){throw console.error("Mobile backup creation failed:",Sr),new Error("Impossible de créer une sauvegarde mobile")}}async performMigration(e,t){try{const{products:n,users:i,sales:a,debts:r,expenses:s}=e;n&&n.length>0&&(await ra.setProducts(n),t.migratedCounts.products=n.length,console.log(`Migrated ${n.length} mobile products`)),i&&i.length>0&&(await ra.setUsers(i),t.migratedCounts.users=i.length,console.log(`Migrated ${i.length} mobile users`)),a&&a.length>0&&(await ra.setSales(a),t.migratedCounts.sales=a.length,console.log(`Migrated ${a.length} mobile sales`)),console.log("Mobile migration to SQLite completed")}catch(Sr){throw console.error("Mobile migration execution failed:",Sr),new Error(`Échec de la migration mobile: ${Sr.message}`)}}async verifyMigration(e,t){try{const n=await ra.getStats();return e.products&&e.products.length!==n.products?(t.errors.push(`Nombre de produits mobile incorrect: attendu ${e.products.length}, trouvé ${n.products}`),!1):e.users&&e.users.length!==n.users?(t.errors.push(`Nombre d'utilisateurs mobile incorrect: attendu ${e.users.length}, trouvé ${n.users}`),!1):e.sales&&e.sales.length!==n.sales?(t.errors.push(`Nombre de ventes mobile incorrect: attendu ${e.sales.length}, trouvé ${n.sales}`),!1):(console.log("Mobile migration verification successful"),!0)}catch(Sr){return console.error("Mobile migration verification failed:",Sr),t.errors.push(`Échec de la vérification mobile: ${Sr.message}`),!1}}async rollbackMigration(){try{console.log("Rolling back mobile migration...");const e=await Wi.get({key:"smartboutique_mobile_csv_backup"});if(!e.value)return console.error("No mobile backup found for rollback"),!1;const t=JSON.parse(e.value),{data:n}=t;return n.products&&await Qi.setProducts(n.products),n.users&&await Qi.setUsers(n.users),n.sales&&await Qi.setSales(n.sales),n.debts&&await Qi.setDebts(n.debts),n.expenses&&await Qi.setExpenses(n.expenses),await Wi.remove({key:this.MIGRATION_FLAG_KEY}),await ra.close(),console.log("Mobile migration rollback completed"),!0}catch(Sr){return console.error("Mobile rollback failed:",Sr),!1}}async getMigrationStatus(){const e=await this.isMigrationCompleted(),t=await Wi.get({key:"smartboutique_mobile_csv_backup"}),n=await Wi.get({key:"smartboutique_csv_products"});return{isCompleted:e,sqliteStats:e?await ra.getStats():void 0,csvDataExists:Boolean(n.value),backupExists:Boolean(t.value)}}async cleanupOldData(){if(!(await this.isMigrationCompleted()))return void console.warn("Cannot cleanup mobile data: migration not completed");const e=["smartboutique_csv_products","smartboutique_csv_users","smartboutique_csv_sales","smartboutique_csv_debts","smartboutique_csv_expenses","smartboutique_csv_settings"];for(const t of e)await Wi.remove({key:t});console.log("Old mobile CSV data cleaned up")}async forceMigration(){return await Wi.remove({key:this.MIGRATION_FLAG_KEY}),this.migrateToSQLite()}};const la=new class{constructor(){n(this,"migrationChecked",!1),n(this,"mobileMigrationChecked",!1)}get storage(){return Mi()?Qi:$i}get sqliteStorage(){return Mi()?ra:Zi}async checkDesktopMigration(){if(!this.migrationChecked&&!Mi()){this.migrationChecked=!0;try{return void console.log("SQLite migration temporarily disabled - using CSV storage")}catch(Sr){console.error("Desktop migration check failed:",Sr)}}}async checkMobileMigration(){if(!this.mobileMigrationChecked&&Mi()){this.mobileMigrationChecked=!0;try{if(!(await oa.isMigrationCompleted())){console.log("Starting automatic mobile migration to SQLite...");const e=await oa.migrateToSQLite();e.success?console.log("Mobile migration completed successfully:",e.message):(console.error("Mobile migration failed:",e.message,e.errors),console.log("Falling back to CSV storage on mobile"))}}catch(Sr){console.error("Mobile migration check failed:",Sr),console.log("Falling back to CSV storage on mobile due to error")}}}async checkMigration(){Mi()?await this.checkMobileMigration():await this.checkDesktopMigration()}async set(e,t){Mi()?console.warn("Generic set method not available in CSV storage"):$i.set(e,t)}async get(e,t){return Mi()?(console.warn("Generic get method not available in CSV storage"),t):$i.get(e,t)}async remove(e){Mi()?console.warn("Generic remove method not available in CSV storage"):$i.remove(e)}async clear(){Mi()?await this.clearMobileData():await this.clearDesktopData()}async clearMobileData(){try{await Qi.clearAllData();const{Preferences:e}=await Si(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Xi);return{Preferences:e}},void 0,import.meta.url),{keys:t}=await e.keys(),n=t.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const i of n)await e.remove({key:i});try{await oa.isMigrationCompleted()&&await this.sqliteStorage.clearAllData()}catch(Sr){console.warn("Mobile SQLite clear failed or not available:",Sr)}console.log("✅ Mobile data cleared successfully")}catch(Sr){throw console.error("❌ Error clearing mobile data:",Sr),Sr}}async clearDesktopData(){try{$i.clear();try{Zi.clearAllData()}catch(Sr){console.warn("Desktop SQLite clear failed or not available:",Sr)}console.log("✅ Desktop data cleared successfully")}catch(Sr){throw console.error("❌ Error clearing desktop data:",Sr),Sr}}async getUsers(){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return await this.sqliteStorage.getUsers()}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}return await Qi.getUsers()}return $i.getUsers()}async setUsers(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.setUsers(e))}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}await Qi.setUsers(e)}else $i.setUsers(e)}async getProducts(){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return await this.sqliteStorage.getProducts()}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}return await Qi.getProducts()}return $i.getProducts()}async setProducts(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.setProducts(e))}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}await Qi.setProducts(e)}else $i.setProducts(e)}async getSales(){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return await this.sqliteStorage.getSales()}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}return await Qi.getSales()}return $i.getSales()}async setSales(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.setSales(e))}catch(Sr){console.warn("Mobile SQLite failed, falling back to CSV:",Sr)}await Qi.setSales(e)}else $i.setSales(e)}async getDebts(){return Mi()?await Qi.getDebts():$i.getDebts()}async setDebts(e){Mi()?await Qi.setDebts(e):$i.setDebts(e)}async getDettes(){return this.getDebts()}async setDettes(e){await this.setDebts(e)}async getCreances(){return this.getDebts()}async setCreances(e){await this.setDebts(e)}async getExpenses(){return Mi()?await Qi.getExpenses():$i.getExpenses()}async setExpenses(e){Mi()?await Qi.setExpenses(e):$i.setExpenses(e)}async getEmployeePayments(){var e,t;if(Mi())return await(null==(e=Qi.getEmployeePayments)?void 0:e.call(Qi))||[];try{return Zi.getEmployeePayments()}catch(Sr){return console.warn("SQLite not available for employee payments, using localStorage:",Sr),(null==(t=$i.getEmployeePayments)?void 0:t.call($i))||[]}}async addEmployeePayment(e){if(Mi())Qi.addEmployeePayment&&await Qi.addEmployeePayment(e);else try{Zi.addEmployeePayment(e)}catch(Sr){console.warn("SQLite not available for employee payments, using localStorage:",Sr),$i.addEmployeePayment&&$i.addEmployeePayment(e)}}async updateEmployeePayment(e){if(Mi())Qi.updateEmployeePayment&&await Qi.updateEmployeePayment(e);else try{Zi.updateEmployeePayment(e)}catch(Sr){console.warn("SQLite not available for employee payments, using localStorage:",Sr),$i.updateEmployeePayment&&$i.updateEmployeePayment(e)}}async deleteEmployeePayment(e){if(Mi())Qi.deleteEmployeePayment&&await Qi.deleteEmployeePayment(e);else try{Zi.deleteEmployeePayment(e)}catch(Sr){console.warn("SQLite not available for employee payments, using localStorage:",Sr),$i.deleteEmployeePayment&&$i.deleteEmployeePayment(e)}}async getSettings(){return Mi()?await Qi.getSettings():$i.getSettings()}async setSettings(e){Mi()?await Qi.setSettings(e):$i.setSettings(e)}async getCurrentUser(){return Mi()?await Qi.getCurrentUser():$i.getCurrentUser()}async setCurrentUser(e){Mi()?await Qi.setCurrentUser(e):$i.setCurrentUser(e)}async initializeDefaultData(){console.log("🔄 Initializing fresh demo data..."),Mi()?await Qi.initializeDefaultData():$i.initializeDefaultData();0===(await this.getDebts()).length&&await this.forceInitializeDebts();0===(await this.getSales()).length&&await this.initializeSampleSales();0===(await this.getExpenses()).length&&await this.initializeSampleExpenses(),console.log("✅ Fresh demo data initialized successfully")}async initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),i=[{id:"1",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant - Client satisfait"},{id:"2",numeroRecu:"RV-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",datevente:n.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70}],totalCDF:196e3,totalUSD:70,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement Mobile Money - Airtel"}];await this.setSales(i),console.log("✅ Sample sales data initialized")}async initializeSampleExpenses(){const e=new Date,t=new Date(e.getTime()-6048e5),n=[{id:"1",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0001",dateDepense:t.toISOString(),description:"Achat de fournitures de bureau",montantCDF:28e4,montantUSD:100,categorie:"Fournitures",methodePaiement:"cash",creePar:"Super Admin",notes:"Papier, stylos, et autres fournitures",dateCreation:t.toISOString(),dateModification:t.toISOString()},{id:"2",numeroRecu:"EX-"+e.toISOString().slice(0,10).replace(/-/g,"")+"-0002",dateDepense:e.toISOString(),description:"Frais de transport - Livraison",montantCDF:14e4,montantUSD:50,categorie:"Transport",methodePaiement:"mobile_money",creePar:"Gestionnaire",notes:"Transport pour livraison clients",dateCreation:e.toISOString(),dateModification:e.toISOString()}];await this.setExpenses(n),console.log("✅ Sample expenses data initialized")}async forceInitializeDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()+2592e6),i=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 823 456 789",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:75e3,montantTotalUSD:26.79,montantPayeCDF:25e3,montantPayeUSD:8.93,montantRestantCDF:5e4,montantRestantUSD:17.86,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:25e3,montantUSD:8.93,methodePaiement:"cash",datePaiement:e.toISOString(),notes:"Paiement partiel"}],notes:"Dette partiellement payée"}];await this.setDebts(i)}async exportData(){if(Mi()){const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>lr);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();return t.data?{csvData:t.data,exportDate:(new Date).toISOString()}:{}}return $i.exportData()}async importData(e){if(Mi()){if(e.csvData&&"string"==typeof e.csvData){const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>lr);return{csvImportExportService:e}},void 0,import.meta.url);return console.log("CSV import for mobile needs full implementation"),!1}return $i.importData(e)}return $i.importData(e)}async migrateFromDesktop(){if(!Mi())return console.warn("Migration should only be called on mobile platform"),!1;try{if((await Qi.getUsers()).length>0)return console.log("CSV storage already has data, skipping migration"),!0;const e={users:localStorage.getItem("smartboutique_users"),products:localStorage.getItem("smartboutique_products"),sales:localStorage.getItem("smartboutique_sales"),debts:localStorage.getItem("smartboutique_debts"),expenses:localStorage.getItem("smartboutique_expenses"),settings:localStorage.getItem("smartboutique_settings"),currentUser:localStorage.getItem("smartboutique_currentUser")};let t=!1;return e.users&&(await Qi.setUsers(JSON.parse(e.users)),t=!0),e.products&&(await Qi.setProducts(JSON.parse(e.products)),t=!0),e.sales&&(await Qi.setSales(JSON.parse(e.sales)),t=!0),e.debts&&(await Qi.setDebts(JSON.parse(e.debts)),t=!0),e.expenses&&(await Qi.setExpenses(JSON.parse(e.expenses)),t=!0),e.settings&&(await Qi.setSettings(JSON.parse(e.settings)),t=!0),e.currentUser&&(await Qi.setCurrentUser(JSON.parse(e.currentUser)),t=!0),t?console.log("Successfully migrated data from desktop to mobile"):console.log("No desktop data found to migrate"),!0}catch(Sr){return console.error("Error during migration:",Sr),!1}}async getEmployees(){await this.checkMigration();let e=[];if(Mi()){try{await oa.isMigrationCompleted()&&(e=await this.sqliteStorage.getEmployees())}catch(Sr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",Sr)}0===e.length&&(e=Qi.getEmployees?await Qi.getEmployees():[])}else try{e=Zi.getEmployees()}catch(Sr){console.warn("SQLite not available for employees, falling back to localStorage storage:",Sr),e=$i.getEmployees?$i.getEmployees():[]}return this.migrateEmployeeStructure(e)}migrateEmployeeStructure(e){return e.map(e=>{if(e.nom&&!e.nomComplet){const t=e.prenom?`${e.prenom} ${e.nom}`:e.nom;return{...e,nomComplet:t,nom:void 0,prenom:void 0}}return e})}async addEmployee(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.addEmployee(e))}catch(Sr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",Sr)}if(!Qi.addEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Qi.addEmployee(e)}else try{Zi.addEmployee(e)}catch(Sr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",Sr),!$i.addEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");$i.addEmployee(e)}}async updateEmployee(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.updateEmployee(e))}catch(Sr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",Sr)}if(!Qi.updateEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Qi.updateEmployee(e)}else try{Zi.updateEmployee(e)}catch(Sr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",Sr),!$i.updateEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");$i.updateEmployee(e)}}async deleteEmployee(e){if(await this.checkMigration(),Mi()){try{if(await oa.isMigrationCompleted())return void(await this.sqliteStorage.deleteEmployee(e))}catch(Sr){console.warn("Mobile SQLite failed for employees, falling back to CSV:",Sr)}if(!Qi.deleteEmployee)throw new Error("Employee management not yet implemented for mobile CSV storage");await Qi.deleteEmployee(e)}else try{Zi.deleteEmployee(e)}catch(Sr){if(console.warn("SQLite not available for employees, falling back to localStorage storage:",Sr),!$i.deleteEmployee)throw new Error("Neither SQLite nor localStorage storage available for employees");$i.deleteEmployee(e)}}};const ca=new class{constructor(){n(this,"currentUser",null),n(this,"initialized",!1)}async initialize(){this.initialized||(this.currentUser=await la.getCurrentUser(),this.initialized=!0)}async login(e,t){await this.initialize();const n=(await la.getUsers()).find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,await la.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}async logout(){this.currentUser=null,await la.remove("currentUser")}getCurrentUser(){return this.currentUser}async getCurrentUserAsync(){return await this.initialize(),this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;case"/employee-payments":return t.canViewEmployeePayments;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewEmployeePayments&&t.push({label:"Paiements Employés",path:"/employee-payments",icon:"Payment"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}async updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=await la.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,await la.setUsers(t),this.currentUser=i,await la.setCurrentUser(i),{success:!0}}catch(Sr){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}async changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}},da=({currentUser:e,onLogout:t})=>{var n,F;const[U,T]=vt.useState(!1),k=Jn(),R=Xn(),M=e?ca.getUserPermissions():null,A=[{label:"Inventaire",path:"/products",icon:i.jsx(S,{}),permission:"canViewProducts"},{label:"Ventes",path:"/sales",icon:i.jsx(C,{}),permission:"canViewSales"},{label:"Dettes",path:"/debts",icon:i.jsx(D,{}),permission:"canViewDebts"},{label:"Plus",path:"/more",icon:i.jsx(v,{}),permission:null}],I=[{label:"Tableau de bord",path:"/dashboard",icon:i.jsx(b,{}),permission:"canViewDashboard"},{label:"Dépenses",path:"/expenses",icon:i.jsx(f,{}),permission:"canViewExpenses"},{label:"Rapports",path:"/reports",icon:i.jsx(w,{}),permission:"canViewReports"},{label:"Utilisateurs",path:"/users",icon:i.jsx(E,{}),permission:"canViewUsers"},{label:"Paramètres",path:"/settings",icon:i.jsx(P,{}),permission:"canViewSettings"}],N=e=>!e||!M||M[e];return i.jsxs(a,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[i.jsx(r,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:i.jsxs(s,{children:[i.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(l,{sx:{bgcolor:"secondary.main",width:32,height:32},children:(null==(n=null==e?void 0:e.nom)?void 0:n.charAt(0))||"U"})]})}),i.jsx(a,{component:"main",sx:{flexGrow:1,pt:8,pb:7,overflow:"auto"},children:i.jsx(si,{})}),i.jsx(c,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:3,children:i.jsx(d,{value:(()=>{const e=R.pathname;return A.find(t=>t.path===e)?e:"/more"})(),onChange:(e,t)=>{var n;"/more"===(n=t)?T(!0):k(n)},showLabels:!0,children:A.map(e=>N(e.permission)&&i.jsx(u,{label:e.label,value:e.path,icon:e.icon},e.path))})}),i.jsxs(m,{anchor:"right",open:U,onClose:()=>T(!1),PaperProps:{sx:{width:280}},children:[i.jsx(s,{}),i.jsx(a,{sx:{p:2},children:i.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:2},children:[i.jsx(l,{sx:{bgcolor:"primary.main",mr:2},children:(null==(F=null==e?void 0:e.nom)?void 0:F.charAt(0))||"U"}),i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle1",fontWeight:"bold",children:(null==e?void 0:e.nom)||"Utilisateur"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:(null==e?void 0:e.role)||"Rôle"})]})]})}),i.jsx(h,{}),i.jsxs(p,{children:[I.map(e=>N(e.permission)&&i.jsxs(x,{onClick:()=>{return t=e.path,k(t),void T(!1);var t},sx:{cursor:"pointer"},children:[i.jsx(g,{children:e.icon}),i.jsx(y,{primary:e.label})]},e.path)),i.jsx(h,{sx:{my:1}}),i.jsxs(x,{onClick:()=>{t(),T(!1)},sx:{cursor:"pointer"},children:[i.jsx(g,{children:i.jsx(j,{})}),i.jsx(y,{primary:"Déconnexion"})]})]})]})]})},ua={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},ma={date:wt({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:wt({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:wt({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},ha={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},pa=["MMM","MMMM"],xa={code:"fr",formatDistance:(e,t,n)=>{let i;const a=ua[e];return i="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"dans "+i:"il y a "+i:i},formatLong:ma,formatRelative:(e,t,n,i)=>ha[e],localize:{preprocessor:(e,t)=>{if(1===e.getDate())return t;return t.some(e=>e.isToken&&pa.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t},ordinalNumber:(e,t)=>{const n=Number(e),i=null==t?void 0:t.unit;if(0===n)return"0";let a;return a=1===n?i&&["year","week","hour","minute","second"].includes(i)?"ère":"er":"ème",n+a},era:Et({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},defaultWidth:"wide"}),quarter:Et({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:Et({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},defaultWidth:"wide"}),day:Et({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:Et({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})},match:{ordinalNumber:Ft({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:Pt({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:Pt({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Pt({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Pt({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Pt({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};const ga=new class{constructor(){n(this,"currentUser",null),this.currentUser=$i.getCurrentUser()}login(e,t){const n=$i.getUsers().find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,$i.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}logout(){this.currentUser=null,$i.remove("currentUser")}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=$i.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,$i.setUsers(t),this.currentUser=i,$i.setCurrentUser(i),{success:!0}}catch(Sr){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}};const ya=new class{constructor(){n(this,"STORAGE_KEY","notifications"),n(this,"LOW_STOCK_THRESHOLD",5),n(this,"notifications",[]),n(this,"listeners",[]),this.loadNotifications()}loadNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);this.notifications=e?JSON.parse(e):[]}catch(Sr){console.error("Erreur lors du chargement des notifications:",Sr),this.notifications=[]}}saveNotifications(){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.notifications)),this.notifyListeners()}catch(Sr){console.error("Erreur lors de la sauvegarde des notifications:",Sr)}}notifyListeners(){this.listeners.forEach(e=>e(this.notifications))}subscribe(e){return this.listeners.push(e),e(this.notifications),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}getNotifications(){return[...this.notifications]}getUnreadCount(){return this.notifications.filter(e=>!e.lu).length}markAsRead(e){const t=this.notifications.find(t=>t.id===e);t&&!t.lu&&(t.lu=!0,this.saveNotifications())}markAllAsRead(){let e=!1;this.notifications.forEach(t=>{t.lu||(t.lu=!0,e=!0)}),e&&this.saveNotifications()}deleteNotification(e){const t=this.notifications.findIndex(t=>t.id===e);t>-1&&(this.notifications.splice(t,1),this.saveNotifications())}clearAll(){this.notifications=[],this.saveNotifications()}shouldReceiveStockNotifications(e){if(!e)return!1;const t=ga.getUserPermissions();return"admin"===e.role||"super_admin"===e.role||!0===(null==t?void 0:t.canManageProducts)}calculateSuggestedReorder(e){return Math.max(3*e.stockMin,20)-e.stock}checkLowStock(e){const t=ga.getCurrentUser();if(!this.shouldReceiveStockNotifications(t))return;e.filter(e=>e.stock>0&&e.stock<=this.LOW_STOCK_THRESHOLD).forEach(e=>{this.notifications.find(t=>"warning"===t.type&&t.titre.includes(e.nom)&&t.message.includes("stock bas")&&!t.lu&&Date.now()-new Date(t.dateCreation).getTime()<864e5)||this.createLowStockNotification(e)})}createLowStockNotification(e){const t=this.calculateSuggestedReorder(e),n={id:`stock-${e.id}-${Date.now()}`,type:"warning",titre:`Stock bas: ${e.nom}`,message:`Le produit "${e.nom}" a un stock critique de ${e.stock} unité(s). Stock minimum: ${e.stockMin}. Quantité suggérée à commander: ${t} unité(s).`,dateCreation:(new Date).toISOString(),lu:!1,productId:e.id,productName:e.nom,currentStock:e.stock,minimumStock:e.stockMin,suggestedReorder:t};this.notifications.unshift(n),this.saveNotifications(),this.showBrowserNotification(n)}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission&&new Notification(`SmartBoutique - ${e.titre}`,{body:`Stock actuel: ${e.currentStock} unité(s). Commande suggérée: ${e.suggestedReorder} unité(s).`,icon:"/favicon.ico",tag:`stock-${e.productId}`})}async requestNotificationPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;return"granted"===await Notification.requestPermission()}createNotification(e,t,n){const i={id:`notif-${Date.now()}`,type:e,titre:t,message:n,dateCreation:(new Date).toISOString(),lu:!1};this.notifications.unshift(i),this.saveNotifications()}},ja=({color:e="inherit"})=>{const[t,n]=vt.useState(null),{notifications:r,unreadCount:s,markAsRead:l,markAllAsRead:d,deleteNotification:u,clearAll:m,requestPermission:j}=(()=>{const[e,t]=vt.useState([]),[n,i]=vt.useState(0);return vt.useEffect(()=>ya.subscribe(e=>{t(e),i(ya.getUnreadCount())}),[]),{notifications:e,unreadCount:n,markAsRead:e=>{ya.markAsRead(e)},markAllAsRead:()=>{ya.markAllAsRead()},deleteNotification:e=>{ya.deleteNotification(e)},clearAll:()=>{ya.clearAll()},requestPermission:async()=>await ya.requestNotificationPermission()}})(),C=e=>{switch(e){case"info":default:return i.jsx(B,{color:"info"});case"warning":return i.jsx($,{color:"warning"});case"error":return i.jsx(z,{color:"error"});case"success":return i.jsx(_,{color:"success"})}},D=e=>{switch(e){case"info":return"info";case"warning":return"warning";case"error":return"error";case"success":return"success";default:return"default"}},v=Boolean(t),b=v?"notification-popover":void 0;return i.jsxs(i.Fragment,{children:[i.jsx(F,{title:"Notifications",children:i.jsx(U,{color:e,onClick:e=>{n(e.currentTarget)},children:i.jsx(T,{badgeContent:s,color:"error",children:s>0?i.jsx(k,{}):i.jsx(R,{})})})}),i.jsx(M,{id:b,open:v,anchorEl:t,onClose:()=>{n(null)},anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},children:i.jsxs(c,{sx:{width:400,maxHeight:500},children:[i.jsxs(a,{sx:{p:2,borderBottom:"1px solid",borderColor:"divider"},children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[i.jsx(o,{variant:"h6",children:"Notifications"}),s>0&&i.jsx(A,{label:`${s} non lue(s)`,color:"primary",size:"small"})]}),r.length>0&&i.jsxs(a,{sx:{mt:1,display:"flex",gap:1},children:[s>0&&i.jsx(I,{size:"small",startIcon:i.jsx(N,{}),onClick:d,children:"Tout marquer lu"}),i.jsx(I,{size:"small",startIcon:i.jsx(L,{}),onClick:m,color:"error",children:"Tout effacer"})]})]}),"Notification"in window&&"default"===Notification.permission&&i.jsx(O,{severity:"info",sx:{m:1},action:i.jsx(I,{size:"small",onClick:async()=>{await j()},children:"Activer"}),children:"Activez les notifications du navigateur pour les alertes de stock"}),0===r.length?i.jsxs(a,{sx:{p:3,textAlign:"center"},children:[i.jsx(R,{sx:{fontSize:48,color:"text.secondary",mb:1}}),i.jsx(o,{variant:"body2",color:"text.secondary",children:"Aucune notification"})]}):i.jsx(p,{sx:{maxHeight:350,overflow:"auto"},children:r.map((e,t)=>i.jsxs(ft.Fragment,{children:[i.jsxs(x,{button:!0,onClick:()=>(e=>{e.lu||l(e.id)})(e),sx:{bgcolor:e.lu?"transparent":"action.hover","&:hover":{bgcolor:"action.selected"}},children:[i.jsx(g,{children:e.titre.includes("Stock bas")?i.jsx(S,{color:"warning"}):C(e.type)}),i.jsx(y,{primary:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"subtitle2",sx:{fontWeight:e.lu?"normal":"bold",flex:1},children:e.titre}),i.jsx(A,{label:e.type,size:"small",color:D(e.type),variant:"outlined"})]}),secondary:i.jsxs(a,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:e.message}),i.jsx(o,{variant:"caption",color:"text.secondary",children:Ut(new Date(e.dateCreation),"dd/MM/yyyy HH:mm",{locale:xa})})]})}),i.jsx(V,{children:i.jsx(F,{title:"Supprimer",children:i.jsx(U,{edge:"end",size:"small",onClick:t=>{return n=t,i=e.id,n.stopPropagation(),void u(i);var n,i},children:i.jsx(q,{fontSize:"small"})})})})]}),t<r.length-1&&i.jsx(h,{})]},e.id))})]})})]})},Sa=240,Ca=({currentUser:e,onLogout:t})=>{var n,c,d;if(Mi())return i.jsx(da,{currentUser:e,onLogout:t});const[u,T]=vt.useState(!1),[k,R]=vt.useState(null),M=Jn(),A=Xn(),I=()=>{T(!u)},N=()=>{R(null)},L=ca.getNavigationItems(),O=e=>{switch(e){case"Dashboard":default:return i.jsx(b,{});case"Inventory":return i.jsx(S,{});case"PointOfSale":return i.jsx(C,{});case"AccountBalance":return i.jsx(D,{});case"Receipt":return i.jsx(f,{});case"Payment":return i.jsx(Y,{});case"Assessment":return i.jsx(w,{});case"People":return i.jsx(E,{});case"Settings":return i.jsx(P,{})}},V=i.jsxs("div",{children:[i.jsx(s,{children:i.jsxs(a,{display:"flex",alignItems:"center",width:"100%",children:[i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(U,{onClick:I,sx:{display:{sm:"none"}},children:i.jsx(W,{})})]})}),i.jsx(h,{}),i.jsx(p,{children:L.map(e=>i.jsx(x,{disablePadding:!0,children:i.jsxs(X,{selected:A.pathname===e.path,onClick:()=>{M(e.path),T(!1)},children:[i.jsx(g,{children:O(e.icon)}),i.jsx(y,{primary:e.label})]})},e.path))})]});return i.jsxs(a,{sx:{display:"flex"},children:[i.jsx(r,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:i.jsxs(s,{children:[i.jsx(U,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:I,sx:{mr:2,display:{sm:"none"}},children:i.jsx(v,{})}),i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(n=L.find(e=>e.path===A.pathname))?void 0:n.label)||"SmartBoutique"}),i.jsx(ja,{color:"inherit"}),i.jsx(F,{title:"Profil utilisateur",children:i.jsx(U,{color:"inherit",onClick:e=>{R(e.currentTarget)},sx:{ml:1},children:i.jsx(l,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:(null==(d=null==(c=null==e?void 0:e.nom)?void 0:c.charAt(0))?void 0:d.toUpperCase())||"U"})})}),i.jsxs(Q,{anchorEl:k,open:Boolean(k),onClose:N,onClick:N,children:[i.jsx(J,{disabled:!0,children:i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",children:null==e?void 0:e.nom}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["super_admin"===(null==e?void 0:e.role)&&"Super Administrateur","admin"===(null==e?void 0:e.role)&&"Administrateur","employee"===(null==e?void 0:e.role)&&"Employé"]})]})}),i.jsx(h,{}),i.jsxs(J,{onClick:()=>M("/settings"),children:[i.jsx(g,{children:i.jsx(H,{fontSize:"small"})}),"Mon Profil"]}),i.jsxs(J,{onClick:()=>{N(),t(),M("/login")},children:[i.jsx(g,{children:i.jsx(j,{fontSize:"small"})}),"Déconnexion"]})]})]})}),i.jsxs(a,{component:"nav",sx:{width:{sm:Sa},flexShrink:{sm:0}},children:[i.jsx(m,{variant:"temporary",open:u,onClose:I,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:Sa}},children:V}),i.jsx(m,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:Sa}},open:!0,children:V})]}),i.jsxs(a,{component:"main",sx:{flexGrow:1,p:3,width:{sm:"calc(100% - 240px)"},minHeight:"100vh",backgroundColor:"background.default"},children:[i.jsx(s,{}),i.jsx(si,{})]})]})},Da=({children:e,requiredPermission:t,requiredRole:n})=>ca.getCurrentUser()?n&&!ca.hasRole(n)?i.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(G,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Contactez votre administrateur pour obtenir l'accès requis."})]})}):t&&!ca.hasPermission(t)?i.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(G,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Cette fonctionnalité est réservée aux utilisateurs autorisés."})]})}):i.jsx(i.Fragment,{children:e}):i.jsx(ri,{to:"/login",replace:!0}),va=({onLogin:e})=>{const[t,n]=vt.useState(""),[r,s]=vt.useState(""),[c,d]=vt.useState(!1),[u,m]=vt.useState(""),[h,p]=vt.useState(!1),x=async t=>{p(!0),m("");let n={email:"",password:""};switch(t){case"admin":n={email:"<EMAIL>",password:"admin123"};break;case"manager":n={email:"<EMAIL>",password:"manager123"};break;case"employee":n={email:"<EMAIL>",password:"employee123"}}try{const t=await ca.login(n.email,n.password);t.success&&t.user?e(t.user):m(t.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}};return i.jsx(K,{maxWidth:"sm",sx:{height:"100vh",display:"flex",alignItems:"center"},children:i.jsxs(a,{sx:{width:"100%",px:2},children:[i.jsxs(a,{sx:{textAlign:"center",mb:4},children:[i.jsx(l,{sx:{width:80,height:80,bgcolor:"primary.main",mx:"auto",mb:2},children:i.jsx(Z,{sx:{fontSize:40}})}),i.jsx(o,{variant:"h4",component:"h1",fontWeight:"bold",color:"primary",children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",sx:{mt:1},children:"Gestion de boutique mobile"})]}),i.jsx(ee,{elevation:4,sx:{borderRadius:3},children:i.jsxs(te,{sx:{p:4},children:[i.jsx(o,{variant:"h5",component:"h2",textAlign:"center",sx:{mb:3},children:"Connexion"}),u&&i.jsx(O,{severity:"error",sx:{mb:3},children:u}),i.jsxs(a,{component:"form",onSubmit:async n=>{n.preventDefault(),m(""),p(!0);try{const n=await ca.login(t,r);n.success&&n.user?e(n.user):m(n.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}},children:[i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:t,onChange:e=>n(e.target.value),required:!0,sx:{mb:3},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(ae,{color:"action"})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"email",style:{fontSize:"16px"}}}),i.jsx(ne,{fullWidth:!0,label:"Mot de passe",type:c?"text":"password",value:r,onChange:e=>s(e.target.value),required:!0,sx:{mb:4},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(G,{color:"action"})}),endAdornment:i.jsx(ie,{position:"end",children:i.jsx(U,{onClick:()=>{d(!c)},edge:"end",size:"large",children:c?i.jsx(re,{}):i.jsx(se,{})})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"current-password",style:{fontSize:"16px"}}}),i.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:h,sx:{py:2,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,mb:3},children:h?"Connexion...":"Se connecter"})]}),i.jsxs(a,{sx:{mt:3},children:[i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mb:2},children:"Connexion rapide (Démo)"}),i.jsxs(a,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("admin"),disabled:h,sx:{py:1.5},children:"Super Admin"}),i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("manager"),disabled:h,sx:{py:1.5},children:"Gestionnaire"}),i.jsx(I,{variant:"outlined",size:"large",onClick:()=>x("employee"),disabled:h,sx:{py:1.5},children:"Employé"})]})]})]})}),i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mt:3},children:"Version Mobile • SmartBoutique 2024"})]})})},ba=({onLogin:e})=>{if(Mi())return i.jsx(va,{onLogin:e});const[t,n]=vt.useState(""),[r,s]=vt.useState(""),[c,d]=vt.useState(""),[u,m]=vt.useState(!1),p=(e,t)=>{n(e),s(t)};return i.jsx(K,{component:"main",maxWidth:"sm",children:i.jsxs(a,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",py:4},children:[i.jsxs(a,{sx:{mb:4,textAlign:"center"},children:[i.jsx(l,{sx:{mx:"auto",mb:2,bgcolor:"primary.main",width:64,height:64},children:i.jsx(Z,{sx:{fontSize:32}})}),i.jsx(o,{component:"h1",variant:"h4",gutterBottom:!0,children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",children:"Système de Gestion de Boutique"})]}),i.jsx(ee,{sx:{width:"100%",maxWidth:400},children:i.jsxs(te,{sx:{p:4},children:[i.jsxs(a,{sx:{display:"flex",alignItems:"center",mb:3},children:[i.jsx(l,{sx:{mr:2,bgcolor:"secondary.main"},children:i.jsx(oe,{})}),i.jsx(o,{component:"h2",variant:"h5",children:"Connexion"})]}),c&&i.jsx(O,{severity:"error",sx:{mb:2},children:c}),i.jsxs(a,{component:"form",onSubmit:async n=>{n.preventDefault(),d(""),m(!0);try{const n=await ca.login(t,r);n.success&&n.user?e(n.user):d(n.message||"Erreur de connexion")}catch(i){d("Une erreur est survenue lors de la connexion")}finally{m(!1)}},children:[i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t,onChange:e=>n(e.target.value),disabled:u}),i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:"password",id:"password",autoComplete:"current-password",value:r,onChange:e=>s(e.target.value),disabled:u}),i.jsx(I,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?"Connexion...":"Se connecter"})]}),i.jsx(h,{sx:{my:3},children:i.jsx(A,{label:"Comptes de démonstration",size:"small"})}),i.jsxs(a,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","admin123"),disabled:u,children:"Super Admin (<EMAIL>)"}),i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","manager123"),disabled:u,children:"Gestionnaire (<EMAIL>)"}),i.jsx(I,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","employee123"),disabled:u,children:"Employé (<EMAIL>)"})]})]})}),i.jsxs(a,{sx:{mt:4,textAlign:"center"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"© 2024 SmartBoutique. Tous droits réservés."}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Version 1.0.0"})]})]})})},fa=2800,wa=e=>null==e||isNaN(e)?0:Math.round(100*e)/100,Ea=(e,t,n,i=2800)=>{if(null==e||isNaN(e))return 0;if((null==i||isNaN(i)||i<=0)&&(console.warn("Invalid exchange rate provided, using default:",fa),i=fa),t===n)return wa(e);let a;if("CDF"===t&&"USD"===n)a=e/i;else{if("USD"!==t||"CDF"!==n)return console.error("Unsupported currency conversion:",t,"to",n),0;a=e*i}return wa(a)},Pa=(e,t=2800)=>Ea(e,"CDF","USD",t),Fa=(e,t,n={})=>{const{showSymbol:i=!0,minimumFractionDigits:a=("USD"===t?2:0),maximumFractionDigits:r=2}=n,s=((e,t=0,n=2)=>null==e||isNaN(e)?"0":e.toLocaleString("fr-FR",{minimumFractionDigits:t,maximumFractionDigits:n}))(null==e||isNaN(e)?0:e,a,r);return i?"USD"===t?`$${s}`:"CDF"===t?`${s} CDF`:`${s} ${t}`:s},Ua=(e,t="valeur",n={})=>{const{allowZero:i=!1,allowNegative:a=!1,minValue:r=null,maxValue:s=null}=n,o=[];return null==e||isNaN(e)?(o.push(`${t} doit être un nombre valide`),{isValid:!1,errors:o}):(!a&&e<0&&o.push(`${t} ne peut pas être négatif`),i||0!==e||o.push(`${t} doit être supérieur à zéro`),null!==r&&e<r&&o.push(`${t} doit être supérieur ou égal à ${r}`),null!==s&&e>s&&o.push(`${t} doit être inférieur ou égal à ${s}`),{isValid:0===o.length,errors:o})},Ta=(e,t=0,n="Erreur de calcul financier")=>{try{const n=e();return null==n||isNaN(n)?t:n}catch(Sr){return console.error(n,Sr),t}},ka="Le prix ne peut pas être négatif",Ra=(e,t,n=2800)=>{const i=wa((a=t,r=e,Ta(()=>a-r,0,"Erreur lors du calcul du bénéfice unitaire")));var a,r;return{beneficeUnitaireCDF:i,beneficeUnitaireUSD:wa(Pa(i,n))}},Ma=(e,t)=>{const n=((e,t,n={})=>{const{purchaseFieldName:i="Le prix d'achat",sellingFieldName:a="Le prix de vente"}=n,r=Ua(e,i),s=Ua(t,a),o=[...r.errors,...s.errors];return r.isValid&&s.isValid&&t<=e&&o.push("Le prix de vente doit être supérieur au prix d'achat"),{isValid:0===o.length,errors:o}})(e,t);return{isValid:n.isValid,errorMessage:n.errors.length>0?n.errors[0]:void 0}},Aa=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,Ia=(e,t)=>{const n=(e=>Ta(()=>{let t=0,n=0;return e.forEach(e=>{e.beneficeUnitaireCDF&&e.stock>0&&(t+=wa(e.beneficeUnitaireCDF*e.stock)),e.beneficeUnitaireUSD&&e.stock>0&&(n+=wa(e.beneficeUnitaireUSD*e.stock))}),{totalRevenueCDF:wa(t),totalRevenueUSD:wa(n)}},{totalRevenueCDF:0,totalRevenueUSD:0},"Erreur lors du calcul des revenus d'inventaire"))(e),i=((e,t)=>Ta(()=>{let n=0,i=0;return e.forEach(e=>{e.produits.forEach(e=>{const a=t.find(t=>t.id===e.produitId);a&&a.beneficeUnitaireCDF&&(n+=wa(a.beneficeUnitaireCDF*e.quantite)),a&&a.beneficeUnitaireUSD&&(i+=wa(a.beneficeUnitaireUSD*e.quantite))})}),{realizedRevenueCDF:wa(n),realizedRevenueUSD:wa(i)}},{realizedRevenueCDF:0,realizedRevenueUSD:0},"Erreur lors du calcul des revenus réalisés"))(t,e),a=(e=>{const t={};return e.forEach(e=>{t[e.categorie]||(t[e.categorie]={revenueCDF:0,revenueUSD:0,productCount:0}),e.beneficeUnitaireCDF&&e.stock>0&&(t[e.categorie].revenueCDF+=e.beneficeUnitaireCDF*e.stock),e.beneficeUnitaireUSD&&e.stock>0&&(t[e.categorie].revenueUSD+=e.beneficeUnitaireUSD*e.stock),t[e.categorie].productCount+=1}),t})(e),r=((e,t=10)=>e.filter(e=>e.beneficeUnitaireCDF&&e.beneficeUnitaireCDF>0).sort((e,t)=>{const n=(e.beneficeUnitaireCDF||0)*e.stock;return(t.beneficeUnitaireCDF||0)*t.stock-n}).slice(0,t))(e,5);return{totalInventoryRevenueCDF:n.totalRevenueCDF,totalInventoryRevenueUSD:n.totalRevenueUSD,realizedRevenueCDF:i.realizedRevenueCDF,realizedRevenueUSD:i.realizedRevenueUSD,categoryBreakdown:a,topProducts:r}},Na=(e,t)=>Fa(e,t),La=(e,t=2800)=>{const n=null==e||isNaN(e)?0:e,i=null==t||isNaN(t)||t<=0?fa:t,a=wa(Pa(n,i));return{primaryAmount:Fa(n,"CDF",{showSymbol:!1}),secondaryAmount:Fa(a,"USD",{showSymbol:!1}),primaryCurrency:"CDF",secondaryCurrency:"USD"}},Oa=e=>0===e.stock?"out_of_stock":e.stock<=e.stockMin?"low_stock":"in_stock",Va=()=>"123"+Date.now().toString().slice(-10),qa=()=>{const e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`SB${e.slice(-8)}${t}`},Ba=(e,t)=>Pa(e,t),_a=(e,t)=>((e,t=2800)=>Ea(e,"USD","CDF",t))(e,t),za=(e,t)=>Pa(e,t),$a=({analytics:e,products:t,todaySalesData:n,debtsData:r,isLoading:s=!1})=>{if(s)return i.jsxs(a,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),i.jsx(le,{})]});if(0===e.totalProductsWithProfit)return i.jsxs(a,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Profit - Analyse des Bénéfices"}),i.jsx(O,{severity:"info",children:"Aucun produit avec prix d'achat et de vente configurés. Ajoutez des prix d'achat aux produits pour voir l'analyse des profits."})]});const l=Object.entries(e.categoryBreakdown);return i.jsxs(a,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Profit - Analyse des Bénéfices"}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Potentiel"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[La(e.totalInventoryRevenueCDF,2800).primaryAmount," ",La(e.totalInventoryRevenueCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",La(e.totalInventoryRevenueCDF,2800).secondaryAmount]})]}),i.jsx(S,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Profit Réalisé"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[La(e.realizedRevenueCDF,2800).primaryAmount," ",La(e.realizedRevenueCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"success.main",children:["≈ $",La(e.realizedRevenueCDF,2800).secondaryAmount]})]}),i.jsx(de,{color:"success",sx:{fontSize:40}})]})})})}),n&&i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[La(n.revenusCDF,2800).primaryAmount," ",La(n.revenusCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"primary",children:["≈ $",La(n.revenusCDF,2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:[n.nombreVentes," vente",1!==n.nombreVentes?"s":""]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),r&&i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total des Dettes"}),i.jsxs(o,{variant:"h6",color:"error",fontWeight:"medium",children:[La(r.montantDettesTotalCDF,2800).primaryAmount," ",La(r.montantDettesTotalCDF,2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"error",children:["≈ $",La(r.montantDettesTotalCDF,2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:[r.dettesActives," dette",1!==r.dettesActives?"s":""," active",1!==r.dettesActives?"s":""]})]}),i.jsx(D,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Produits les Plus Rentables"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{size:"small",children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produit"}),i.jsx(ge,{align:"right",children:"Profit Potentiel"})]})}),i.jsx(ye,{children:e.topProducts.map(e=>{const t=(e.beneficeUnitaireCDF||0)*e.stock;return i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:e.nom}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:["Stock: ",e.stock]})]})}),i.jsx(ge,{align:"right",children:i.jsx(o,{variant:"body2",children:Aa(t,"CDF")})})]},e.id)})})]})})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Profit par Catégorie"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{size:"small",children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Produits"}),i.jsx(ge,{align:"right",children:"Profit (CDF)"})]})}),i.jsx(ye,{children:l.sort(([,e],[,t])=>t.revenueCDF-e.revenueCDF).map(([e,t])=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx(A,{label:e,size:"small"})}),i.jsx(ge,{align:"right",children:t.productCount}),i.jsxs(ge,{align:"right",children:[i.jsx(o,{variant:"body2",children:Aa(t.revenueCDF,"CDF")}),i.jsx(o,{variant:"caption",color:"textSecondary",children:Aa(t.revenueUSD,"USD")})]})]},e))})]})})})]})})]})]})},Wa=()=>{const[e,t]=vt.useState(null),[n,r]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(null),[m,h]=vt.useState(null),[j,D]=vt.useState(null),[v,b]=vt.useState({tauxChangeUSDCDF:2800}),[f,w]=vt.useState("jour"),E=ca.getUserPermissions(),P=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(Sr){return console.warn("Error parsing date:",e,Sr),null}};vt.useEffect(()=>{(async()=>{try{const e=await la.getSettings();b(e),await U()}catch(Sr){console.error("Error loading data:",Sr),await U()}})()},[]);const F=(e,t)=>{switch(e){case"jour":default:return t.ventesDuJour;case"semaine":return t.ventesDeLaSemaine;case"mois":return t.ventesDuMois}},U=async()=>{const e=await la.getProducts(),n=await la.getSales(),i=await la.getDebts(),a=await la.getSettings(),s=new Date,o=Tt(s),c=kt(s),d=Rt(s,7),u=Rt(s,30),m=n.filter(e=>{const t=P(e.datevente);return!!t&&(Mt(t,o)&&At(t,c))}),h=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,d)}),p=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,u)}),x=e.filter(e=>e.stock>0),g=e.filter(e=>e.stock<=e.stockMin&&e.stock>0),y=e.filter(e=>0===e.stock),j=i.filter(e=>"active"===e.statut),S=i.filter(e=>"overdue"===e.statut),C=wa(m.reduce((e,t)=>e+t.totalCDF,0)),v=wa(h.reduce((e,t)=>e+t.totalCDF,0)),b=wa(p.reduce((e,t)=>e+t.totalCDF,0)),f=wa(e.reduce((e,t)=>e+t.prixCDF*t.stock,0)),w=wa(j.reduce((e,t)=>e+t.montantRestantCDF,0)),F=a.tauxChangeUSDCDF||fa,U={ventesDuJour:{nombreVentes:m.length,revenusCDF:C,revenusUSD:Pa(C,F)},ventesDeLaSemaine:{nombreVentes:h.length,revenusCDF:v,revenusUSD:Pa(v,F)},ventesDuMois:{nombreVentes:p.length,revenusCDF:b,revenusUSD:Pa(b,F)},articlesActifs:x.length,produitsStockBas:g.length,articlesEnRupture:y.length,valeurInventaireCDF:f,valeurInventaireUSD:f/a.tauxChangeUSDCDF,dettesActives:j.length,dettesEnRetard:S.length,montantDettesTotalCDF:w,montantDettesTotalUSD:w/a.tauxChangeUSDCDF};if(t(U),r(g.slice(0,5)),l(n.slice(-5).reverse()),E.canViewRevenue){const t=Ia(e,n);D(t)}T(n),k(n)},T=e=>{const t=Array.from({length:14},(e,t)=>{const n=Rt(new Date,13-t);return{date:n,label:Ut(n,"dd/MM",{locale:xa}),sales:0,revenue:0}});e.forEach(e=>{const n=P(e.datevente);if(!n)return;const i=t.find(e=>Ut(e.date,"yyyy-MM-dd")===Ut(n,"yyyy-MM-dd"));i&&(i.sales+=1,i.revenue+=e.totalCDF)}),u({labels:t.map(e=>e.label),datasets:[{label:"Profit (CDF)",data:t.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]})},k=e=>{const t=e.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+1,e),{});h({labels:["Cash","Banque","Mobile Money"],datasets:[{data:[t.cash||0,t.banque||0,t.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]})},R=e=>0===e.stock?i.jsx(z,{color:"error"}):e.stock<=e.stockMin?i.jsx($,{color:"warning"}):i.jsx(_,{color:"success"});return e?i.jsxs(a,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Tableau de bord"}),E.canViewRevenue&&j&&i.jsx(a,{sx:{mb:4},children:i.jsx($a,{analytics:j,products:n,todaySalesData:null==e?void 0:e.ventesDuJour,debtsData:e?{montantDettesTotalCDF:e.montantDettesTotalCDF,montantDettesTotalUSD:e.montantDettesTotalUSD,dettesActives:e.dettesActives}:void 0,isLoading:!j})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1,children:[i.jsxs(je,{size:"small",sx:{minWidth:120},children:[i.jsx(Se,{children:"Période"}),i.jsxs(Ce,{value:f,label:"Période",onChange:e=>w(e.target.value),children:[i.jsx(J,{value:"jour",children:"Jour"}),i.jsx(J,{value:"semaine",children:"Semaine"}),i.jsx(J,{value:"mois",children:"Mois"})]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]}),i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:(e=>{switch(e){case"jour":default:return"Ventes du jour";case"semaine":return"Ventes de la semaine";case"mois":return"Ventes du mois"}})(f)}),i.jsx(o,{variant:"h6",children:F(f,e).nombreVentes}),E.canViewFinancials&&i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[La(F(f,e).revenusCDF,v.tauxChangeUSDCDF).primaryAmount," ",La(F(f,e).revenusCDF,v.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(F(f,e).revenusCDF,v.tauxChangeUSDCDF).secondaryAmount]})]})]})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes de la semaine"}),i.jsx(o,{variant:"h6",children:e.ventesDeLaSemaine.nombreVentes}),E.canViewFinancials&&i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[La(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).primaryAmount," ",La(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(e.ventesDeLaSemaine.revenusCDF,v.tauxChangeUSDCDF).secondaryAmount]})]})]}),i.jsx(De,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Articles actifs"}),i.jsx(o,{variant:"h6",children:e.articlesActifs}),e.produitsStockBas>0&&i.jsx(A,{label:`${e.produitsStockBas} stock bas`,color:"warning",size:"small"})]}),i.jsx(S,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:4,lg:2.4,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),E.canViewFinancials?i.jsxs(i.Fragment,{children:[i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[La(e.valeurInventaireCDF,v.tauxChangeUSDCDF).primaryAmount," ",La(e.valeurInventaireCDF,v.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",La(e.valeurInventaireCDF,v.tauxChangeUSDCDF).secondaryAmount]})]}):i.jsxs(o,{variant:"h6",children:[e.articlesActifs," articles"]})]}),i.jsx(S,{color:"success",sx:{fontSize:40}})]})})})})]}),e.articlesEnRupture>0&&i.jsx(ce,{container:!0,spacing:3,sx:{mb:3},children:i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",sx:{mb:2},children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{variant:"h6",component:"div",children:"Articles en rupture"}),i.jsxs(o,{variant:"body2",children:[e.articlesEnRupture," article",e.articlesEnRupture>1?"s":""," en rupture de stock nécessite",e.articlesEnRupture>1?"nt":""," un réapprovisionnement urgent"]})]}),i.jsx(z,{sx:{fontSize:40}})]})})})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Tendance des ventes (14 derniers jours)"}),d&&i.jsx(Vt,{data:d,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Méthodes de paiement"}),m&&i.jsx(qt,{data:m,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})]})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Produits en stock bas"}),0===n.length?i.jsx(O,{severity:"success",children:"Aucun produit en stock bas"}):i.jsx(p,{dense:!0,children:n.map(e=>i.jsxs(x,{children:[i.jsx(g,{children:R(e)}),i.jsx(y,{primary:e.nom,secondary:`Stock: ${e.stock} (Min: ${e.stockMin})`})]},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Ventes récentes"}),0===s.length?i.jsx(O,{severity:"info",children:"Aucune vente récente"}):i.jsx(p,{dense:!0,children:s.map(e=>i.jsx(x,{children:i.jsx(y,{primary:E.canViewFinancials?`${e.nomClient} - ${La(e.totalCDF,v.tauxChangeUSDCDF).primaryAmount} ${La(e.totalCDF,v.tauxChangeUSDCDF).primaryCurrency}`:e.nomClient,secondary:(()=>{const t=P(e.datevente),n=t?Ut(t,"dd/MM/yyyy HH:mm",{locale:xa}):"Date invalide";if(E.canViewFinancials){return`${n} • ≈ $${La(e.totalCDF,v.tauxChangeUSDCDF).secondaryAmount}`}return n})()})},e.id))})]})})]})]}):i.jsx(o,{children:"Chargement..."})},Xa=({label:e,value:t,onChange:n,min:r=0,max:s=1e6,step:l=100,exchangeRate:c,disabled:d=!1,required:u=!1,error:m=!1,helperText:h,showSlider:p=!0,allowUSDInput:x=!0,onCurrencyModeChange:g})=>{const[y,j]=vt.useState("CDF"),[S,C]=vt.useState(""),[D,v]=vt.useState(t);vt.useEffect(()=>{if(v(t),"CDF"===y)C(t.toString());else{const e=Ba(t,c);C(e.toFixed(2))}},[t,y,c]);const b=Ba(D,c),f=D;return i.jsxs(je,{fullWidth:!0,disabled:d,children:[i.jsxs(ve,{component:"legend",sx:{mb:1},children:[e," ",u&&"*"]}),i.jsxs(ce,{container:!0,spacing:2,children:[x&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(be,{value:y,exclusive:!0,onChange:(e,t)=>{if(t&&t!==y){if(j(t),"USD"===t){const e=Ba(D,c);C(e.toFixed(2))}else C(D.toString());g&&g(t)}},size:"small",disabled:d,children:[i.jsx(fe,{value:"CDF",children:"Saisie en CDF"}),i.jsx(fe,{value:"USD",children:"Saisie en USD"})]})}),i.jsx(ce,{item:!0,xs:12,md:p?6:12,children:i.jsx(ne,{fullWidth:!0,label:`Montant (${y})`,type:"number",value:S,onChange:e=>{const t=e.target.value;C(t);const i=parseFloat(t)||0;if(Ua(i,"Le montant",{allowZero:!0,allowNegative:!1,minValue:r,maxValue:s}).isValid){let e;e="CDF"===y?i:_a(i,c),e=Math.max(r,Math.min(s,e)),v(e),n(e)}},disabled:d,error:m,helperText:h,inputProps:{min:"CDF"===y?r:Ba(r,c),max:"CDF"===y?s:Ba(s,c),step:"CDF"===y?l:.01},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:"USD"===y?"$":""}),endAdornment:i.jsx(ie,{position:"end",children:"CDF"===y?"CDF":"USD"})}})}),p&&i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(a,{sx:{px:2,pt:3},children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Curseur (CDF)"}),i.jsx(we,{value:D,onChange:(e,t)=>{const i=Array.isArray(t)?t[0]:t;if(v(i),"CDF"===y)C(i.toString());else{const e=Ba(i,c);C(e.toFixed(2))}n(i)},min:r,max:s,step:l,disabled:d,valueLabelDisplay:"auto",valueLabelFormat:e=>Na(e,"CDF")})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(a,{sx:{p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Équivalences:"}),i.jsx(o,{variant:"body1",color:"primary",fontWeight:"medium",children:Na(f,"CDF")}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",Na(b,"USD")]})]})})]})]})},Qa=({value:e,onChange:t,min:n=1,max:r=999999,disabled:s=!1,size:o="small",showButtons:l=!0,allowDirectInput:c=!0,label:d,error:u=!1,helperText:m})=>{const[h,p]=vt.useState(e.toString());vt.useEffect(()=>{p(e.toString())},[e]);const x=i=>{const a=i.target.value;if(p(a),""===a)return;const s=parseFloat(a);if(!isNaN(s)){const i=Math.round(s),a=Math.max(n,Math.min(r,i));i>=n&&i<=r?t(i):a!==e&&t(a)}},g=()=>{if(""===h||isNaN(parseFloat(h))){const i=e||n;return p(i.toString()),void(i!==e&&t(i))}const i=Math.round(parseFloat(h)),a=Math.max(n,Math.min(r,i));p(a.toString()),a!==e&&t(a)},y=()=>{const n=Math.min(r,e+1);t(n)},j=()=>{const i=Math.max(n,e-1);t(i)},S=e=>{e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase())||(/[0-9.]/.test(e.key)||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab","Enter","Home","End","Escape","."].includes(e.key)||e.preventDefault(),"."===e.key&&h.includes(".")&&e.preventDefault())};return!c&&l?i.jsxs(a,{display:"flex",alignItems:"center",gap:.5,children:[i.jsx(U,{size:o,onClick:j,disabled:s||e<=n,children:i.jsx(Ee,{fontSize:o})}),i.jsx(a,{sx:{minWidth:"small"===o?"30px":"40px",textAlign:"center",fontWeight:"medium",fontSize:"small"===o?"0.875rem":"1rem"},children:e}),i.jsx(U,{size:o,onClick:y,disabled:s||e>=r,children:i.jsx(Pe,{fontSize:o})})]}):c&&!l?i.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:S,disabled:s,error:u,helperText:m,inputProps:{min:n,max:r,step:1},sx:{minWidth:"80px"}}):i.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:S,disabled:s,error:u,helperText:m||(l?"Tapez directement la quantité (recommandé) ou utilisez +/-":"Tapez directement la quantité désirée"),placeholder:"Tapez la quantité...",inputProps:{min:n,max:r,step:1,style:{textAlign:"center",fontSize:"small"===o?"0.875rem":"1rem",fontWeight:500}},InputProps:{startAdornment:l?i.jsx(ie,{position:"start",children:i.jsx(U,{size:o,onClick:j,disabled:s||e<=n,edge:"start",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(Ee,{fontSize:o})})}):void 0,endAdornment:l?i.jsx(ie,{position:"end",children:i.jsx(U,{size:o,onClick:y,disabled:s||e>=r,edge:"end",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(Pe,{fontSize:o})})}):void 0},sx:{minWidth:l?"160px":"100px","& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderWidth:2,borderColor:"primary.main"},"& input":{cursor:"text","&:focus":{backgroundColor:"rgba(25, 118, 210, 0.04)"}}}}})},Ja=()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState("all"),[g,y]=vt.useState(0),[j,C]=vt.useState(10),[D,v]=vt.useState(!1),[b,f]=vt.useState(null),[w,E]=vt.useState({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0}),[P,T]=vt.useState(""),[k,R]=vt.useState(""),[M,N]=vt.useState(!1),[L,V]=vt.useState(""),[B,W]=vt.useState({tauxChangeUSDCDF:2800}),X=ca.getUserPermissions();vt.useEffect(()=>{Q()},[]);vt.useEffect(()=>{e.length>0&&ya.checkLowStock(e)},[e]),vt.useEffect(()=>{H()},[e,d,m,p]),vt.useEffect(()=>{-1===j&&C(s.length||1)},[s.length,j]);const Q=async()=>{try{const e=await la.getProducts(),n=await la.getSettings(),i=e.map(e=>{const t=(new Date).toISOString();return{...e,dateCreation:e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())?e.dateCreation:t,dateModification:e.dateModification&&!isNaN(new Date(e.dateModification).getTime())?e.dateModification:t}});t(i),r(n.categories),W(n)}catch(e){console.error("Error loading products:",e),T("Erreur lors du chargement des produits")}},H=()=>{let t=e;d&&(t=t.filter(e=>e.nom.toLowerCase().includes(d.toLowerCase())||e.codeQR.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase()))),m&&(t=t.filter(e=>e.categorie===m)),"all"!==p&&(t=t.filter(e=>Oa(e)===p)),l(t)},Y=e=>{switch(e){case"out_of_stock":return"error";case"low_stock":return"warning";case"in_stock":return"success";default:return"default"}},G=e=>{switch(e){case"out_of_stock":return i.jsx(z,{});case"low_stock":return i.jsx($,{});case"in_stock":return i.jsx(_,{});default:return i.jsx(S,{})}},K=e=>{switch(e){case"out_of_stock":return"Rupture";case"low_stock":return"Stock bas";case"in_stock":return"En stock";default:return"Inconnu"}},Z=e=>{e?(f(e),E({nom:e.nom,description:e.description,prixAchatCDF:e.prixAchatCDF||0,prixCDF:e.prixCDF,categorie:e.categorie,stock:e.stock,stockMin:e.stockMin,quantiteEnStock:e.quantiteEnStock||e.stock,coutAchatStockCDF:e.coutAchatStockCDF||e.prixAchatCDF*e.stock,prixParPieceCDF:e.prixParPieceCDF||e.prixCDF})):(f(null),E({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0,quantiteEnStock:0,coutAchatStockCDF:0,prixParPieceCDF:0})),v(!0),T(""),R(""),setTimeout(()=>{document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea').forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})},100)},ae=()=>{v(!1),f(null),T(""),R("")},re=e.length,se=e.filter(e=>"in_stock"===Oa(e)).length,oe=e.filter(e=>"low_stock"===Oa(e)).length,le=e.reduce((e,t)=>e+t.prixCDF*t.stock,0);return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Inventaire"}),X.canManageProducts&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>Z(),children:"Nouveau Produit"})]}),k&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:k}),P&&i.jsx(O,{severity:"error",sx:{mb:2},action:i.jsx(I,{color:"inherit",size:"small",onClick:()=>{window.confirm("Cela va supprimer toutes les données et réinitialiser l'application. Continuer?")&&(localStorage.clear(),window.location.reload())},children:"Réinitialiser les données"}),children:P}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Produits"}),i.jsx(o,{variant:"h6",children:re})]}),i.jsx(S,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Stock"}),i.jsx(o,{variant:"h6",color:"success.main",children:se})]}),i.jsx(_,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Stock Bas"}),i.jsx(o,{variant:"h6",color:"warning.main",children:oe})]}),i.jsx($,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:[La(le,B.tauxChangeUSDCDF).primaryAmount," ",La(le,B.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",La(le,B.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(S,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom, Code QR ou description...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Catégorie"}),i.jsxs(Ce,{value:m,label:"Catégorie",onChange:e=>h(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Statut Stock"}),i.jsxs(Ce,{value:p,label:"Statut Stock",onChange:e=>x(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"in_stock",children:"En stock"}),i.jsx(J,{value:"low_stock",children:"Stock bas"}),i.jsx(J,{value:"out_of_stock",children:"Rupture"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:2,children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Exporter CSV",children:i.jsx(U,{onClick:()=>{const e=`SmartBoutique_Produits_${(new Date).toISOString().split("T")[0]}.csv`;Ai.downloadCSV(s,Ii,e),R("Produits exportés en CSV avec succès (compatible Excel)"),setTimeout(()=>R(""),3e3)},children:i.jsx(Ue,{})})}),i.jsx(F,{title:"Importer CSV",children:i.jsx(U,{onClick:()=>N(!0),children:i.jsx(Te,{})})})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produit"}),i.jsx(ge,{children:"Code QR"}),i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Prix de Vente"}),i.jsx(ge,{align:"center",children:"Stock"}),i.jsx(ge,{align:"center",children:"Qté Stock"}),i.jsx(ge,{align:"right",children:"Coût Stock"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{children:"Dernière Modif."}),X.canManageProducts&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===j?s:s.slice(g*j,g*j+j)).map(n=>{const r=Oa(n);return i.jsxs(xe,{hover:!0,onClick:()=>Z(n),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),i.jsx(o,{variant:"caption",color:"text.secondary",children:n.description})]})}),i.jsx(ge,{children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[n.codeQR,i.jsx(F,{title:"Code QR",children:i.jsx(U,{size:"small",children:i.jsx(ke,{fontSize:"small"})})})]})}),i.jsx(ge,{children:n.categorie}),i.jsx(ge,{align:"right",children:i.jsxs(a,{children:[i.jsxs(o,{variant:"body2",fontWeight:"medium",children:[La(n.prixCDF,B.tauxChangeUSDCDF).primaryAmount," ",La(n.prixCDF,B.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(n.prixCDF,B.tauxChangeUSDCDF).secondaryAmount]})]})}),i.jsx(ge,{align:"center",children:i.jsxs(a,{children:[i.jsx(o,{variant:"body2",children:n.stock}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Min: ",n.stockMin]})]})}),i.jsx(ge,{align:"center",children:i.jsxs(a,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:n.quantiteEnStock||n.stock}),i.jsx(o,{variant:"caption",color:"text.secondary",children:"Réel"})]})}),i.jsx(ge,{align:"right",children:i.jsxs(a,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:n.coutAchatStockCDF?`${La(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryAmount} ${La(n.coutAchatStockCDF,B.tauxChangeUSDCDF).primaryCurrency}`:`${La(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryAmount} ${La(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).primaryCurrency}`}),i.jsx(o,{variant:"caption",color:"text.secondary",children:n.coutAchatStockCDF?`≈ $${La(n.coutAchatStockCDF,B.tauxChangeUSDCDF).secondaryAmount} • Total`:`≈ $${La(n.prixAchatCDF*n.stock,B.tauxChangeUSDCDF).secondaryAmount} • Total`})]})}),i.jsx(ge,{align:"center",children:i.jsx(A,{icon:G(r),label:K(r),color:Y(r),size:"small"})}),i.jsx(ge,{children:(()=>{try{const e=new Date(n.dateModification);return isNaN(e.getTime())?"Date invalide":Ut(e,"dd/MM/yyyy",{locale:xa})}catch(e){return"Date invalide"}})()}),X.canManageProducts&&i.jsx(ge,{align:"center",children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>Z(n),children:i.jsx(Re,{fontSize:"small"})})}),ca.hasRole(["super_admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await la.setProducts(i),R("Produit supprimé avec succès"),setTimeout(()=>R(""),3e3)}})(n),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id)})})]}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:g,onPageChange:(e,t)=>{-1!==j&&y(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);C(t),y(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:D,onClose:ae,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:b?"Modifier le Produit":"Nouveau Produit"}),i.jsxs(Ne,{children:[P&&i.jsx(O,{severity:"error",sx:{mb:2},children:P}),k&&i.jsx(O,{severity:"success",sx:{mb:2},children:k}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom du produit *",value:w.nom,onChange:e=>E({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:w.description,onChange:e=>E({...w,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Prix d'achat *",value:w.prixAchatCDF,onChange:e=>E({...w,prixAchatCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixAchatCDF<=0,helperText:w.prixAchatCDF<=0?"Le prix d'achat doit être supérieur à zéro":"Prix d'achat du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Prix de vente *",value:w.prixCDF,onChange:e=>E({...w,prixCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixCDF<=w.prixAchatCDF,helperText:w.prixCDF<=w.prixAchatCDF?"Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice":"Prix de vente du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Catégorie *"}),i.jsx(Ce,{value:w.categorie,label:"Catégorie *",onChange:e=>E({...w,categorie:e.target.value}),children:n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Qa,{value:w.stock,onChange:e=>E({...w,stock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock actuel",helperText:"Quantité actuelle en stock"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Qa,{value:w.stockMin,onChange:e=>E({...w,stockMin:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock minimum",helperText:"Seuil d'alerte pour stock bas"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(o,{variant:"h6",sx:{mt:2,mb:1,color:"primary.main"},children:"Gestion Avancée de l'Inventaire"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Qa,{value:w.quantiteEnStock,onChange:e=>E({...w,quantiteEnStock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Quantité en Stock",helperText:"Quantité réelle en stock (éditable)"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Coût d'Achat du Stock",value:w.coutAchatStockCDF,onChange:e=>E({...w,coutAchatStockCDF:e}),min:0,max:5e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Coût total d'achat du stock actuel"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Prix par Pièce",value:w.prixParPieceCDF,onChange:e=>E({...w,prixParPieceCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,showSlider:!0,allowUSDInput:!0,helperText:"Prix unitaire pour vente au détail"})})]})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:ae,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void T("Le nom du produit est requis");if(w.prixAchatCDF<=0)return void T("Le prix d'achat doit être supérieur à zéro");if(w.prixCDF<=0)return void T("Le prix de vente doit être supérieur à zéro");if(w.stock<0)return void T("Le stock ne peut pas être négatif");if(w.stockMin<0)return void T("Le stock minimum ne peut pas être négatif");if(!w.categorie)return void T("La catégorie est requise");const n=Ma(w.prixAchatCDF,w.prixCDF);if(!n.isValid)return void T(n.errorMessage||"Erreur de validation des prix");const i=await la.getSettings(),a=(new Date).toISOString(),r=Ra(w.prixAchatCDF,w.prixCDF,i.tauxChangeUSDCDF);if(b){const n={...b,nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:za(w.prixAchatCDF,i.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:za(w.prixCDF,i.tauxChangeUSDCDF),beneficeUnitaireCDF:r.beneficeUnitaireCDF,beneficeUnitaireUSD:r.beneficeUnitaireUSD,categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,dateModification:a,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:za(w.coutAchatStockCDF,i.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:za(w.prixParPieceCDF,i.tauxChangeUSDCDF)},s=e.map(e=>e.id===b.id?n:e);t(s),await la.setProducts(s),R("Produit mis à jour avec succès")}else{const n={id:Date.now().toString(),nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:za(w.prixAchatCDF,i.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:za(w.prixCDF,i.tauxChangeUSDCDF),beneficeUnitaireCDF:r.beneficeUnitaireCDF,beneficeUnitaireUSD:r.beneficeUnitaireUSD,codeQR:qa(),categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,codeBarres:Va(),dateCreation:a,dateModification:a,quantiteEnStock:w.quantiteEnStock,coutAchatStockCDF:w.coutAchatStockCDF,coutAchatStockUSD:za(w.coutAchatStockCDF,i.tauxChangeUSDCDF),prixParPieceCDF:w.prixParPieceCDF,prixParPieceUSD:za(w.prixParPieceCDF,i.tauxChangeUSDCDF)},s=[...e,n];t(s),await la.setProducts(s),R("Produit créé avec succès")}setTimeout(()=>{ae()},1500)},variant:"contained",children:b?"Mettre à jour":"Créer"})]})]}),i.jsxs(Ae,{open:M,onClose:()=>N(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:"Importer des Produits depuis CSV"}),i.jsxs(Ne,{children:[i.jsx(Oe,{sx:{mb:2},children:"Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification"}),i.jsx(ne,{fullWidth:!0,multiline:!0,rows:10,value:L,onChange:e=>V(e.target.value),placeholder:"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\n1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01",variant:"outlined"})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:()=>N(!1),children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(L.trim())try{const n=Ai.csvToArray(L,Ii),i=Ai.validateCSVData(n,Ii);if(!i.isValid)return void T("Données CSV invalides: "+i.errors.join(", "));const a=n.map((e,t)=>({...e,id:e.id||Date.now().toString()+t,dateCreation:e.dateCreation||(new Date).toISOString(),dateModification:e.dateModification||(new Date).toISOString(),codeQR:e.codeQR||qa(),codeBarres:e.codeBarres||Va()})),r=new Set(e.map(e=>e.id)),s=a.filter(e=>!r.has(e.id)),o=[...e,...s];t(o),await la.setProducts(o),R(`${s.length} produits importés avec succès`),N(!1),V(""),setTimeout(()=>R(""),3e3)}catch(n){T("Erreur lors de l'importation: "+n.message)}else T("Veuillez saisir le contenu CSV à importer")},variant:"contained",children:"Importer"})]})]})]})};class Ha{static async generateSalesReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await la.getSales()).filter(t=>{var n;return new Date(t.datevente).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RV-${e}`))}).length+1).toString().padStart(4,"0");return`RV-${e}-${t}`}static async generateExpenseReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await la.getExpenses()).filter(t=>{var n;return new Date(t.dateDepense).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RD-${e}`))}).length+1).toString().padStart(4,"0");return`RD-${e}-${t}`}static async createSalesReceiptData(e){const t=await la.getSettings();return{type:"sale",numero:e.numeroRecu||await this.generateSalesReceiptNumber(),date:e.datevente,entreprise:t.entreprise,sale:e,vendeur:e.vendeur}}static async createExpenseReceiptData(e){const t=await la.getSettings();return{type:"expense",numero:e.numeroRecu||await this.generateExpenseReceiptNumber(),date:e.dateDepense,entreprise:t.entreprise,expense:e,creePar:e.creePar}}static getPaymentMethodLabel(e){switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}}static formatCurrency(e,t){const{formatCurrency:n}=require("@/utils/currencyUtils.js");return n(e,t)}static formatReceiptDate(e){return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}static async printReceipt(){return new Promise(e=>{try{if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)try{const{ipcRenderer:t}=window.require("electron"),n={silent:!1,printBackground:!0,color:!1,margin:{marginType:"none"},landscape:!1,pagesPerSheet:1,collate:!1,copies:1,header:"",footer:""};t.invoke("print-receipt",n).then(()=>{console.log("Receipt printed successfully via Electron IPC"),e()}).catch(t=>{console.error("Electron IPC print failed:",t),window.print(),e()})}catch(t){console.error("IPC not available, falling back to window.print():",t),window.print(),e()}else window.print(),e()}catch(Sr){console.error("Error during printing:",Sr),window.print(),e()}})}static async shouldAutoPrint(){var e;return(null==(e=(await la.getSettings()).impression)?void 0:e.impressionAutomatique)||!1}static async getPaperSize(){var e;return(null==(e=(await la.getSettings()).impression)?void 0:e.taillePapier)||"thermal"}}const Ya=({receiptData:e,paperSize:t="thermal"})=>{const{sale:n,entreprise:r,numero:s,vendeur:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(a,{textAlign:"center",mb:2,children:[r.logo&&i.jsx(a,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(a,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(a,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE VENTE #",s]}),i.jsx(o,{variant:"body2",children:Ha.formatReceiptDate(n.datevente)})]}),i.jsx(a,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Vendeur:"})," ",l]})}),i.jsxs(a,{mb:2,children:[i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Client:"})," ",n.nomClient]}),n.telephoneClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Téléphone:"})," ",n.telephoneClient]}),n.adresseClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Adresse:"})," ",n.adresseClient]})]}),i.jsx(h,{sx:{my:1}}),i.jsx(me,{children:i.jsxs(he,{size:"small",sx:{"& .MuiTableCell-root":{padding:"4px",border:"none"}},children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx("strong",{children:"Produit"})}),i.jsx(ge,{align:"center",children:i.jsx("strong",{children:"Qté"})}),i.jsx(ge,{align:"right",children:i.jsx("strong",{children:"Prix unit."})}),i.jsx(ge,{align:"right",children:i.jsx("strong",{children:"Sous-total"})})]})}),i.jsx(ye,{children:n.produits.map((e,t)=>i.jsxs(xe,{children:[i.jsx(ge,{sx:{fontSize:"11px"},children:e.nomProduit}),i.jsx(ge,{align:"center",sx:{fontSize:"11px"},children:e.quantite}),i.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:Ha.formatCurrency(e.prixUnitaireCDF,"CDF")}),i.jsx(ge,{align:"right",sx:{fontSize:"11px"},children:Ha.formatCurrency(e.totalCDF,"CDF")})]},t))})]})}),i.jsx(h,{sx:{my:1}}),i.jsx(a,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Méthode de paiement:"})," ",Ha.getPaymentMethodLabel(n.methodePaiement)]})}),i.jsxs(a,{mb:2,children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Sous-total:"}),i.jsx(o,{variant:"body2",children:Ha.formatCurrency(n.totalCDF,"CDF")})]}),i.jsxs(a,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Total CDF:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Ha.formatCurrency(n.totalCDF,"CDF")})]}),n.totalUSD&&i.jsxs(a,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Total USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Ha.formatCurrency(n.totalUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsx(a,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Notes:"})," ",n.notes]})}),i.jsxs(a,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Merci pour votre achat!"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},Ga=({receiptData:e,paperSize:t="thermal"})=>{const{expense:n,entreprise:r,numero:s,creePar:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(a,{textAlign:"center",mb:2,children:[r.logo&&i.jsx(a,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(a,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(a,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE DÉPENSE #",s]}),i.jsx(o,{variant:"body2",children:Ha.formatReceiptDate(n.dateDepense)})]}),i.jsx(a,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Créé par:"})," ",l]})}),i.jsx(h,{sx:{my:1}}),i.jsxs(a,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Description:"})}),i.jsx(o,{variant:"body2",sx:{pl:1,mb:2},children:n.description}),i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Catégorie:"})," ",n.categorie]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(a,{mb:2,children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",mb:1,children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Montant:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Ha.formatCurrency(n.montantCDF,"CDF")})]}),n.montantUSD&&i.jsxs(a,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Équivalent USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Ha.formatCurrency(n.montantUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsxs(a,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Notes:"})}),i.jsx(o,{variant:"body2",sx:{pl:1},children:n.notes})]}),i.jsxs(a,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Reçu de dépense validé"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",s]})]})]})},Ka=({open:e,onClose:t,receiptData:n,paperSize:r="thermal",onPrintSuccess:s})=>{const[l,c]=vt.useState(!1);return n?i.jsxs(Ae,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,className:"receipt-preview-modal",PaperProps:{sx:{maxHeight:"90vh","@media print":{boxShadow:"none",margin:0,maxWidth:"none",maxHeight:"none"}}},children:[i.jsxs(Ie,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center","@media print":{display:"none"}},children:[i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(se,{}),i.jsx(o,{variant:"h6",children:"Aperçu du reçu"})]}),i.jsx(U,{onClick:t,size:"small",children:i.jsx(Ve,{})})]}),i.jsx(Ne,{sx:{p:0,"@media print":{padding:0,margin:0}},children:i.jsx(a,{sx:{p:2,"@media print":{padding:0,margin:0}},children:"sale"===n.type?i.jsx(Ya,{receiptData:n,paperSize:r}):i.jsx(Ga,{receiptData:n,paperSize:r})})}),i.jsxs(Le,{sx:{p:2,gap:1,"@media print":{display:"none"}},children:[i.jsx(I,{onClick:t,variant:"outlined",disabled:l,children:"Fermer"}),i.jsx(I,{onClick:async()=>{if(n){c(!0);try{const e=document.createElement("div");e.className="receipt-print-container",e.style.cssText="\n        position: fixed;\n        top: -9999px;\n        left: -9999px;\n        width: 100%;\n        height: auto;\n        background: white;\n        z-index: 9999;\n      ";const n=document.querySelector(".receipt-container");if(n){const t=n.cloneNode(!0);t.style.cssText="\n          display: block !important;\n          position: static !important;\n          width: 100% !important;\n          height: auto !important;\n          margin: 0 !important;\n          padding: 5mm !important;\n          background: white !important;\n          color: black !important;\n        ",e.appendChild(t)}document.body.appendChild(e),await new Promise(e=>setTimeout(e,200));const i=document.querySelector(".receipt-preview-modal");i&&(i.style.display="none"),await Ha.printReceipt(),document.body.removeChild(e),i&&(i.style.display="block"),s&&s(),setTimeout(()=>{t()},500)}catch(Sr){console.error("Error printing receipt:",Sr)}finally{c(!1)}}},variant:"contained",startIcon:l?i.jsx(qe,{size:16}):i.jsx(Be,{}),disabled:l,children:l?"Impression...":"Imprimer"})]})]}):null},Za=()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState([]),[s,l]=vt.useState([]),[d,u]=vt.useState(""),[m,g]=vt.useState(0),[j,S]=vt.useState(10),[v,b]=vt.useState(!1),[w,E]=vt.useState(!1),[P,T]=vt.useState(null),[k,R]=vt.useState(""),[M,N]=vt.useState(""),[L,q]=vt.useState([]),[B,_]=vt.useState(null),[z,$]=vt.useState(1),[W,X]=vt.useState(""),[Q,H]=vt.useState(""),[Y,G]=vt.useState(""),[K,Z]=vt.useState("cash"),[ae,re]=vt.useState("cash"),[se,oe]=vt.useState("CDF"),[le,de]=vt.useState(""),[ue,De]=vt.useState(""),[be,fe]=vt.useState(!1),[we,Ee]=vt.useState({tauxChangeUSDCDF:2800}),[Ue,Te]=vt.useState(!1),[ke,Re]=vt.useState(null),[Oe,Ve]=vt.useState(!1),Be=ca.getUserPermissions(),Ye=ca.getCurrentUser(),Ge=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{Ke()},[]),vt.useEffect(()=>{Ze()},[e,d]),vt.useEffect(()=>{-1===j&&S(s.length||1)},[s.length,j]);const Ke=async()=>{const e=await la.getSales(),n=await la.getProducts(),i=await la.getSettings();t(e),r(n),Ee(i)},Ze=()=>{let t=e;d&&(t=t.filter(e=>e.nomClient.toLowerCase().includes(d.toLowerCase())||e.id.toLowerCase().includes(d.toLowerCase())||e.vendeur.toLowerCase().includes(d.toLowerCase()))),l(t)},et=()=>{b(!1),tt(),R(""),N("")},tt=()=>{q([]),_(null),$(1),X(""),H(""),G(""),Z("cash"),re("cash"),oe("CDF"),de(""),De(""),fe(!1)},nt=e=>{const t=L.filter((t,n)=>n!==e);q(t)},it=()=>L.reduce((e,t)=>({CDF:wa(e.CDF+t.totalCDF),USD:wa(e.USD+(t.totalUSD||0))}),{CDF:0,USD:0}),at=e=>"USD"===se?{price:e.prixUSD||e.prixCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),currency:"USD",symbol:"$"}:{price:e.prixCDF,currency:"CDF",symbol:""},rt=e=>{const t=at(e);return`${t.symbol}${t.price.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===t.currency?2:0,maximumFractionDigits:"USD"===t.currency?2:0})} ${t.currency}`},st=e=>{T(e),E(!0)},ot=e=>{switch(e){case"cash":default:return i.jsx(f,{});case"banque":return i.jsx(ze,{});case"mobile_money":return i.jsx(He,{})}},lt=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},ct=e.filter(e=>{const t=Ge(e.datevente);if(!t)return!1;const n=new Date;return t.toDateString()===n.toDateString()}),dt=ct.reduce((e,t)=>e+t.totalCDF,0),ut=e.length,mt=e.reduce((e,t)=>e+t.totalCDF,0),ht=ut>0?mt/ut:0;return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Ventes"}),Be.canManageSales&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>{tt(),b(!0),R(""),N("")},children:"Nouvelle Vente"})]}),M&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>N(""),children:M}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),i.jsx(o,{variant:"h6",children:ct.length}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[La(dt,we.tauxChangeUSDCDF).primaryAmount," ",La(dt,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(dt,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Ventes"}),i.jsx(o,{variant:"h6",children:ut}),i.jsxs(o,{variant:"body2",color:"success.main",fontWeight:"medium",children:[La(mt,we.tauxChangeUSDCDF).primaryAmount," ",La(mt,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(mt,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(_e,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Vente Moyenne"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[La(ht,we.tauxChangeUSDCDF).primaryAmount," ",La(ht,we.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(ht,we.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(D,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",children:e.filter(e=>"credit"===e.typeVente).length})]}),i.jsx(ze,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, ID vente ou vendeur...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Produits Vendus"}),i.jsx(ge,{children:"Client"}),i.jsx(ge,{children:"Vendeur"}),i.jsx(ge,{align:"right",children:"Total CDF"}),i.jsx(ge,{align:"right",children:"Total USD"}),i.jsx(ge,{align:"center",children:"Paiement"}),i.jsx(ge,{align:"center",children:"Type"}),i.jsx(ge,{children:"Date"}),i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===j?s:s.slice(m*j,m*j+j)).map(e=>i.jsxs(xe,{hover:!0,onClick:()=>st(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:e.produits.map(e=>e.nomProduit).join(", ")}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["ID: ",e.id," • ",e.produits.length," article",e.produits.length>1?"s":""]})]})}),i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",children:e.nomClient}),e.telephoneClient&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.telephoneClient})]})}),i.jsx(ge,{children:e.vendeur}),i.jsx(ge,{align:"right",children:Na(e.totalCDF,"CDF")}),i.jsx(ge,{align:"right",children:e.totalUSD?Na(e.totalUSD,"USD"):"-"}),i.jsx(ge,{align:"center",children:i.jsx(A,{icon:ot(e.methodePaiement),label:lt(e.methodePaiement),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(A,{label:"cash"===e.typeVente?"Cash":"Crédit",color:"cash"===e.typeVente?"success":"warning",size:"small"})}),i.jsx(ge,{children:(()=>{const t=Ge(e.datevente);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:xa}):"Date invalide"})()}),i.jsx(ge,{align:"center",children:i.jsx(F,{title:"Voir détails",children:i.jsx(U,{size:"small",onClick:()=>st(e),children:i.jsx(f,{fontSize:"small"})})})})]},e.id))})]}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:s.length,rowsPerPage:-1===j?s.length:j,page:-1===j?0:m,onPageChange:(e,t)=>{-1!==j&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);S(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:v,onClose:et,maxWidth:"lg",fullWidth:!0,children:[i.jsx(Ie,{children:"Nouvelle Vente"}),i.jsxs(Ne,{children:[k&&i.jsx(O,{severity:"error",sx:{mb:2},children:k}),M&&i.jsx(O,{severity:"success",sx:{mb:2},children:M}),i.jsxs(a,{sx:{mb:3,p:2,bgcolor:"primary.50",borderRadius:1,border:"1px solid",borderColor:"primary.200"},children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"💱 Devise de la Vente"}),i.jsxs(je,{fullWidth:!0,size:"small",children:[i.jsx(Se,{children:"Sélectionnez la devise pour cette vente"}),i.jsxs(Ce,{value:se,label:"Sélectionnez la devise pour cette vente",onChange:e=>oe(e.target.value),children:[i.jsx(J,{value:"CDF",children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇨🇩"}),i.jsx(o,{children:"CDF (Franc Congolais) - Devise principale"})]})}),i.jsx(J,{value:"USD",children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇺🇸"}),i.jsx(o,{children:"USD (Dollar Américain)"})]})})]})]}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Les prix des produits seront affichés dans la devise sélectionnée"})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Sélection des Produits"}),i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx($e,{options:n.filter(e=>e.stock>0),getOptionLabel:e=>`${e.nom} - ${rt(e)} - Stock: ${e.stock}`,value:B,onChange:(e,t)=>_(t),renderInput:e=>i.jsx(ne,{...e,label:"Produit",fullWidth:!0}),renderOption:(e,t)=>i.jsx(a,{component:"li",...e,children:i.jsxs(a,{sx:{display:"flex",flexDirection:"column",width:"100%"},children:[i.jsx(o,{variant:"body1",sx:{fontWeight:500},children:t.nom}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Prix: ",rt(t)," • Stock: ",t.stock," • Code: ",t.codeQR]})]})})})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(a,{children:[i.jsx(Qa,{value:z,onChange:$,min:1,max:(null==B?void 0:B.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0,label:"Quantité"}),B&&i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",sx:{mt:1},children:["Stock disponible: ",B.stock]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(I,{fullWidth:!0,variant:"contained",onClick:()=>{if(!B)return void R("Veuillez sélectionner un produit");if(z<=0)return void R("La quantité doit être supérieure à 0");if(z>B.stock)return void R("Quantité insuffisante en stock");const e=L.findIndex(e=>e.produitId===B.id);if(e>=0){const t=[...L],n=t[e].quantite+z;if(n>B.stock)return void R("Quantité totale dépasse le stock disponible");t[e]={...t[e],quantite:n,totalCDF:n*B.prixCDF,totalUSD:B.prixUSD?n*B.prixUSD:void 0},q(t)}else{const e={produitId:B.id,nomProduit:B.nom,quantite:z,prixUnitaireCDF:B.prixCDF,prixUnitaireUSD:B.prixUSD,totalCDF:z*B.prixCDF,totalUSD:B.prixUSD?z*B.prixUSD:void 0};q([...L,e])}_(null),$(1),R("")},disabled:!B,children:"Ajouter"})})]}),B&&i.jsx(a,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Produit sélectionné: ",B.nom]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Code: ",B.codeQR," • Catégorie: ",B.categorie]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",color:"primary",sx:{fontWeight:"bold"},children:["Prix unitaire: ",rt(B)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Devise: ",se]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Total: ",(()=>{const e=at(B),t=e.price*z;return`${e.symbol}${t.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===e.currency?2:0,maximumFractionDigits:"USD"===e.currency?2:0})} ${e.currency}`})()]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[z," × ",rt(B)]})]})]})})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:["Panier (",L.length," articles) - Devise: ",se]}),0===L.length?i.jsx(O,{severity:"info",children:"Aucun produit ajouté"}):i.jsxs(p,{children:[L.map((e,t)=>{var r;const s=(e=>{if("USD"===se){const t=e.prixUnitaireUSD||e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),n=e.totalUSD||e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800);return{unitPrice:`$${t.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`,total:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`}}return{unitPrice:`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF`,total:`${e.totalCDF.toLocaleString("fr-FR")} CDF`}})(e);return i.jsxs(ft.Fragment,{children:[i.jsxs(x,{children:[i.jsx(y,{primary:i.jsx(o,{variant:"subtitle1",sx:{fontWeight:500},children:e.nomProduit}),secondary:`Prix unitaire: ${s.unitPrice}`}),i.jsx(V,{children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(Qa,{value:e.quantite,onChange:e=>((e,t)=>{if(t<=0)return void nt(e);const i=L[e],a=n.find(e=>e.id===i.produitId);if(a&&t>a.stock)return void R(`Quantité maximale disponible: ${a.stock}`);const r=[...L];r[e]={...i,quantite:t,totalCDF:t*i.prixUnitaireCDF,totalUSD:i.prixUnitaireUSD?t*i.prixUnitaireUSD:void 0},q(r),R("")})(t,e),min:1,max:(null==(r=n.find(t=>t.id===e.produitId))?void 0:r.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0}),i.jsx(o,{variant:"subtitle1",sx:{minWidth:"120px",textAlign:"right",fontWeight:"bold",color:"primary.main"},children:s.total}),i.jsx(U,{size:"small",color:"error",onClick:()=>nt(t),title:"Supprimer l'article",children:i.jsx(_e,{fontSize:"small"})})]})})]}),t<L.length-1&&i.jsx(h,{})]},t)}),i.jsx(h,{sx:{my:2}}),i.jsx(x,{sx:{bgcolor:"primary.50",borderRadius:1},children:i.jsx(y,{primary:i.jsxs(o,{variant:"h6",sx:{fontWeight:"bold",color:"primary.main"},children:["Total: ",(()=>{const e=it();return"USD"===se?`$${e.USD.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`:`${e.CDF.toLocaleString("fr-FR")} CDF`})()]}),secondary:i.jsx(o,{variant:"body2",color:"text.secondary",children:"USD"===se?`≈ ${Na(it().CDF,"CDF")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`:`≈ ${Na(it().USD,"USD")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`})})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations Client"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Nom du client",placeholder:"Client (par défaut)",value:W,onChange:e=>X(e.target.value)})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:Q,onChange:e=>H(e.target.value)})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:Y,onChange:e=>G(e.target.value)})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations de Paiement"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{component:"fieldset",children:[i.jsx(ve,{component:"legend",children:"Type de vente"}),i.jsxs(We,{row:!0,value:ae,onChange:e=>re(e.target.value),children:[i.jsx(Xe,{value:"cash",control:i.jsx(Qe,{}),label:"Cash"}),i.jsx(Xe,{value:"credit",control:i.jsx(Qe,{}),label:"Crédit"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:K,label:"Méthode de paiement",onChange:e=>Z(e.target.value),children:[i.jsx(J,{value:"cash",children:"Cash"}),i.jsx(J,{value:"banque",children:"Banque"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),"credit"===ae&&i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date d'échéance",type:"date",value:le,onChange:e=>de(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:ue,onChange:e=>De(e.target.value)})})]})]})]})]}),i.jsxs(Le,{children:[i.jsx(Xe,{control:i.jsx(Je,{checked:be,onChange:e=>fe(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),Oe&&i.jsxs(a,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(qe,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(I,{onClick:et,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var i;if(0===L.length)return void R("Veuillez ajouter au moins un produit");if("credit"===ae&&!le)return void R("La date d'échéance est requise pour les ventes à crédit");const a=it(),s=(new Date).toISOString(),o=be||(null==(i=null==we?void 0:we.impression)?void 0:i.impressionAutomatique)||!1,l=o?await Ha.generateSalesReceiptNumber():void 0,c={id:`VTE-${Date.now()}`,produits:L,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:Y.trim()||void 0,totalCDF:a.CDF,totalUSD:a.USD,methodePaiement:K,typeVente:ae,datevente:s,vendeur:(null==Ye?void 0:Ye.nom)||"Inconnu",notes:ue.trim()||void 0,numeroRecu:l},d=n.map(e=>{const t=L.find(t=>t.produitId===e.id);return t?{...e,stock:e.stock-t.quantite,dateModification:s}:e}),u=[...e,c];if(t(u),r(d),await la.setSales(u),await la.setProducts(d),"credit"===ae){const e={id:`DET-${Date.now()}`,venteId:c.id,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:Y.trim()||void 0,montantTotalCDF:a.CDF,montantTotalUSD:a.USD,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:a.CDF,montantRestantUSD:a.USD,dateCreation:s,dateEcheance:le,statut:"active",statutPaiement:"impaye",paiements:[],notes:ue.trim()||void 0,deviseVente:se},t=await la.getDebts();await la.setDebts([...t,e])}if(N("Vente enregistrée avec succès"),o)try{Ve(!0);const e=await Ha.createSalesReceiptData(c);Re(e),Te(!0)}catch(m){console.error("Erreur lors de la génération du reçu:",m),R("Erreur lors de la génération du reçu")}finally{Ve(!1)}setTimeout(()=>{et()},1500)},variant:"contained",disabled:0===L.length||Oe,children:"Enregistrer la Vente"})]})]}),i.jsxs(Ae,{open:w,onClose:()=>E(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:"Détails de la Vente"}),i.jsx(Ne,{children:P&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Vendus:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:P.produits.map(e=>e.nomProduit).join(", ")})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:P.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date:"}),i.jsx(o,{variant:"body1",children:Ut(new Date(P.datevente),"dd/MM/yyyy HH:mm",{locale:xa})})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",children:P.nomClient}),P.telephoneClient&&i.jsx(o,{variant:"body2",color:"text.secondary",children:P.telephoneClient})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Vendeur:"}),i.jsx(o,{variant:"body1",children:P.vendeur})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Produits:"}),i.jsxs(p,{dense:!0,children:[P.produits.map((e,t)=>i.jsxs(x,{children:[i.jsx(y,{primary:e.nomProduit,secondary:i.jsxs(a,{children:[i.jsxs(o,{variant:"body2",component:"span",children:[e.quantite," × ",Na(e.prixUnitaireCDF,"CDF")]}),i.jsxs(o,{variant:"caption",display:"block",color:"text.secondary",children:["≈ ",e.quantite," × ",Na(e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})}),i.jsxs(a,{textAlign:"right",children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:Na(e.totalCDF,"CDF")}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ ",Na(e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),"USD")]})]})]},t)),i.jsx(h,{}),i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(a,{children:[i.jsxs(o,{variant:"h6",color:"primary",fontWeight:"medium",children:["Total: ",La(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryAmount," ",La(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",La(P.totalCDF,(null==we?void 0:we.tauxChangeUSDCDF)||2800).secondaryAmount]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Taux de change: 1 USD = ",(null==we?void 0:we.tauxChangeUSDCDF)||2800," CDF"]})]})})})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Méthode de paiement:"}),i.jsx(A,{icon:ot(P.methodePaiement),label:lt(P.methodePaiement)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Type de vente:"}),i.jsx(A,{label:"cash"===P.typeVente?"Cash":"Crédit",color:"cash"===P.typeVente?"success":"warning"})]}),P.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:P.notes})]})]})}),i.jsx(Le,{children:i.jsx(I,{onClick:()=>E(!1),children:"Fermer"})})]}),i.jsx(Ka,{open:Ue,onClose:()=>Te(!1),receiptData:ke,onPrintSuccess:()=>{N("Reçu imprimé avec succès"),setTimeout(()=>N(""),3e3)}})]})},er=()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState("all"),[m,g]=vt.useState("all"),[j,S]=vt.useState(0),[C,v]=vt.useState(10),[b,w]=vt.useState(!1),[E,P]=vt.useState(!1),[T,k]=vt.useState(null),[R,M]=vt.useState(0),[N,L]=vt.useState("cash"),[V,q]=vt.useState(""),[B,z]=vt.useState(""),[W,X]=vt.useState(""),[Q,H]=vt.useState({tauxChangeUSDCDF:2800}),[G,K]=vt.useState([]),[Z,ae]=vt.useState(!1),[re,oe]=vt.useState("CDF"),de=ca.getUserPermissions(),ue=e=>G.find(t=>t.id===e.venteId),De=e=>{const t=ue(e);return t&&t.produits&&0!==t.produits.length?t.produits.map(e=>`${e.nomProduit} (x${e.quantite})`).join(", "):"Produits non disponibles"},ve=e=>{if(e.nomClient&&""!==e.nomClient.trim()&&"Client"!==e.nomClient)return e.nomClient;const t=ue(e);return t&&t.nomClient&&""!==t.nomClient.trim()&&"Client"!==t.nomClient?t.nomClient:"Client"},be=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};vt.useEffect(()=>{fe();(async()=>{0===(await la.getDebts()).length&&(console.log("No debts found, forcing initialization..."),await la.forceInitializeDebts(),setTimeout(()=>fe(),500))})()},[]),vt.useEffect(()=>{we()},[e,s,d,m]),vt.useEffect(()=>{-1===C&&v(n.length||1)},[n.length,C]);const fe=async()=>{try{const e=await la.getDebts(),n=await la.getSettings(),i=await la.getSales(),a=e.filter(e=>e.id&&e.venteId?(e.nomClient&&""!==e.nomClient.trim()||(e.nomClient="Client"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,!0):(console.warn("Invalid debt record missing required fields:",e),!1)).map(e=>{try{const t=We(e);if("paid"===t.statut)return t;const n=new Date,i=be(t.dateEcheance);return t.montantRestantCDF<=0?{...t,statut:"paid",statutPaiement:"paye"}:i&&Mt(n,i)?{...t,statut:"overdue"}:{...t,statut:"active"}}catch(t){return console.warn("Error processing debt status:",e,t),e}});t(a),H(n),K(i),await la.setDebts(a)}catch(e){console.error("Error loading debt data:",e),z("Erreur lors du chargement des données de dette.")}},we=()=>{try{let t=e;if(s&&s.trim()){const e=s.toLowerCase().trim();t=t.filter(t=>{var n,i,a,r;try{const s=(null==(n=t.nomClient)?void 0:n.toLowerCase())||"",o=(null==(i=t.id)?void 0:i.toLowerCase())||"",l=(null==(a=t.venteId)?void 0:a.toLowerCase())||"",c=(null==(r=t.telephoneClient)?void 0:r.toLowerCase())||"",d=De(t).toLowerCase();return s.includes(e)||o.includes(e)||l.includes(e)||c.includes(e)||d.includes(e)}catch(s){return console.warn("Error filtering debt:",t,s),!1}})}"all"!==d&&(t=t.filter(e=>e.statut===d)),"all"!==m&&(t=t.filter(e=>e.statutPaiement===m)),r(t)}catch(t){console.error("Error in filterDebts:",t),r(e),z("Erreur lors de la recherche. Affichage de toutes les dettes.")}},Ee=e=>{switch(e){case"active":return"primary";case"overdue":return"error";case"paid":return"success";default:return"default"}},Pe=e=>{switch(e){case"active":return"Active";case"overdue":return"En retard";case"paid":return"Payée";default:return e}},Te=e=>{switch(e){case"active":return i.jsx(Ye,{});case"overdue":return i.jsx($,{});case"paid":return i.jsx(_,{});default:return i.jsx(D,{})}},ke=e=>{switch(e){case"paye":return"Payé";case"impaye":return"Impayé";default:return e}},Re=e=>{switch(e){case"paye":return i.jsx(Ge,{});case"impaye":return i.jsx(et,{});default:return i.jsx(D,{})}},Oe=()=>{w(!1),k(null),M(0),L("cash"),q(""),oe("CDF"),z(""),X("")},Ve=e=>{k(e),P(!0)},qe=()=>{P(!1),k(null),ae(!1),M(0),q(""),oe("CDF"),z(""),X("")},Be=async()=>{if(!T)return;if(R<=0)return void z("Le montant doit être supérieur à 0");if(R>T.montantRestantCDF)return void z("Le montant ne peut pas dépasser le montant restant");const n=(new Date).toISOString();let i,a;"USD"===re?(a=R/Q.tauxChangeUSDCDF,i=R):(i=R,a=R/Q.tauxChangeUSDCDF);const r={id:`PAY-${Date.now()}`,montantCDF:i,montantUSD:a,methodePaiement:N,datePaiement:n,notes:V.trim()||void 0,deviseOriginale:re},s={...T,montantPayeCDF:T.montantPayeCDF+R,montantPayeUSD:(T.montantPayeUSD||0)+R/Q.tauxChangeUSDCDF,paiements:[...T.paiements,r]},o=We(s),l={...o,statut:o.montantRestantCDF<=0?"paid":T.statut,statutPaiement:Je(o)},c=e.map(e=>e.id===T.id?l:e);t(c),await la.setDebts(c),k(l),X("Paiement enregistré avec succès"),Z?setTimeout(()=>{ae(!1),M(0),q(""),oe("CDF"),z(""),X("")},2e3):setTimeout(()=>{Oe()},1500)},_e=e=>{switch(e){case"cash":default:return i.jsx(f,{});case"banque":return i.jsx(ze,{});case"mobile_money":return i.jsx(He,{})}},$e=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},We=e=>{const t=e.paiements.reduce((e,t)=>e+t.montantCDF,0),n=e.paiements.reduce((e,t)=>e+(t.montantUSD||0),0),i=e.paiements.length>0?t:e.montantPayeCDF,a=e.paiements.length>0?n:e.montantPayeUSD||0,r=e.montantTotalCDF-i,s=e.montantTotalUSD?e.montantTotalUSD-a:void 0,o=e.montantTotalUSD||e.montantTotalCDF/Q.tauxChangeUSDCDF,l=a||i/Q.tauxChangeUSDCDF,c=void 0!==s?Math.max(0,s):Math.max(0,r/Q.tauxChangeUSDCDF);return{...e,montantTotalCDF:e.montantTotalCDF,montantTotalUSD:o,montantPayeCDF:i,montantPayeUSD:l,montantRestantCDF:Math.max(0,r),montantRestantUSD:c}},Qe=e=>0===e.montantTotalCDF?100:Math.min(100,e.montantPayeCDF/e.montantTotalCDF*100),Je=e=>e.montantPayeCDF>=e.montantTotalCDF?"paye":"impaye",tt=e=>"paye"===Je(e)?"Payé":"Impayé",nt=e=>"paye"===Je(e)?"success":"error",it=e=>"paye"===Je(e)?i.jsx(_,{fontSize:"small"}):i.jsx(Ze,{fontSize:"small"}),at=e.filter(e=>"active"===e.statut),rt=e.filter(e=>"overdue"===e.statut),st=e.filter(e=>"paye"===e.statutPaiement),ot=e.filter(e=>"impaye"===e.statutPaiement),lt=e.reduce((e,t)=>e+t.montantTotalCDF,0),ct=e.reduce((e,t)=>e+t.montantRestantCDF,0);return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dettes"}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>{const e=`SmartBoutique_Dettes_${(new Date).toISOString().split("T")[0]}.csv`;Ai.downloadCSV(n,Oi,e),X("Dettes exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>X(""),3e3)},children:"Exporter les Dettes"})]}),W&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>X(""),children:W}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dettes Actives"}),i.jsx(o,{variant:"h6",children:at.length}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",children:[La(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",La(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(at.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(Ye,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Retard"}),i.jsx(o,{variant:"h6",color:"error.main",children:rt.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[La(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryAmount," ",La(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(rt.reduce((e,t)=>e+t.montantRestantCDF,0),Q.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx($,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Statut Payé"}),i.jsx(o,{variant:"h6",color:"success.main",children:st.length}),i.jsxs(o,{variant:"body2",color:"error",children:["Impayé: ",ot.length]})]}),i.jsx(Ge,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Restant"}),i.jsxs(o,{variant:"h6",fontWeight:"medium",children:[La(ct,Q.tauxChangeUSDCDF).primaryAmount," ",La(ct,Q.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",La(ct,Q.tauxChangeUSDCDF).secondaryAmount," sur ",La(lt,Q.tauxChangeUSDCDF).primaryAmount," ",La(lt,Q.tauxChangeUSDCDF).primaryCurrency]})]}),i.jsx(D,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, produit, ID dette ou ID vente...",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Statut"}),i.jsxs(Ce,{value:d,label:"Statut",onChange:e=>u(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"active",children:"Actives"}),i.jsx(J,{value:"overdue",children:"En retard"}),i.jsx(J,{value:"paid",children:"Payées"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Statut Paiement"}),i.jsxs(Ce,{value:m,label:"Statut Paiement",onChange:e=>g(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"paye",children:"Payé"}),i.jsx(J,{value:"impaye",children:"Impayé"})]})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Client"}),i.jsx(ge,{children:"Montant Dû"}),i.jsx(ge,{align:"right",children:"Payé"}),i.jsx(ge,{align:"right",children:"Restant"}),i.jsx(ge,{align:"center",children:"Progression"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{align:"center",children:"Statut Paiement"}),i.jsx(ge,{children:"Échéance"}),de.canManageDebts&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===C?n:n.slice(j*C,j*C+C)).map(e=>{try{const t=Qe(e),n="overdue"===e.statut;return i.jsxs(xe,{hover:!0,onClick:()=>Ve(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"h6",fontWeight:"bold",color:"primary",gutterBottom:!0,children:ve(e)}),e.telephoneClient&&i.jsxs(o,{variant:"body2",color:"text.secondary",display:"block",sx:{mb:.5},children:["📞 ",e.telephoneClient]}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",display:"block",sx:{mb:.5},children:["🛍️ Produits: ",De(e)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["Créé le: ",(()=>{try{const t=be(e.dateCreation);return t?Ut(t,"dd/MM/yyyy",{locale:xa}):"Date invalide"}catch(t){return console.warn("Error formatting creation date:",e.dateCreation,t),"Date invalide"}})()," • ID: ",e.venteId||"N/A"]})]})}),i.jsx(ge,{align:"right",children:Na(e.montantTotalCDF,"CDF")}),i.jsx(ge,{align:"right",children:Na(e.montantPayeCDF,"CDF")}),i.jsx(ge,{align:"right",children:i.jsx(o,{variant:"body2",color:e.montantRestantCDF>0?"error":"success.main",children:Na(e.montantRestantCDF,"CDF")})}),i.jsx(ge,{align:"center",children:i.jsxs(a,{sx:{width:100},children:[i.jsx(le,{variant:"determinate",value:t,color:100===t?"success":n?"error":"primary"}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(t),"%"]})]})}),i.jsx(ge,{align:"center",children:i.jsx(A,{icon:Te(e.statut),label:Pe(e.statut),color:Ee(e.statut),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(A,{icon:it(e),label:tt(e),color:nt(e),size:"small"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:n?"error":"text.primary",children:(()=>{const t=be(e.dateEcheance);return t?Ut(t,"dd/MM/yyyy",{locale:xa}):"Date invalide"})()})}),de.canManageDebts&&i.jsx(ge,{align:"center",children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Voir détails",children:i.jsx(U,{size:"small",onClick:()=>Ve(e),children:i.jsx(se,{fontSize:"small"})})}),"paid"!==e.statut&&i.jsx(F,{title:"Ajouter paiement",children:i.jsx(U,{size:"small",color:"primary",onClick:()=>(e=>{k(e),M(e.montantRestantCDF),L("cash"),q(""),oe("CDF"),w(!0),z(""),X("")})(e),children:i.jsx(Y,{fontSize:"small"})})})]})})]},e.id)}catch(t){return console.error("Error rendering debt row:",e,t),i.jsx(xe,{children:i.jsx(ge,{colSpan:de.canManageDebts?9:8,children:i.jsxs(o,{color:"error",variant:"body2",children:["Erreur d'affichage pour cette dette. ID: ",e.id||"Inconnu"]})})},e.id||`error-${Math.random()}`)}})})]}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===C?n.length:C,page:-1===C?0:j,onPageChange:(e,t)=>{-1!==C&&S(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);v(t),S(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===C?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:b,onClose:Oe,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ie,{children:"Ajouter un Paiement"}),i.jsxs(Ne,{children:[B&&i.jsx(O,{severity:"error",sx:{mb:2},children:B}),W&&i.jsx(O,{severity:"success",sx:{mb:2},children:W}),T&&i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Client: ",ve(T)]}),i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Na(T.montantRestantCDF,"CDF")]})]}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Xa,{label:"Montant du paiement",value:R,onChange:e=>M(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:N,label:"Méthode de paiement",onChange:e=>L(e.target.value),children:[i.jsx(J,{value:"cash",children:"Cash"}),i.jsx(J,{value:"banque",children:"Banque"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:V,onChange:e=>q(e.target.value)})})]})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:Oe,children:"Annuler"}),i.jsx(I,{onClick:Be,variant:"contained",disabled:!T||R<=0,children:"Enregistrer le Paiement"})]})]}),i.jsxs(Ae,{open:E,onClose:qe,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:"Détails de la Dette"}),i.jsx(Ne,{children:T&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Achetés à Crédit:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:De(T)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"ID Dette:"}),i.jsx(o,{variant:"body1",children:T.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:T.venteId})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",sx:{fontWeight:"medium"},children:ve(T)}),T.telephoneClient&&i.jsxs(a,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Téléphone:"}),i.jsx(o,{variant:"body2",color:"primary",children:T.telephoneClient})]}),T.adresseClient&&i.jsxs(a,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Adresse:"}),i.jsx(o,{variant:"body2",color:"text.primary",children:T.adresseClient})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut:"}),i.jsx(A,{icon:Te(T.statut),label:Pe(T.statut),color:Ee(T.statut)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut Paiement:"}),i.jsx(a,{display:"flex",alignItems:"center",gap:1,sx:{mt:.5},children:de.canManageDebts?i.jsx(Xe,{control:i.jsx(Ke,{checked:"paye"===T.statutPaiement,onChange:n=>(async(n,i)=>{if(!de.canManageDebts)return;if(!window.confirm(`Êtes-vous sûr de vouloir changer le statut de paiement à "${ke(i)}" ?\n\nCette action modifiera le statut de paiement de la dette.`))return;const a=e.map(e=>e.id===n?{...e,statutPaiement:i}:e);t(a),await la.setDebts(a),T&&T.id===n&&k({...T,statutPaiement:i}),X(`Statut de paiement mis à jour: ${ke(i)}`),setTimeout(()=>{X("")},3e3)})(T.id,n.target.checked?"paye":"impaye"),color:"success",size:"small"}),label:i.jsxs(a,{display:"flex",alignItems:"center",gap:.5,children:[Re(T.statutPaiement),i.jsx(o,{variant:"body2",children:ke(T.statutPaiement)})]}),labelPlacement:"end"}):i.jsx(A,{icon:Re(T.statutPaiement),label:ke(T.statutPaiement),color:(e=>{switch(e){case"paye":return"success";case"impaye":return"error";default:return"default"}})(T.statutPaiement)})})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Dû:"}),i.jsx(o,{variant:"body1",color:"primary",sx:{fontWeight:"bold"},children:Na(T.montantTotalCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Payé:"}),i.jsx(o,{variant:"body1",color:"success.main",children:Na(T.montantPayeCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Restant:"}),i.jsx(o,{variant:"body1",color:"error",children:Na(T.montantRestantCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Progression du Paiement:"}),i.jsxs(a,{sx:{width:"100%",mb:1},children:[i.jsx(le,{variant:"determinate",value:Qe(T),color:100===Qe(T)?"success":"overdue"===T.statut?"error":"primary",sx:{height:10,borderRadius:5}}),i.jsxs(a,{sx:{display:"flex",justifyContent:"space-between",mt:1},children:[i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(Qe(T)),"% payé"]}),i.jsx(o,{variant:"caption",color:"text.secondary",children:T.montantRestantCDF>0?`${Na(T.montantRestantCDF,"CDF")} restant`:"Entièrement payé"})]})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date de Création:"}),i.jsx(o,{variant:"body1",children:(()=>{const e=be(T.dateCreation);return e?Ut(e,"dd/MM/yyyy",{locale:xa}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date d'Échéance:"}),i.jsx(o,{variant:"body1",color:"overdue"===T.statut?"error":"text.primary",children:(()=>{const e=be(T.dateEcheance);return e?Ut(e,"dd/MM/yyyy",{locale:xa}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Historique des Paiements (",T.paiements.length,")"]}),0===T.paiements.length?i.jsx(O,{severity:"info",children:"Aucun paiement enregistré"}):i.jsx(p,{dense:!0,children:T.paiements.map((e,t)=>i.jsxs(ft.Fragment,{children:[i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[_e(e.methodePaiement),i.jsx(o,{variant:"body2",children:"USD"===e.deviseOriginale?`${Na(e.montantUSD||0,"USD")} (≈ ${Na(e.montantCDF,"CDF")})`:`${Na(e.montantCDF,"CDF")} (≈ ${Na(e.montantUSD||0,"USD")})`}),i.jsx(A,{label:$e(e.methodePaiement),size:"small"})]}),secondary:i.jsxs(a,{children:[i.jsx(o,{variant:"caption",children:(()=>{const t=be(e.datePaiement);return t?Ut(t,"dd/MM/yyyy HH:mm",{locale:xa}):"Date invalide"})()}),e.notes&&i.jsx(o,{variant:"caption",display:"block",children:e.notes})]})})}),t<T.paiements.length-1&&i.jsx(h,{})]},e.id))})]}),de.canManageDebts&&"paid"!==T.statut&&Z&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(c,{elevation:2,sx:{p:2,mt:2,bgcolor:"background.default"},children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(Y,{}),"Ajouter un Paiement"]}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Na(T.montantRestantCDF,"CDF")]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Montant à payer",value:R,onChange:e=>M(e),min:0,max:T.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,onCurrencyModeChange:e=>oe(e)})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:N,onChange:e=>L(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"cash",children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(f,{fontSize:"small"}),"Cash"]})}),i.jsx(J,{value:"mobile_money",children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(He,{fontSize:"small"}),"Mobile Money"]})}),i.jsx(J,{value:"banque",children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(ze,{fontSize:"small"}),"Banque"]})})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes (optionnel)",value:V,onChange:e=>q(e.target.value),multiline:!0,rows:2,placeholder:"Ajouter des notes sur ce paiement..."})}),B&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",children:B})}),W&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"success",children:W})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(a,{display:"flex",gap:1,justifyContent:"flex-end",children:[i.jsx(I,{variant:"outlined",onClick:()=>{ae(!1),M(0),q(""),oe("CDF"),z(""),X("")},children:"Annuler"}),i.jsx(I,{variant:"contained",color:"success",onClick:()=>{M(T.montantRestantCDF),q("Paiement complet")},startIcon:i.jsx(Y,{}),children:"Paiement Complet"}),i.jsx(I,{variant:"contained",color:"primary",onClick:Be,startIcon:i.jsx(Y,{}),disabled:R<=0,children:"Payer"})]})})]})]})}),T.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:T.notes})]})]})}),i.jsxs(Le,{children:[de.canManageDebts&&T&&"paid"!==T.statut&&!Z&&i.jsx(I,{variant:"contained",color:"primary",onClick:()=>{ae(!0),M(0),L("cash"),q(""),z(""),X("")},startIcon:i.jsx(Y,{}),sx:{mr:1},children:"Ajouter Paiement"}),i.jsx(I,{onClick:qe,children:"Fermer"})]})]})]})},tr=(e,t="dd/MM/yyyy")=>{try{const n="string"==typeof e?new Date(e):e;return Lt(n)?Ut(n,t,{locale:xa}):"Date invalide"}catch(Sr){return console.warn("Invalid date value:",e,Sr),"Date invalide"}},nr=()=>{var e;try{const[t,n]=vt.useState([]),[r,s]=vt.useState([]),[l,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[S,C]=vt.useState(!1),[D,v]=vt.useState(null),[b,w]=vt.useState({description:"",montantCDF:0,categorie:"",dateDepense:tr(new Date,"yyyy-MM-dd"),notes:""}),[E,P]=vt.useState(""),[T,k]=vt.useState(""),[R,M]=vt.useState({tauxChangeUSDCDF:2800}),[N,L]=vt.useState(!1),[V,B]=vt.useState(!1),[_,z]=vt.useState(null),[$,W]=vt.useState(!1),X=ca.getUserPermissions(),Q=ca.getCurrentUser(),H=["Loyer","Électricité","Eau","Internet","Téléphone","Transport","Carburant","Fournitures de bureau","Marketing","Maintenance","Assurance","Taxes","Salaires","Formation","Équipement","Autres"];vt.useEffect(()=>{Y()},[]),vt.useEffect(()=>{G()},[t,l,u,h]),vt.useEffect(()=>{-1===y&&j(r.length||1)},[r.length,y]);const Y=async()=>{const e=await la.getExpenses(),t=await la.getSettings();n(e),M(t)},G=()=>{let e=t;if(l&&(e=e.filter(e=>e.description.toLowerCase().includes(l.toLowerCase())||e.categorie.toLowerCase().includes(l.toLowerCase())||e.notes&&e.notes.toLowerCase().includes(l.toLowerCase()))),u&&(e=e.filter(e=>e.categorie===u)),h){const t=new Date;let n,i;switch(h){case"today":n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),i=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59);break;case"this_month":n=It(t),i=Nt(t);break;case"last_month":const e=new Date(t.getFullYear(),t.getMonth()-1,1);n=It(e),i=Nt(e);break;default:n=new Date(0),i=new Date}e=e.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,{start:n,end:i})})}s(e)},K=e=>{e?(v(e),w({description:e.description,montantCDF:e.montantCDF,categorie:e.categorie,dateDepense:tr(e.dateDepense,"yyyy-MM-dd"),notes:e.notes||""})):(v(null),w({description:"",montantCDF:0,categorie:"",dateDepense:tr(new Date,"yyyy-MM-dd"),notes:""})),C(!0),P(""),k("")},Z=()=>{C(!1),v(null),P(""),k(""),L(!1)},ae=async()=>{var e;if(b.description.trim())if(b.montantCDF<=0)P("Le montant doit être supérieur à 0");else if(b.categorie)if(b.dateDepense){if(D){const e={...D,description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/R.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0},i=t.map(t=>t.id===D.id?e:t);n(i),await la.setExpenses(i),k("Dépense mise à jour avec succès")}else{const a=N||(null==(e=R.impression)?void 0:e.impressionAutomatique)||!1,r=a?await Ha.generateExpenseReceiptNumber():void 0,s={id:Date.now().toString(),description:b.description.trim(),montantCDF:b.montantCDF,montantUSD:b.montantCDF/R.tauxChangeUSDCDF,categorie:b.categorie,dateDepense:b.dateDepense,notes:b.notes.trim()||void 0,creePar:(null==Q?void 0:Q.nom)||"Inconnu",numeroRecu:r},o=[...t,s];if(n(o),await la.setExpenses(o),k("Dépense créée avec succès"),a)try{W(!0);const e=await Ha.createExpenseReceiptData(s);z(e),B(!0)}catch(i){console.error("Erreur lors de la génération du reçu:",i),P("Erreur lors de la génération du reçu")}finally{W(!1)}}setTimeout(()=>{Z()},1500)}else P("La date est requise");else P("La catégorie est requise");else P("La description est requise")},re=async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${e.description}" ?`)){const i=t.filter(t=>t.id!==e.id);n(i),await la.setExpenses(i),k("Dépense supprimée avec succès"),setTimeout(()=>k(""),3e3)}},se=(e,t)=>{-1!==y&&g(t)},oe=e=>{const t=parseInt(e.target.value,10);j(t),g(0)},le=new Date,de={start:It(le),end:Nt(le)},ue=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&t.toDateString()===le.toDateString()}),De=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,de)}),ve=t.length,be=ue.reduce((e,t)=>e+t.montantCDF,0),fe=De.reduce((e,t)=>e+t.montantCDF,0),we=t.reduce((e,t)=>e+t.montantCDF,0),Ee=t.reduce((e,t)=>(e[t.categorie]=(e[t.categorie]||0)+t.montantUSD,e),{}),Te=Object.entries(Ee).sort(([,e],[,t])=>t-e).slice(0,5),ke=()=>{const e=`SmartBoutique_Depenses_${tr(new Date,"yyyy-MM-dd")}.csv`;Ai.downloadCSV(r,Vi,e),k("Dépenses exportées en CSV avec succès (compatible Excel)"),setTimeout(()=>k(""),3e3)};return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dépenses"}),i.jsxs(a,{display:"flex",gap:2,children:[i.jsx(I,{variant:"outlined",startIcon:i.jsx(Ue,{}),onClick:ke,children:"Exporter les Dépenses"}),X.canManageExpenses&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>K(),children:"Nouvelle Dépense"})]})]}),T&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>k(""),children:T}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du jour"}),i.jsx(o,{variant:"h6",children:ue.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[La(be,R.tauxChangeUSDCDF).primaryAmount," ",La(be,R.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(be,R.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(f,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du mois"}),i.jsx(o,{variant:"h6",children:De.length}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[La(fe,R.tauxChangeUSDCDF).primaryAmount," ",La(fe,R.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(fe,R.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(tt,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",children:ve}),i.jsxs(o,{variant:"body2",color:"error",fontWeight:"medium",children:[La(we,R.tauxChangeUSDCDF).primaryAmount," ",La(we,R.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["≈ $",La(we,R.tauxChangeUSDCDF).secondaryAmount]})]}),i.jsx(nt,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),i.jsx(o,{variant:"h6",children:Object.keys(Ee).length}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Top: ",(null==(e=Te[0])?void 0:e[0])||"N/A"]})]}),i.jsx(it,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par description, catégorie ou notes...",value:l,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Catégorie"}),i.jsxs(Ce,{value:u,label:"Catégorie",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),H.map(e=>i.jsx(J,{value:e,children:e},e))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Période"}),i.jsxs(Ce,{value:h,label:"Période",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les périodes"}),i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"})]})]})})]})}),Te.length>0&&i.jsxs(c,{sx:{p:2,mb:3},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Top 5 Catégories"}),i.jsx(ce,{container:!0,spacing:1,children:Te.map(([e,t])=>i.jsx(ce,{item:!0,children:i.jsx(A,{label:`${e}: ${Na(t,"CDF")}`,color:"primary",variant:"outlined"})},e))})]}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Description"}),i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Montant CDF"}),i.jsx(ge,{align:"right",children:"Montant USD"}),i.jsx(ge,{children:"Date"}),i.jsx(ge,{children:"Créé par"}),X.canManageExpenses&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===y?r:r.slice(x*y,x*y+y)).map(e=>i.jsxs(xe,{hover:!0,onClick:()=>K(e),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",children:e.description}),e.notes&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.notes})]})}),i.jsx(ge,{children:i.jsx(A,{label:e.categorie,size:"small"})}),i.jsx(ge,{align:"right",children:Na(e.montantCDF,"CDF")}),i.jsx(ge,{align:"right",children:e.montantUSD?Na(e.montantUSD,"USD"):"-"}),i.jsx(ge,{children:tr(e.dateDepense)}),i.jsx(ge,{children:e.creePar}),X.canManageExpenses&&i.jsx(ge,{align:"center",children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>K(e),children:i.jsx(Re,{fontSize:"small"})})}),ca.hasRole(["super_admin","admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>re(e),children:i.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:r.length,rowsPerPage:-1===y?r.length:y,page:-1===y?0:x,onPageChange:se,onRowsPerPageChange:oe,labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:S,onClose:Z,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:D?"Modifier la Dépense":"Nouvelle Dépense"}),i.jsxs(Ne,{children:[E&&i.jsx(O,{severity:"error",sx:{mb:2},children:E}),T&&i.jsx(O,{severity:"success",sx:{mb:2},children:T}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description *",value:b.description,onChange:e=>w({...b,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Xa,{label:"Montant de la dépense",value:b.montantCDF,onChange:e=>w({...b,montantCDF:e}),min:0,max:5e6,step:50,exchangeRate:R.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Catégorie *"}),i.jsx(Ce,{value:b.categorie,label:"Catégorie *",onChange:e=>w({...b,categorie:e.target.value}),children:H.map(e=>i.jsx(J,{value:e,children:e},e))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date de la dépense *",type:"date",value:b.dateDepense,onChange:e=>w({...b,dateDepense:e.target.value}),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:b.notes,onChange:e=>w({...b,notes:e.target.value})})})]})]}),i.jsxs(Le,{children:[!D&&i.jsx(Xe,{control:i.jsx(Je,{checked:N,onChange:e=>L(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),$&&i.jsxs(a,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(qe,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(I,{onClick:Z,children:"Annuler"}),i.jsx(I,{onClick:ae,variant:"contained",disabled:$,children:D?"Mettre à jour":"Créer"})]})]}),i.jsx(Ka,{open:V,onClose:()=>B(!1),receiptData:_,onPrintSuccess:()=>{k("Reçu imprimé avec succès"),setTimeout(()=>k(""),3e3)}})]})}catch(Sr){return console.error("ExpensesPage error:",Sr),i.jsxs(a,{p:3,children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Dépenses"}),i.jsx(O,{severity:"error",children:"Une erreur s'est produite lors du chargement de la page des dépenses. Veuillez recharger l'application ou contacter le support technique."})]})}};function ir(e){const{children:t,value:n,index:r,...s}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==r,id:`reports-tabpanel-${r}`,"aria-labelledby":`reports-tab-${r}`,...s,children:n===r&&i.jsx(a,{sx:{p:3},children:t})})}const ar=()=>{const[e,t]=vt.useState(0),[n,r]=vt.useState("this_month"),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,p]=vt.useState([]),[x,g]=vt.useState([]),[y,j]=vt.useState([]),[v,b]=vt.useState([]),[E,P]=vt.useState([]),[F,U]=vt.useState([]),[T,k]=vt.useState({tauxChangeUSDCDF:2800});vt.useEffect(()=>{(async()=>{try{const e=await la.getSettings();k(e)}catch(Sr){console.error("Error loading settings:",Sr)}})()},[]),vt.useEffect(()=>{R()},[]),vt.useEffect(()=>{M()},[m,v,n,s,d]);const R=async()=>{try{console.log("ReportsPage: Loading data..."),p(await la.getSales()),g(await la.getProducts()),j(await la.getDebts()),b(await la.getExpenses()),console.log("ReportsPage: Data loaded successfully")}catch(Sr){console.error("ReportsPage: Error loading data:",Sr),p([]),g([]),j([]),b([])}},M=()=>{const e=new Date;let t,i;switch(n){case"today":t=Tt(e),i=kt(e);break;case"this_week":t=Rt(e,7),i=e;break;case"this_month":default:t=It(e),i=Nt(e);break;case"last_month":const n=new Date(e.getFullYear(),e.getMonth()-1,1);t=It(n),i=Nt(n);break;case"custom":s&&d?(t=new Date(s),i=new Date(d)):(t=It(e),i=Nt(e))}const a=m.filter(e=>{const n=new Date(e.datevente);return Ot(n,{start:t,end:i})}),r=v.filter(e=>{const n=new Date(e.dateDepense);return Ot(n,{start:t,end:i})});P(a),U(r)},N=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,L=e=>{try{const t=null==e||isNaN(e)?0:e,n=t/((null==T?void 0:T.tauxChangeUSDCDF)||2800);return{primary:`${t.toLocaleString("fr-FR")} CDF`,secondary:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`}}catch(Sr){return console.error("ReportsPage: Error formatting dual currency:",Sr),{primary:"0 CDF",secondary:"$0.00"}}},O=E.reduce((e,t)=>e+t.totalCDF,0),V=E.length,q=V>0?O/V:0,B=F.reduce((e,t)=>e+t.montantCDF,0),_=O-B,z=E.reduce((e,t)=>(t.produits.forEach(t=>{e[t.produitId]||(e[t.produitId]={nom:t.nomProduit,quantite:0,revenue:0}),e[t.produitId].quantite+=t.quantite,e[t.produitId].revenue+=t.totalCDF}),e),{}),$=Object.values(z).sort((e,t)=>t.revenue-e.revenue).slice(0,10),W=E.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+t.totalCDF,e),{}),X=E.reduce((e,t)=>(t.produits.forEach(t=>{const n=x.find(e=>e.id===t.produitId);n&&(e[n.categorie]=(e[n.categorie]||0)+t.totalCDF)}),e),{}),Q=E.reduce((e,t)=>{const n=Ut(new Date(t.datevente),"yyyy-MM-dd");return e[n]=(e[n]||0)+t.totalCDF,e},{}),H=Array.from({length:30},(e,t)=>{const n=Rt(new Date,29-t),i=Ut(n,"yyyy-MM-dd");return{date:Ut(n,"dd/MM"),revenue:Q[i]||0}}),Y={labels:H.map(e=>e.date),datasets:[{label:"Profit (USD)",data:H.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]},G={labels:["Cash","Banque","Mobile Money"],datasets:[{data:[W.cash||0,W.banque||0,W.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},K={labels:Object.keys(X),datasets:[{label:"Profit par catégorie (USD)",data:Object.values(X),backgroundColor:["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40"]}]};return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Rapports et Analyses"}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>{const e=`Résumé du Rapport\nPériode,${"custom"===n?`${s} - ${d}`:n}\nDate de Génération,${(new Date).toISOString()}\nTotal Ventes,${V}\nChiffre d'Affaires (USD),${O.toFixed(2)}\nTotal Dépenses (USD),${B.toFixed(2)}\nBénéfice Net (USD),${_.toFixed(2)}\nVente Moyenne (USD),${q.toFixed(2)}\n`,t=`\nTop Produits les Plus Vendus\nRang,Produit,Quantité Vendue,Revenus (USD),Revenus Moyens (USD)\n${$.map((e,t)=>`${t+1},${e.nom},${e.quantite},${e.revenue.toFixed(2)},${(e.revenue/e.quantite).toFixed(2)}`).join("\n")}\n`,i=`\nMéthodes de Paiement\nMéthode,Nombre de Transactions,Montant Total (USD)\n${W.map(e=>`${e.method},${e.count},${e.amount.toFixed(2)}`).join("\n")}\n`,a=`\nPerformance par Catégorie\nCatégorie,Revenus (USD),Nombre de Ventes\n${X.map(e=>`${e.category},${e.revenue.toFixed(2)},${e.sales}`).join("\n")}\n`,r=new Blob(["\ufeff"+`SmartBoutique - Rapport d'Analyse\n${e}${t}${i}${a}`],{type:"text/csv;charset=utf-8"}),o=URL.createObjectURL(r),l=document.createElement("a");l.href=o,l.download=`SmartBoutique_Rapport_${n}_${Ut(new Date,"yyyy-MM-dd")}.csv`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(o)},children:"Exporter le Rapport"})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Période"}),i.jsxs(Ce,{value:n,label:"Période",onChange:e=>r(e.target.value),children:[i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_week",children:"Cette semaine"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"}),i.jsx(J,{value:"custom",children:"Personnalisée"})]})]})}),"custom"===n&&i.jsxs(i.Fragment,{children:[i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de début",type:"date",value:s,onChange:e=>l(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de fin",type:"date",value:d,onChange:e=>u(e.target.value),InputLabelProps:{shrink:!0}})})]})]})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Chiffre d'Affaires"}),i.jsx(o,{variant:"h6",color:"primary",fontWeight:"medium",children:L(O*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",L(O*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(De,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Nombre de Ventes"}),i.jsx(o,{variant:"h6",color:"success.main",children:V})]}),i.jsx(C,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",color:"error",fontWeight:"medium",children:L(B*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",L(B*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(f,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Bénéfice Net"}),i.jsx(o,{variant:"h6",color:_>=0?"success.main":"error",fontWeight:"medium",children:L(_*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).primary}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",L(_*((null==T?void 0:T.tauxChangeUSDCDF)||2800)).secondary]})]}),i.jsx(D,{color:_>=0?"success":"error",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{mb:3},children:i.jsxs(at,{value:e,onChange:(e,n)=>{t(n)},"aria-label":"reports tabs",children:[i.jsx(rt,{label:"Tendances",icon:i.jsx(De,{})}),i.jsx(rt,{label:"Produits",icon:i.jsx(S,{})}),i.jsx(rt,{label:"Analyses",icon:i.jsx(w,{})})]})}),i.jsx(ir,{value:e,index:0,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Évolution des Ventes (30 derniers jours)"}),i.jsx(te,{children:i.jsx(Vt,{data:Y,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Méthodes de Paiement"}),i.jsx(te,{children:i.jsx(qt,{data:G,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Performance par Catégorie"}),i.jsx(te,{children:i.jsx(Bt,{data:K,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})})]})}),i.jsx(ir,{value:e,index:1,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Top 10 Produits les Plus Vendus"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Rang"}),i.jsx(ge,{children:"Produit"}),i.jsx(ge,{align:"right",children:"Quantité Vendue"}),i.jsx(ge,{align:"right",children:"Profit"}),i.jsx(ge,{align:"right",children:"Profit Moyen"})]})}),i.jsx(ye,{children:$.map((e,t)=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsx(A,{label:`#${t+1}`,color:t<3?"primary":"default",size:"small"})}),i.jsx(ge,{children:e.nom}),i.jsx(ge,{align:"right",children:e.quantite}),i.jsx(ge,{align:"right",children:N(e.revenue,"USD")}),i.jsx(ge,{align:"right",children:N(e.revenue/e.quantite,"USD")})]},t))})]})})})]})}),i.jsx(ir,{value:e,index:2,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Métriques de Performance"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Vente Moyenne"}),i.jsx(o,{variant:"h6",children:N(q,"USD")})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Produits Vendus"}),i.jsx(o,{variant:"h6",children:E.reduce((e,t)=>e+t.produits.reduce((e,t)=>e+t.quantite,0),0)})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Panier Moyen"}),i.jsxs(o,{variant:"h6",children:[V>0?(E.reduce((e,t)=>e+t.produits.length,0)/V).toFixed(1):"0"," articles"]})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Répartition des Profits"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes Cash"}),i.jsx(o,{variant:"h6",color:"success.main",children:N(E.filter(e=>"cash"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",color:"warning.main",children:N(E.filter(e=>"credit"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsx(h,{sx:{width:"100%",my:1}}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Créances Actives"}),i.jsx(o,{variant:"h6",color:"error",children:N(y.filter(e=>"paid"!==e.statut).reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Analyse des Dépenses par Catégorie"}),i.jsx(te,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Catégorie"}),i.jsx(ge,{align:"right",children:"Nombre"}),i.jsx(ge,{align:"right",children:"Montant Total"}),i.jsx(ge,{align:"right",children:"Montant Moyen"}),i.jsx(ge,{align:"right",children:"% du Total"})]})}),i.jsx(ye,{children:Object.entries(F.reduce((e,t)=>(e[t.categorie]||(e[t.categorie]={count:0,total:0}),e[t.categorie].count+=1,e[t.categorie].total+=t.montantCDF,e),{})).sort(([,e],[,t])=>t.total-e.total).map(([e,t])=>i.jsxs(xe,{children:[i.jsx(ge,{children:e}),i.jsx(ge,{align:"right",children:t.count}),i.jsx(ge,{align:"right",children:N(t.total,"USD")}),i.jsx(ge,{align:"right",children:N(t.total/t.count,"USD")}),i.jsx(ge,{align:"right",children:B>0?`${(t.total/B*100).toFixed(1)}%`:"0%"})]},e))})]})})})]})})]})})]})},rr=()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState([]),[s,d]=vt.useState(""),[u,m]=vt.useState(""),[h,p]=vt.useState(""),[x,g]=vt.useState(0),[y,j]=vt.useState(10),[S,C]=vt.useState(!1),[D,v]=vt.useState(null),[b,f]=vt.useState(!1),[w,P]=vt.useState({nom:"",email:"",role:"employee",motDePasse:"",actif:!0}),[T,k]=vt.useState(""),[R,M]=vt.useState(""),N=ca.getUserPermissions(),L=ca.getCurrentUser();vt.useEffect(()=>{V()},[]),vt.useEffect(()=>{B()},[e,s,u,h]),vt.useEffect(()=>{-1===y&&j(n.length||1)},[n.length,y]);const V=async()=>{const e=await la.getUsers();t(e)},B=()=>{let t=e;if(s&&(t=t.filter(e=>e.nom.toLowerCase().includes(s.toLowerCase())||e.email.toLowerCase().includes(s.toLowerCase()))),u&&(t=t.filter(e=>e.role===u)),h){const e="active"===h;t=t.filter(t=>t.actif===e)}r(t)},_=e=>{switch(e){case"super_admin":return"Super Admin";case"admin":return"Administrateur";case"employee":return"Employé";default:return e}},z=e=>{switch(e){case"super_admin":return"error";case"admin":return"warning";case"employee":return"primary";default:return"default"}},$=e=>{switch(e){case"super_admin":case"admin":return i.jsx(ot,{});default:return i.jsx(lt,{})}},W=e=>{e?(v(e),P({nom:e.nom,email:e.email,role:e.role,motDePasse:"",actif:e.actif})):(v(null),P({nom:"",email:"",role:"employee",motDePasse:"",actif:!0})),C(!0),f(!1),k(""),M("")},X=()=>{C(!1),v(null),k(""),M("")},Q=e.length,H=e.filter(e=>e.actif).length,Y=e.filter(e=>"admin"===e.role||"super_admin"===e.role).length,K=e.filter(e=>"employee"===e.role).length;return i.jsxs(a,{children:[i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Utilisateurs"}),N.canManageUsers&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>W(),children:"Nouvel Utilisateur"})]}),R&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>M(""),children:R}),T&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>k(""),children:T}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Utilisateurs"}),i.jsx(o,{variant:"h6",children:Q})]}),i.jsx(E,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Utilisateurs Actifs"}),i.jsx(o,{variant:"h6",color:"success.main",children:H})]}),i.jsx(st,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Administrateurs"}),i.jsx(o,{variant:"h6",color:"warning.main",children:Y})]}),i.jsx(ot,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(a,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(a,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Employés"}),i.jsx(o,{variant:"h6",color:"info.main",children:K})]}),i.jsx(lt,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom ou email...",value:s,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Rôle"}),i.jsxs(Ce,{value:u,label:"Rôle",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les rôles"}),i.jsx(J,{value:"super_admin",children:"Super Admin"}),i.jsx(J,{value:"admin",children:"Administrateur"}),i.jsx(J,{value:"employee",children:"Employé"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Statut"}),i.jsxs(Ce,{value:h,label:"Statut",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les statuts"}),i.jsx(J,{value:"active",children:"Actif"}),i.jsx(J,{value:"inactive",children:"Inactif"})]})]})})]})}),i.jsxs(me,{component:c,children:[i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Utilisateur"}),i.jsx(ge,{children:"Email"}),i.jsx(ge,{align:"center",children:"Rôle"}),i.jsx(ge,{align:"center",children:"Statut"}),i.jsx(ge,{children:"Date de Création"}),N.canManageUsers&&i.jsx(ge,{align:"center",children:"Actions"})]})}),i.jsx(ye,{children:(-1===y?n:n.slice(x*y,x*y+y)).map(n=>i.jsxs(xe,{hover:!0,onClick:()=>W(n),sx:{cursor:"pointer"},children:[i.jsx(ge,{children:i.jsxs(a,{display:"flex",alignItems:"center",gap:2,children:[i.jsx(l,{sx:{bgcolor:z(n.role)},children:n.nom.charAt(0).toUpperCase()}),i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),n.id===(null==L?void 0:L.id)&&i.jsx(A,{label:"Vous",size:"small",color:"primary"})]})]})}),i.jsx(ge,{children:n.email}),i.jsx(ge,{align:"center",children:i.jsx(A,{icon:$(n.role),label:_(n.role),color:z(n.role),size:"small"})}),i.jsx(ge,{align:"center",children:i.jsx(A,{label:n.actif?"Actif":"Inactif",color:n.actif?"success":"error",size:"small"})}),i.jsx(ge,{children:Ut(new Date(n.dateCreation),"dd/MM/yyyy",{locale:xa})}),N.canManageUsers&&i.jsx(ge,{align:"center",children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>W(n),disabled:"super_admin"===n.role&&!ca.hasRole(["super_admin"]),children:i.jsx(Re,{fontSize:"small"})})}),i.jsx(F,{title:n.actif?"Désactiver":"Activer",children:i.jsx(U,{size:"small",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id)&&n.actif)return k("Vous ne pouvez pas désactiver votre propre compte"),void setTimeout(()=>k(""),3e3);const i={...n,actif:!n.actif},a=e.map(e=>e.id===n.id?i:e);t(a),await la.setUsers(a),M(`Utilisateur ${i.actif?"activé":"désactivé"} avec succès`),setTimeout(()=>M(""),3e3)})(n),disabled:n.id===(null==L?void 0:L.id)&&n.actif,children:n.actif?i.jsx(G,{fontSize:"small"}):i.jsx(st,{fontSize:"small"})})}),ca.hasRole(["super_admin"])&&i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id))return k("Vous ne pouvez pas supprimer votre propre compte"),void setTimeout(()=>k(""),3e3);if("super_admin"===n.role&&!ca.hasRole(["super_admin"]))return k("Seul un Super Admin peut supprimer un compte Super Admin"),void setTimeout(()=>k(""),3e3);if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await la.setUsers(i),M("Utilisateur supprimé avec succès"),setTimeout(()=>M(""),3e3)}})(n),disabled:n.id===(null==L?void 0:L.id),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id))})]}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===y?n.length:y,page:-1===y?0:x,onPageChange:(e,t)=>{-1!==y&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);j(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:S,onClose:X,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ie,{children:D?"Modifier l'Utilisateur":"Nouvel Utilisateur"}),i.jsxs(Ne,{children:[T&&i.jsx(O,{severity:"error",sx:{mb:2},children:T}),R&&i.jsx(O,{severity:"success",sx:{mb:2},children:R}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom complet *",value:w.nom,onChange:e=>P({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Email *",type:"email",value:w.email,onChange:e=>P({...w,email:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Rôle *"}),i.jsxs(Ce,{value:w.role,label:"Rôle *",onChange:e=>P({...w,role:e.target.value}),disabled:!ca.hasRole(["super_admin"])||!(!D||D.id!==(null==L?void 0:L.id)),children:[i.jsx(J,{value:"employee",children:"Employé"}),i.jsx(J,{value:"admin",children:"Administrateur"}),ca.hasRole(["super_admin"])&&i.jsx(J,{value:"super_admin",children:"Super Admin"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:D?"Nouveau mot de passe (optionnel)":"Mot de passe *",type:b?"text":"password",value:w.motDePasse,onChange:e=>P({...w,motDePasse:e.target.value}),InputProps:{endAdornment:i.jsx(ie,{position:"end",children:i.jsx(U,{onClick:()=>f(!b),edge:"end",children:b?i.jsx(re,{}):i.jsx(se,{})})})},helperText:D?"Laissez vide pour conserver le mot de passe actuel":"Minimum 6 caractères"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Xe,{control:i.jsx(Ke,{checked:w.actif,onChange:e=>P({...w,actif:e.target.checked}),disabled:!(!D||D.id!==(null==L?void 0:L.id))}),label:"Compte actif"})})]})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:X,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!w.nom.trim())return void k("Le nom est requis");if(!w.email.trim())return void k("L'email est requis");if(!D&&!w.motDePasse.trim())return void k("Le mot de passe est requis pour un nouvel utilisateur");if(w.motDePasse&&w.motDePasse.length<6)return void k("Le mot de passe doit contenir au moins 6 caractères");if(e.some(e=>e.email.toLowerCase()===w.email.trim().toLowerCase()&&e.id!==(null==D?void 0:D.id)))return void k("Un utilisateur avec cet email existe déjà");if("super_admin"===w.role&&!ca.hasRole(["super_admin"]))return void k("Seul un Super Admin peut créer ou modifier un compte Super Admin");if(D&&D.id===(null==L?void 0:L.id)&&w.role!==D.role)return void k("Vous ne pouvez pas modifier votre propre rôle");if(D&&D.id===(null==L?void 0:L.id)&&!w.actif)return void k("Vous ne pouvez pas désactiver votre propre compte");const n=(new Date).toISOString();if(D){const n={...D,nom:w.nom.trim(),email:w.email.trim(),role:w.role,actif:w.actif,...w.motDePasse&&{motDePasse:w.motDePasse}},i=e.map(e=>e.id===D.id?n:e);t(i),await la.setUsers(i),D.id===(null==L?void 0:L.id)&&await la.setCurrentUser(n),M("Utilisateur mis à jour avec succès")}else{const i={id:Date.now().toString(),nom:w.nom.trim(),email:w.email.trim(),role:w.role,motDePasse:w.motDePasse,dateCreation:n,actif:w.actif},a=[...e,i];t(a),await la.setUsers(a),M("Utilisateur créé avec succès")}setTimeout(()=>{X()},1500)},variant:"contained",children:D?"Mettre à jour":"Créer"})]})]})]})};class sr{async exportAllData(){try{const[e,t,n,i,a,r]=await Promise.all([Qi.getProducts(),Qi.getUsers(),Qi.getSales(),Qi.getDebts(),Qi.getExpenses(),Qi.getSettings()]),s={exportDate:(new Date).toISOString(),products:Ai.arrayToCSV(e,Ii),users:Ai.arrayToCSV(t,Ni),sales:Ai.arrayToCSV(n,Li),debts:Ai.arrayToCSV(i,Oi),expenses:Ai.arrayToCSV(a,Vi),settings:Ai.arrayToCSV(zi(r),_i)},o=`SmartBoutique - Sauvegarde Complète\nDate d'exportation: ${s.exportDate}\n\n=== PRODUITS ===\n${s.products}\n\n=== UTILISATEURS ===\n${s.users}\n\n=== VENTES ===\n${s.sales}\n\n=== DETTES ===\n${s.debts}\n\n=== DÉPENSES ===\n${s.expenses}\n\n=== PARAMÈTRES ===\n${s.settings}\n`;return{success:!0,message:"Exportation complète réussie (compatible Excel)",data:"\ufeff"+o}}catch(Sr){return console.error("Erreur lors de l'exportation complète:",Sr),{success:!1,message:"Erreur lors de l'exportation: "+Sr.message}}}async exportData(e){try{let t=[],n=[],i="";switch(e){case"products":t=await Qi.getProducts(),n=Ii,i="produits";break;case"users":t=await Qi.getUsers(),n=Ni,i="utilisateurs";break;case"sales":t=await Qi.getSales(),n=Li,i="ventes";break;case"debts":t=await Qi.getDebts(),n=Oi,i="dettes";break;case"expenses":t=await Qi.getExpenses(),n=Vi,i="depenses"}const a=Ai.arrayToCSV(t,n);return{success:!0,message:`Exportation ${i} réussie (${t.length} enregistrements)`,data:a}}catch(Sr){return console.error(`Erreur lors de l'exportation ${e}:`,Sr),{success:!1,message:"Erreur lors de l'exportation: "+Sr.message}}}async importProducts(e,t=!1){try{const n=Ai.csvToArray(e,Ii),i=Ai.validateCSVData(n,Ii);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let a=n;if(!t){const e=await Qi.getProducts(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));a=[...e,...i]}return await Qi.setProducts(a),{success:!0,message:`${n.length} produits importés avec succès`,errors:[],importedCount:n.length}}catch(Sr){return console.error("Erreur lors de l'importation des produits:",Sr),{success:!1,message:"Erreur lors de l'importation: "+Sr.message,errors:[Sr.message],importedCount:0}}}async importUsers(e,t=!1){try{const n=Ai.csvToArray(e,Ni),i=Ai.validateCSVData(n,Ni);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let a=n;if(!t){const e=await Qi.getUsers(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));a=[...e,...i]}return await Qi.setUsers(a),{success:!0,message:`${n.length} utilisateurs importés avec succès`,errors:[],importedCount:n.length}}catch(Sr){return console.error("Erreur lors de l'importation des utilisateurs:",Sr),{success:!1,message:"Erreur lors de l'importation: "+Sr.message,errors:[Sr.message],importedCount:0}}}generateTemplate(e){switch(e){case"products":return Ai.generateTemplate(Ii);case"users":return Ai.generateTemplate(Ni);case"sales":return Ai.generateTemplate(Li);case"debts":return Ai.generateTemplate(Oi);case"expenses":return Ai.generateTemplate(Vi);default:return""}}async createAutomaticBackup(){try{const e=await this.exportAllData();if(e.success&&e.data){const t=(new Date).toISOString().replace(/[:.]/g,"-");return{success:!0,message:`Sauvegarde automatique créée: ${t}`,data:e.data}}return e}catch(Sr){return console.error("Erreur lors de la sauvegarde automatique:",Sr),{success:!1,message:"Erreur lors de la sauvegarde automatique: "+Sr.message}}}getSampleCSVData(e){switch(e){case"products":return"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\nSAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01";case"users":return"ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif\nSAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui";default:return this.generateTemplate(e)}}}const or=new sr,lr=Object.freeze(Object.defineProperty({__proto__:null,CSVImportExportService:sr,csvImportExportService:or},Symbol.toStringTag,{value:"Module"})),cr=({onSuccess:e,onError:t})=>{var n,r,s,l;const[c,d]=vt.useState(!1),[u,m]=vt.useState(!1),[p,x]=vt.useState(!1),[g,y]=vt.useState("products"),[j,S]=vt.useState(""),[C,D]=vt.useState(!1),[v,b]=vt.useState(""),f=[{key:"products",label:"Produits",icon:"📦"},{key:"users",label:"Utilisateurs",icon:"👥"},{key:"sales",label:"Ventes",icon:"💰"},{key:"debts",label:"Dettes",icon:"📋"},{key:"expenses",label:"Dépenses",icon:"💸"}],w=()=>{const e=or.generateTemplate(g);S(e)};return i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:[i.jsx(ct,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Données CSV"]}),i.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Exportez et importez vos données au format CSV pour une meilleure portabilité et accessibilité."}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(a,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(Ue,{sx:{mr:1,verticalAlign:"middle"}}),"Exportation"]}),i.jsx(I,{variant:"contained",color:"primary",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await or.exportAllData();if(n.success&&n.data){const t=new Blob([n.data],{type:"text/plain;charset=utf-8"}),i=URL.createObjectURL(t),a=document.createElement("a");a.href=i,a.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(i),null==e||e("Sauvegarde complète exportée avec succès (compatible Excel)")}else null==t||t(n.message)}catch(Sr){null==t||t("Erreur lors de l'exportation: "+Sr.message)}finally{d(!1)}},disabled:c,startIcon:c?i.jsx(qe,{size:20}):i.jsx(dt,{}),sx:{mb:2},children:"Exporter Toutes les Données"}),i.jsx(h,{sx:{my:2}}),i.jsx(o,{variant:"body2",gutterBottom:!0,children:"Exporter un type de données spécifique:"}),i.jsx(a,{sx:{mb:2},children:f.map(e=>i.jsx(A,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await or.exportData(g);n.success&&n.data?(b(n.data),m(!0),null==e||e(n.message)):null==t||t(n.message)}catch(Sr){null==t||t("Erreur lors de l'exportation: "+Sr.message)}finally{d(!1)}},disabled:c,startIcon:i.jsx(ut,{}),children:["Exporter ",null==(n=f.find(e=>e.key===g))?void 0:n.label]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(a,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(Te,{sx:{mr:1,verticalAlign:"middle"}}),"Importation"]}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Sélectionnez le type de données à importer:"}),i.jsx(a,{sx:{mb:2},children:f.filter(e=>["products","users"].includes(e.key)).map(e=>i.jsx(A,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(I,{variant:"outlined",fullWidth:!0,onClick:()=>x(!0),startIcon:i.jsx(Te,{}),sx:{mb:1},children:["Importer ",null==(r=f.find(e=>e.key===g))?void 0:r.label]}),i.jsx(I,{variant:"text",size:"small",fullWidth:!0,onClick:w,children:"Obtenir un modèle CSV"})]})})]}),i.jsxs(Ae,{open:u,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Ie,{children:["Données Exportées - ",null==(s=f.find(e=>e.key===g))?void 0:s.label]}),i.jsx(Ne,{children:i.jsx(ne,{multiline:!0,rows:10,fullWidth:!0,value:v,variant:"outlined",InputProps:{readOnly:!0},sx:{fontFamily:"monospace"}})}),i.jsxs(Le,{children:[i.jsx(I,{onClick:()=>m(!1),children:"Fermer"}),i.jsx(I,{onClick:()=>{var e;const t=(null==(e=f.find(e=>e.key===g))?void 0:e.label)||g;let n=v;n.startsWith("\ufeff")||(n="\ufeff"+n);const i=new Blob([n],{type:"text/csv;charset=utf-8"}),a=URL.createObjectURL(i),r=document.createElement("a");r.href=a,r.download=`SmartBoutique_${t}_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(r),r.click(),document.body.removeChild(r),URL.revokeObjectURL(a),m(!1)},variant:"contained",startIcon:i.jsx(Ue,{}),children:"Télécharger CSV"})]})]}),i.jsxs(Ae,{open:p,onClose:()=>x(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Ie,{children:["Importer ",null==(l=f.find(e=>e.key===g))?void 0:l.label]}),i.jsxs(Ne,{children:[i.jsx(O,{severity:"info",sx:{mb:2},children:'Collez le contenu CSV ci-dessous. Utilisez "Obtenir un modèle CSV" pour voir le format requis.'}),i.jsx(ne,{multiline:!0,rows:8,fullWidth:!0,value:j,onChange:e=>S(e.target.value),placeholder:"Collez votre contenu CSV ici...",variant:"outlined",sx:{mb:2,fontFamily:"monospace"}}),i.jsx(Xe,{control:i.jsx(Je,{checked:C,onChange:e=>D(e.target.checked)}),label:"Remplacer les données existantes"})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:()=>x(!1),children:"Annuler"}),i.jsx(I,{onClick:w,variant:"outlined",children:"Obtenir Modèle"}),i.jsx(I,{onClick:async()=>{if(j.trim()){d(!0);try{let n;switch(g){case"products":n=await or.importProducts(j,C);break;case"users":n=await or.importUsers(j,C);break;default:return void(null==t||t("Type de données non supporté pour l'importation"))}n.success?(null==e||e(n.message),x(!1),S("")):null==t||t(n.message+"\n"+n.errors.join("\n"))}catch(Sr){null==t||t("Erreur lors de l'importation: "+Sr.message)}finally{d(!1)}}else null==t||t("Veuillez saisir le contenu CSV à importer")},variant:"contained",disabled:c||!j.trim(),startIcon:c?i.jsx(qe,{size:20}):i.jsx(Te,{}),children:"Importer"})]})]})]})})},dr=({value:e="",onChange:t,disabled:n=!1,maxSizeKB:r=500,acceptedFormats:s=["image/jpeg","image/jpg","image/png","image/gif"]})=>{const[l,c]=vt.useState(""),[d,u]=vt.useState(!1),m=vt.useRef(null),h=()=>{m.current&&m.current.click()};return i.jsxs(a,{children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Logo de l'entreprise"}),i.jsx("input",{ref:m,type:"file",accept:s.join(","),onChange:e=>{var n;const i=null==(n=e.target.files)?void 0:n[0];if(!i)return;if(c(""),u(!0),!s.includes(i.type))return c(`Format non supporté. Formats acceptés: ${s.map(e=>e.split("/")[1].toUpperCase()).join(", ")}`),void u(!1);if(i.size/1024>r)return c(`Fichier trop volumineux. Taille maximale: ${r}KB`),void u(!1);const a=new FileReader;a.onload=e=>{var n;const i=null==(n=e.target)?void 0:n.result;i&&t(i),u(!1)},a.onerror=()=>{c("Erreur lors de la lecture du fichier"),u(!1)},a.readAsDataURL(i)},style:{display:"none"},disabled:n}),i.jsx(ee,{variant:"outlined",sx:{mb:2},children:i.jsx(te,{sx:{textAlign:"center",py:3},children:e?i.jsxs(a,{children:[i.jsx(a,{component:"img",src:e,alt:"Logo de l'entreprise",sx:{maxWidth:"200px",maxHeight:"100px",objectFit:"contain",border:"1px solid #e0e0e0",borderRadius:1,mb:2}}),i.jsxs(a,{children:[i.jsx(I,{variant:"outlined",startIcon:i.jsx(mt,{}),onClick:h,disabled:n||d,sx:{mr:1},children:"Changer"}),i.jsx(U,{color:"error",onClick:()=>{t(""),c(""),m.current&&(m.current.value="")},disabled:n||d,title:"Supprimer le logo",children:i.jsx(q,{})})]})]}):i.jsxs(a,{children:[i.jsx(ht,{sx:{fontSize:48,color:"text.secondary",mb:2}}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Aucun logo configuré"}),i.jsx(I,{variant:"contained",startIcon:d?i.jsx(qe,{size:20}):i.jsx(mt,{}),onClick:h,disabled:n||d,children:d?"Chargement...":"Télécharger un logo"})]})})}),l&&i.jsx(O,{severity:"error",sx:{mb:2},children:l}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Formats acceptés: ",s.map(e=>e.split("/")[1].toUpperCase()).join(", ")," • Taille maximale: ",r,"KB • Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques"]})]})};function ur(e){const{children:t,value:n,index:r,...s}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==r,id:`settings-tabpanel-${r}`,"aria-labelledby":`settings-tab-${r}`,...s,children:n===r&&i.jsx(a,{sx:{p:3},children:t})})}const mr=()=>{const[e,t]=vt.useState(null),[n,r]=vt.useState(0),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,g]=vt.useState({nom:"",adresse:"",telephone:"",email:"",rccm:"",idNat:"",logo:""}),[j,S]=vt.useState({tauxChangeUSDCDF:2800,seuilStockBas:10}),[C,D]=vt.useState({impressionAutomatique:!1,taillePapier:"thermal"}),[v,b]=vt.useState([]),[f,w]=vt.useState(!1),[E,P]=vt.useState(null),[F,T]=vt.useState({nom:"",description:"",couleur:"#2196F3"}),k=ca.getUserPermissions();vt.useEffect(()=>{R()},[]);const R=async()=>{var e,n;const i=await la.getSettings();t(i),g(i.entreprise),S({tauxChangeUSDCDF:i.tauxChangeUSDCDF,seuilStockBas:i.seuilStockBas}),D({impressionAutomatique:(null==(e=i.impression)?void 0:e.impressionAutomatique)||!1,taillePapier:(null==(n=i.impression)?void 0:n.taillePapier)||"thermal"}),b(i.categories)},M=e=>{e?(P(e),T({nom:e.nom,description:e.description,couleur:e.couleur})):(P(null),T({nom:"",description:"",couleur:"#2196F3"})),w(!0),u("")},N=()=>{w(!1),P(null),u("")},L=async()=>{try{(()=>{var e,t;return"capacitor:"===window.location.protocol||(null==(t=null==(e=window.Capacitor)?void 0:e.isNativePlatform)?void 0:t.call(e))||navigator.userAgent.includes("Capacitor")})()?await B():await _(),console.log("✅ Complete data reset performed successfully")}catch(e){throw console.error("❌ Error during data reset:",e),e}},B=async()=>{try{const{Preferences:t}=await Si(async()=>{const{Preferences:e}=await Promise.resolve().then(()=>Xi);return{Preferences:e}},void 0,import.meta.url),{keys:n}=await t.keys(),i=n.filter(e=>e.startsWith("smartboutique_")||e.startsWith("smartboutique_csv_"));for(const e of i)await t.remove({key:e});try{const{mobileSQLiteStorageService:e}=await Si(async()=>{const{mobileSQLiteStorageService:e}=await Promise.resolve().then(()=>sa);return{mobileSQLiteStorageService:e}},void 0,import.meta.url);console.log("Mobile SQLite data cleared (if available)")}catch(e){console.log("Mobile SQLite not available or already cleared")}console.log("✅ Mobile data cleared successfully")}catch(e){throw console.error("❌ Error clearing mobile data:",e),e}},_=async()=>{try{Object.keys(localStorage).forEach(e=>{e.startsWith("smartboutique_")&&localStorage.removeItem(e)});try{const{sqliteStorageService:e}=await Si(async()=>{const{sqliteStorageService:e}=await Promise.resolve().then(()=>ea);return{sqliteStorageService:e}},void 0,import.meta.url);console.log("Desktop SQLite data cleared (if available)")}catch(e){console.log("Desktop SQLite not available or already cleared")}console.log("✅ Desktop data cleared successfully")}catch(e){throw console.error("❌ Error clearing desktop data:",e),e}};return e?i.jsxs(a,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Paramètres"}),s&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>l(""),children:s}),d&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>u(""),children:d}),i.jsx(c,{sx:{mb:3},children:i.jsxs(at,{value:n,onChange:(e,t)=>{r(t)},"aria-label":"settings tabs",children:[i.jsx(rt,{label:"Entreprise",icon:i.jsx(pt,{})}),i.jsx(rt,{label:"Général",icon:i.jsx(de,{})}),i.jsx(rt,{label:"Impression",icon:i.jsx(Be,{})}),i.jsx(rt,{label:"Catégories",icon:i.jsx(nt,{})}),i.jsx(rt,{label:"Sauvegarde",icon:i.jsx(dt,{})}),i.jsx(rt,{label:"Données CSV",icon:i.jsx(Ue,{})})]})}),i.jsx(ur,{value:n,index:0,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Informations de l'Entreprise",avatar:i.jsx(pt,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom de l'entreprise",value:m.nom,onChange:e=>g({...m,nom:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:m.email,onChange:e=>g({...m,email:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:m.telephone,onChange:e=>g({...m,telephone:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:m.adresse,onChange:e=>g({...m,adresse:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"RCCM",value:m.rccm||"",onChange:e=>g({...m,rccm:e.target.value}),disabled:!k.canManageSettings,helperText:"Registre de Commerce et du Crédit Mobilier"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"ID NAT",value:m.idNat||"",onChange:e=>g({...m,idNat:e.target.value}),disabled:!k.canManageSettings,helperText:"Identification Nationale"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(dr,{value:m.logo||"",onChange:e=>g({...m,logo:e}),disabled:!k.canManageSettings})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,entreprise:m};await la.setSettings(n),t(n),l("Paramètres de l'entreprise sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(ur,{value:n,index:1,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Paramètres Généraux",avatar:i.jsx(de,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(Xa,{label:"Taux de change (1 USD = ? CDF)",value:j.tauxChangeUSDCDF,onChange:e=>S({...j,tauxChangeUSDCDF:e}),min:1e3,max:1e4,step:10,exchangeRate:j.tauxChangeUSDCDF,disabled:!k.canManageSettings,showSlider:!0,allowUSDInput:!1,helperText:"Définit le taux de conversion entre USD et CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Seuil de stock bas",type:"number",value:j.seuilStockBas,onChange:e=>S({...j,seuilStockBas:parseInt(e.target.value)||0}),disabled:!k.canManageSettings,helperText:"Alerte quand le stock est ≤ à cette valeur"})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;if(j.tauxChangeUSDCDF<=0)return void u("Le taux de change doit être supérieur à 0");if(j.seuilStockBas<0)return void u("Le seuil de stock bas ne peut pas être négatif");const n={...e,tauxChangeUSDCDF:j.tauxChangeUSDCDF,seuilStockBas:j.seuilStockBas};await la.setSettings(n),t(n),l("Paramètres généraux sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(ur,{value:n,index:2,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Paramètres d'Impression",avatar:i.jsx(Be,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(Xe,{control:i.jsx(Ke,{checked:C.impressionAutomatique,onChange:e=>D({...C,impressionAutomatique:e.target.checked}),disabled:!k.canManageSettings}),label:"Impression automatique des reçus"}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense"})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Taille du papier"}),i.jsxs(Ce,{value:C.taillePapier,label:"Taille du papier",onChange:e=>D({...C,taillePapier:e.target.value}),disabled:!k.canManageSettings,children:[i.jsx(J,{value:"thermal",children:"Reçu thermique (80mm)"}),i.jsx(J,{value:"a4",children:"A4"})]})]}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Choisissez le format de papier pour l'impression des reçus"})]}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,impression:{impressionAutomatique:C.impressionAutomatique,taillePapier:C.taillePapier}};await la.setSettings(n),t(n),l("Paramètres d'impression sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(ur,{value:n,index:3,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Gestion des Catégories",avatar:i.jsx(nt,{}),action:k.canManageSettings&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>M(),children:"Nouvelle Catégorie"})}),i.jsx(te,{children:i.jsx(p,{children:v.map((n,r)=>i.jsxs(ft.Fragment,{children:[i.jsxs(x,{children:[i.jsx(a,{sx:{width:20,height:20,backgroundColor:n.couleur,borderRadius:1,mr:2}}),i.jsx(y,{primary:n.nom,secondary:n.description}),k.canManageSettings&&i.jsxs(V,{children:[i.jsx(U,{edge:"end",onClick:()=>M(n),sx:{mr:1},children:i.jsx(Re,{})}),i.jsx(U,{edge:"end",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${n.nom}" ?`)){if((await la.getProducts()).some(e=>e.categorie===n.nom))return u("Cette catégorie est utilisée par des produits et ne peut pas être supprimée"),void setTimeout(()=>u(""),5e3);const i=v.filter(e=>e.id!==n.id);if(b(i),e){const n={...e,categories:i};await la.setSettings(n),t(n)}l("Catégorie supprimée avec succès"),setTimeout(()=>l(""),3e3)}})(n),children:i.jsx(q,{})})]})]}),r<v.length-1&&i.jsx(h,{})]},n.id))})})]})}),i.jsx(ur,{value:n,index:4,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Sauvegarde des Données",avatar:i.jsx(Ue,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde."}),i.jsx(I,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:async()=>{try{const{csvImportExportService:e}=await Si(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>lr);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();if(t.success&&t.data){const e=new Blob([t.data],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),l("Sauvegarde CSV exportée avec succès (compatible Excel)"),setTimeout(()=>l(""),3e3)}else u(t.message||"Erreur lors de l'exportation"),setTimeout(()=>u(""),3e3)}catch(e){u("Erreur lors de l'exportation des données"),setTimeout(()=>u(""),3e3)}},fullWidth:!0,sx:{mt:2},children:"Exporter les Données"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Restauration des Données",avatar:i.jsx(Te,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Importez un fichier de sauvegarde pour restaurer vos données."}),i.jsx("input",{accept:".json",style:{display:"none"},id:"import-file",type:"file",onChange:async e=>{var t;const n=null==(t=e.target.files)?void 0:t[0];if(!n)return;const i=new FileReader;i.onload=async e=>{var t;try{const n=JSON.parse(null==(t=e.target)?void 0:t.result);await la.importData(n)?(R(),l("Données importées avec succès"),setTimeout(()=>l(""),3e3),setTimeout(()=>window.location.reload(),2e3)):(u("Erreur lors de l'importation des données"),setTimeout(()=>u(""),3e3))}catch(n){u("Fichier de sauvegarde invalide"),setTimeout(()=>u(""),3e3)}},i.readAsText(n),e.target.value=""}}),i.jsx("label",{htmlFor:"import-file",children:i.jsx(I,{variant:"outlined",component:"span",startIcon:i.jsx(Te,{}),fullWidth:!0,sx:{mt:2},children:"Importer les Données"})})]})]})}),ca.hasRole(["super_admin"])&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(ue,{title:"Réinitialisation",avatar:i.jsx(gt,{})}),i.jsxs(te,{children:[i.jsx(O,{severity:"warning",sx:{mb:2},children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Attention:"})," Cette action supprimera toutes les données et restaurera les paramètres par défaut. Cette action est irréversible."]})}),i.jsx(I,{variant:"outlined",color:"error",startIcon:i.jsx(gt,{}),onClick:async()=>{if(!window.confirm("Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action supprimera définitivement :"))return;if(window.confirm("ATTENTION: Cette action va supprimer TOUTES les données suivantes :\n\n• Tous les produits et inventaire\n• Toutes les ventes et transactions\n• Toutes les dettes et paiements\n• Tous les employés et leurs paiements\n• Toutes les dépenses\n• Tous les utilisateurs (sauf admin système)\n• Tous les fichiers CSV et données importées\n\nL'application sera restaurée avec des données de démonstration fraîches.\n\nCette action est IRRÉVERSIBLE. Confirmez-vous ?"))try{u(""),l("Réinitialisation en cours... Veuillez patienter.");const e=await la.getCurrentUser(),t=(null==e?void 0:e.email)||"<EMAIL>";await L(),await la.initializeDefaultData();const n=await la.getUsers(),i=n.find(e=>e.email===t)||n.find(e=>"super_admin"===e.role);i&&await la.setCurrentUser(i),l("✅ Données réinitialisées avec succès ! Redirection vers le tableau de bord..."),setTimeout(()=>{window.location.reload()},2e3)}catch(e){console.error("Erreur lors de la réinitialisation:",e),u("Erreur lors de la réinitialisation des données. Veuillez réessayer."),setTimeout(()=>u(""),5e3)}},children:"Réinitialiser toutes les Données"})]})]})})]})}),i.jsx(ur,{value:n,index:5,children:i.jsx(cr,{onSuccess:e=>{l(e),setTimeout(()=>l(""),3e3)},onError:e=>{u(e),setTimeout(()=>u(""),5e3)}})}),i.jsxs(Ae,{open:f,onClose:N,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ie,{children:E?"Modifier la Catégorie":"Nouvelle Catégorie"}),i.jsxs(Ne,{children:[d&&i.jsx(O,{severity:"error",sx:{mb:2},children:d}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom de la catégorie *",value:F.nom,onChange:e=>T({...F,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:2,value:F.description,onChange:e=>T({...F,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Couleur",type:"color",value:F.couleur,onChange:e=>T({...F,couleur:e.target.value}),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(yt,{})})}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(a,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"body2",children:"Aperçu:"}),i.jsx(A,{label:F.nom||"Nom de la catégorie",sx:{backgroundColor:F.couleur,color:"white"}})]})})]})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:N,children:"Annuler"}),i.jsx(I,{onClick:async()=>{if(!F.nom.trim())return void u("Le nom de la catégorie est requis");if(v.some(e=>e.nom.toLowerCase()===F.nom.trim().toLowerCase()&&e.id!==(null==E?void 0:E.id)))return void u("Une catégorie avec ce nom existe déjà");let n;if(E)n=v.map(e=>e.id===E.id?{...e,nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur}:e);else{const e={id:Date.now().toString(),nom:F.nom.trim(),description:F.description.trim(),couleur:F.couleur};n=[...v,e]}if(b(n),e){const i={...e,categories:n};await la.setSettings(i),t(i)}l(E?"Catégorie mise à jour":"Catégorie créée"),setTimeout(()=>l(""),3e3),N()},variant:"contained",children:E?"Mettre à jour":"Créer"})]})]})]}):i.jsx(o,{children:"Chargement..."})},hr=()=>{const[e,t]=vt.useState([]),[n,r]=vt.useState([]),[s,l]=vt.useState(""),[d,u]=vt.useState(""),[m,h]=vt.useState(""),[p,x]=vt.useState(0),[g,y]=vt.useState(10),[j,S]=vt.useState(!1),[C,D]=vt.useState(null),[v,b]=vt.useState({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""}),[f,w]=vt.useState(""),[E,P]=vt.useState(""),[T,k]=vt.useState({tauxChangeUSDCDF:2800}),[R,M]=vt.useState(!1),[N,L]=vt.useState([]),[V,B]=vt.useState(!1),[_,z]=vt.useState(null),[$,W]=vt.useState({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""}),[X,Q]=vt.useState(0),H=ca.getUserPermissions(),G=ca.getCurrentUser(),K=[{value:"cash",label:"Cash"},{value:"mobile_money",label:"Mobile Money"},{value:"bank",label:"Banque"}];vt.useEffect(()=>{Z(),ae(),re()},[]),vt.useEffect(()=>{se()},[e,s,d,m]);const Z=async()=>{try{M(!0);const e=await la.getEmployeePayments();t(e)}catch(e){console.error("Error loading employee payments:",e),w("Erreur lors du chargement des paiements employés")}finally{M(!1)}},ae=async()=>{try{const e=await la.getEmployees();L(e)}catch(e){console.error("Error loading employees:",e),w("Erreur lors du chargement des employés")}},re=async()=>{try{const e=await la.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}},se=()=>{let t=[...e];if(s&&(t=t.filter(e=>{var t;return e.nomEmploye.toLowerCase().includes(s.toLowerCase())||(null==(t=e.notes)?void 0:t.toLowerCase().includes(s.toLowerCase()))})),d&&(t=t.filter(e=>e.methodePaiement===d)),m){const e=new Date;let n,i=e;switch(m){case"today":n=new Date(e.getFullYear(),e.getMonth(),e.getDate());break;case"week":n=new Date(e.getTime()-6048e5);break;case"month":n=It(e),i=Nt(e);break;case"year":n=new Date(e.getFullYear(),0,1);break;default:n=new Date(0)}t=t.filter(e=>{const t=new Date(e.datePaiement);return Ot(t,{start:n,end:i})})}r(t),x(0)},oe=e=>{e?(D(e),b({nomEmploye:e.nomEmploye,montantCDF:e.montantCDF,datePaiement:e.datePaiement,methodePaiement:e.methodePaiement,notes:e.notes||""})):(D(null),b({nomEmploye:"",montantCDF:0,datePaiement:Ut(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""})),S(!0),w(""),P("")},le=()=>{S(!1),D(null),w(""),P("")},de=e=>{const t=K.find(t=>t.value===e);return t?t.label:e},ue=e=>{e?(z(e),W({nomComplet:e.nomComplet,poste:e.poste,salaireCDF:e.salaireCDF,dateEmbauche:e.dateEmbauche,telephone:e.telephone||"",adresse:e.adresse||"",statut:e.statut,notes:e.notes||""})):(z(null),W({nomComplet:"",poste:"",salaireCDF:0,dateEmbauche:Ut(new Date,"yyyy-MM-dd"),telephone:"",adresse:"",statut:"actif",notes:""})),B(!0),w(""),P("")},De=()=>{B(!1),z(null),w(""),P("")},ve=n.reduce((e,t)=>e+t.montantCDF,0);return n.reduce((e,t)=>e+(t.montantUSD||0),0),i.jsxs(a,{p:3,children:[i.jsxs(o,{variant:"h4",gutterBottom:!0,children:[i.jsx(Y,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Employés et Paiements"]}),i.jsx(c,{sx:{mb:3},children:i.jsxs(at,{value:X,onChange:(e,t)=>Q(t),children:[i.jsx(rt,{label:"Paiements Employés"}),i.jsx(rt,{label:"Gestion des Employés"})]})}),0===X&&i.jsxs(i.Fragment,{children:[i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Paiements"}),i.jsx(o,{variant:"h5",children:n.length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total"}),i.jsxs(o,{variant:"h5",fontWeight:"medium",children:[La(ve,T.tauxChangeUSDCDF).primaryAmount," ",La(ve,T.tauxChangeUSDCDF).primaryCurrency]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ $",La(ve,T.tauxChangeUSDCDF).secondaryAmount]})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Payés"}),i.jsx(o,{variant:"h5",children:new Set(n.map(e=>e.nomEmploye)).size})]})})})]}),f&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>w(""),children:f}),E&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>P(""),children:E}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Rechercher employé",value:s,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Fe,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Méthode de paiement"}),i.jsxs(Ce,{value:d,onChange:e=>u(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"",children:"Toutes"}),K.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))]})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Période"}),i.jsx(Ce,{value:m,onChange:e=>h(e.target.value),label:"Période",children:[{value:"",label:"Toutes les dates"},{value:"today",label:"Aujourd'hui"},{value:"week",label:"Cette semaine"},{value:"month",label:"Ce mois"},{value:"year",label:"Cette année"}].map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>oe(),disabled:!H.canManageEmployeePayments||R,fullWidth:!0,children:"Nouveau Paiement"})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsx(I,{variant:"outlined",onClick:()=>{l(""),u(""),h("")},fullWidth:!0,children:"Réinitialiser"})})]})}),i.jsxs(c,{children:[i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Employé"}),i.jsx(ge,{children:"Montant (CDF)"}),i.jsx(ge,{children:"Montant (USD)"}),i.jsx(ge,{children:"Date de Paiement"}),i.jsx(ge,{children:"Méthode"}),i.jsx(ge,{children:"Notes"}),i.jsx(ge,{children:"Créé par"}),H.canManageEmployeePayments&&i.jsx(ge,{children:"Actions"})]})}),i.jsx(ye,{children:R?i.jsx(xe,{children:i.jsx(ge,{colSpan:8,align:"center",children:i.jsx(qe,{})})}):0===n.length?i.jsx(xe,{children:i.jsx(ge,{colSpan:8,align:"center",children:i.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun paiement employé trouvé"})})}):n.slice(p*g,p*g+g).map(e=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(a,{display:"flex",alignItems:"center",children:[i.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomEmploye]})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Na(e.montantCDF,"CDF")})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:Na(e.montantUSD||0,"USD")})}),i.jsx(ge,{children:i.jsxs(a,{display:"flex",alignItems:"center",children:[i.jsx(it,{sx:{mr:1,fontSize:16,color:"text.secondary"}}),Ut(new Date(e.datePaiement),"dd/MM/yyyy",{locale:xa})]})}),i.jsx(ge,{children:i.jsx(A,{label:de(e.methodePaiement),size:"small",color:"cash"===e.methodePaiement?"success":"mobile_money"===e.methodePaiement?"info":"default"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",noWrap:!0,sx:{maxWidth:150},children:e.notes||"-"})}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:e.creePar})}),H.canManageEmployeePayments&&i.jsxs(ge,{children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>oe(e),disabled:R,children:i.jsx(Re,{})})}),i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${e.nomEmploye} ?`))try{M(!0),await la.deleteEmployeePayment(e.id),P("Paiement employé supprimé avec succès"),await Z()}catch(t){console.error("Error deleting employee payment:",t),w("Erreur lors de la suppression du paiement employé")}finally{M(!1)}})(e),disabled:R,color:"error",children:i.jsx(q,{})})})]})]},e.id))})]})}),i.jsx(Me,{rowsPerPageOptions:[5,10,25,50,100],component:"div",count:n.length,rowsPerPage:g,page:p,onPageChange:(e,t)=>{x(t)},onRowsPerPageChange:e=>{y(parseInt(e.target.value,10)),x(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:j,onClose:le,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ie,{children:C?"Modifier le Paiement Employé":"Nouveau Paiement Employé"}),i.jsx(Ne,{children:i.jsx(a,{sx:{pt:1},children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsxs(je,{fullWidth:!0,disabled:R,children:[i.jsx(Se,{id:"employee-select-label",children:"Nom de l'employé *"}),i.jsxs(Ce,{labelId:"employee-select-label",value:v.nomEmploye,onChange:e=>b({...v,nomEmploye:e.target.value}),label:"Nom de l'employé *",startAdornment:i.jsx(ie,{position:"start",children:i.jsx(lt,{})}),children:[i.jsx(J,{value:"",disabled:!0,children:i.jsx("em",{children:"Sélectionner un employé"})}),N.filter(e=>"actif"===e.statut).sort((e,t)=>e.nomComplet.localeCompare(t.nomComplet,"fr",{sensitivity:"base"})).map(e=>i.jsx(J,{value:e.nomComplet,children:i.jsxs(a,{sx:{display:"flex",flexDirection:"column",alignItems:"flex-start"},children:[i.jsx(o,{variant:"body1",sx:{fontWeight:500},children:e.nomComplet}),i.jsx(o,{variant:"caption",color:"text.secondary",children:e.poste})]})},e.id)),0===N.filter(e=>"actif"===e.statut).length&&i.jsx(J,{value:"",disabled:!0,children:i.jsx("em",{children:"Aucun employé actif disponible"})})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Xa,{label:"Montant du paiement *",value:v.montantCDF,onChange:e=>b({...v,montantCDF:e}),currency:"CDF",disabled:R,fullWidth:!0})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsx(ne,{fullWidth:!0,type:"date",label:"Date de paiement *",value:v.datePaiement,onChange:e=>b({...v,datePaiement:e.target.value}),disabled:R,InputLabelProps:{shrink:!0},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(it,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Méthode de paiement *"}),i.jsx(Ce,{value:v.methodePaiement,onChange:e=>b({...v,methodePaiement:e.target.value}),label:"Méthode de paiement *",disabled:R,children:K.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,multiline:!0,rows:3,label:"Notes (optionnel)",value:v.notes,onChange:e=>b({...v,notes:e.target.value}),disabled:R,placeholder:"Ajoutez des notes sur ce paiement..."})}),v.montantCDF>0&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(a,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[i.jsxs(o,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["Équivalent USD (taux: ",T.tauxChangeUSDCDF," CDF/USD)"]}),i.jsx(o,{variant:"h6",color:"primary",children:Na(v.montantCDF/T.tauxChangeUSDCDF,"USD")})]})})]})})}),i.jsxs(Le,{children:[i.jsx(I,{onClick:le,disabled:R,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var e;try{if(M(!0),w(""),!v.nomEmploye.trim())return void w("Veuillez sélectionner un employé");if(!N.find(e=>e.nomComplet===v.nomEmploye&&"actif"===e.statut))return void w("L'employé sélectionné n'est pas valide ou n'est plus actif");const t=Ua(v.montantCDF,"Le montant du paiement",{allowZero:!1,allowNegative:!1});if(!t.isValid)return void w(t.errors[0]||ka);const n={id:(null==C?void 0:C.id)||`emp_pay_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomEmploye:v.nomEmploye.trim(),montantCDF:v.montantCDF,montantUSD:v.montantCDF/T.tauxChangeUSDCDF,datePaiement:v.datePaiement,methodePaiement:v.methodePaiement,notes:null==(e=v.notes)?void 0:e.trim(),creePar:(null==G?void 0:G.nom)||"Système",dateCreation:(null==C?void 0:C.dateCreation)||(new Date).toISOString(),dateModification:C?(new Date).toISOString():void 0};C?(await la.updateEmployeePayment(n),P("Paiement employé modifié avec succès")):(await la.addEmployeePayment(n),P("Paiement employé ajouté avec succès")),await Z(),le()}catch(t){console.error("Error saving employee payment:",t),w("Erreur lors de la sauvegarde du paiement employé")}finally{M(!1)}},variant:"contained",disabled:R||!v.nomEmploye.trim()||v.montantCDF<=0||!N.find(e=>e.nomComplet===v.nomEmploye&&"actif"===e.statut),startIcon:R?i.jsx(qe,{size:20}):i.jsx(Y,{}),children:C?"Modifier":"Ajouter"})]})]})]}),1===X&&i.jsxs(i.Fragment,{children:[i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Employés"}),i.jsx(o,{variant:"h5",children:N.length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Actifs"}),i.jsx(o,{variant:"h5",children:N.filter(e=>"actif"===e.statut).length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Masse Salariale (CDF)"}),i.jsx(o,{variant:"h5",children:Na(N.filter(e=>"actif"===e.statut).reduce((e,t)=>e+t.salaireCDF,0),"CDF")})]})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(a,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[i.jsx(o,{variant:"h6",children:"Liste des Employés"}),H.canManageEmployeePayments&&i.jsx(I,{variant:"contained",startIcon:i.jsx(Pe,{}),onClick:()=>ue(),children:"Ajouter Employé"})]})}),i.jsx(c,{children:i.jsx(me,{children:i.jsxs(he,{children:[i.jsx(pe,{children:i.jsxs(xe,{children:[i.jsx(ge,{children:"Nom"}),i.jsx(ge,{children:"Poste"}),i.jsx(ge,{children:"Salaire (CDF)"}),i.jsx(ge,{children:"Date d'Embauche"}),i.jsx(ge,{children:"Statut"}),i.jsx(ge,{children:"Téléphone"}),H.canManageEmployeePayments&&i.jsx(ge,{children:"Actions"})]})}),i.jsx(ye,{children:0===N.length?i.jsx(xe,{children:i.jsx(ge,{colSpan:7,align:"center",children:i.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun employé trouvé"})})}):N.map(e=>i.jsxs(xe,{children:[i.jsx(ge,{children:i.jsxs(a,{display:"flex",alignItems:"center",children:[i.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomComplet," "]})}),i.jsx(ge,{children:e.poste}),i.jsx(ge,{children:i.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Na(e.salaireCDF,"CDF")})}),i.jsx(ge,{children:Ut(new Date(e.dateEmbauche),"dd/MM/yyyy",{locale:xa})}),i.jsx(ge,{children:i.jsx(A,{label:e.statut,color:"actif"===e.statut?"success":"default",size:"small"})}),i.jsx(ge,{children:e.telephone||"-"}),H.canManageEmployeePayments&&i.jsx(ge,{children:i.jsxs(a,{display:"flex",gap:1,children:[i.jsx(F,{title:"Modifier",children:i.jsx(U,{size:"small",onClick:()=>ue(e),children:i.jsx(Re,{fontSize:"small"})})}),i.jsx(F,{title:"Supprimer",children:i.jsx(U,{size:"small",color:"error",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'employé ${e.nomComplet} ?`))try{M(!0),await la.deleteEmployee(e.id),P("Employé supprimé avec succès"),await ae()}catch(t){console.error("Error deleting employee:",t),w("Erreur lors de la suppression de l'employé")}finally{M(!1)}})(e),children:i.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]})})}),i.jsxs(Ae,{open:V,onClose:De,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ie,{children:_?"Modifier Employé":"Ajouter Employé"}),i.jsxs(Ne,{children:[f&&i.jsx(O,{severity:"error",sx:{mb:2},children:f}),E&&i.jsx(O,{severity:"success",sx:{mb:2},children:E}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom Complet *",value:$.nomComplet,onChange:e=>W({...$,nomComplet:e.target.value}),placeholder:"Ex: Jean Dupont"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Poste *",value:$.poste,onChange:e=>W({...$,poste:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Xa,{label:"Salaire",value:$.salaireCDF,onChange:e=>W({...$,salaireCDF:e}),min:0,max:1e7,step:1e3,exchangeRate:T.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date d'Embauche",type:"date",value:$.dateEmbauche,onChange:e=>W({...$,dateEmbauche:e.target.value}),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(je,{fullWidth:!0,children:[i.jsx(Se,{children:"Statut"}),i.jsxs(Ce,{value:$.statut,label:"Statut",onChange:e=>W({...$,statut:e.target.value}),children:[i.jsx(J,{value:"actif",children:"Actif"}),i.jsx(J,{value:"inactif",children:"Inactif"}),i.jsx(J,{value:"suspendu",children:"Suspendu"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:$.telephone,onChange:e=>W({...$,telephone:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:$.adresse,onChange:e=>W({...$,adresse:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:$.notes,onChange:e=>W({...$,notes:e.target.value})})})]})]}),i.jsxs(Le,{children:[i.jsx(I,{onClick:De,children:"Annuler"}),i.jsx(I,{onClick:async()=>{var e,t,n;if($.nomComplet.trim()&&$.poste.trim())try{M(!0);const i=(new Date).toISOString(),a={id:(null==_?void 0:_.id)||`emp_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomComplet:$.nomComplet.trim(),poste:$.poste.trim(),salaireCDF:$.salaireCDF,salaireUSD:$.salaireCDF/T.tauxChangeUSDCDF,dateEmbauche:$.dateEmbauche,telephone:null==(e=$.telephone)?void 0:e.trim(),adresse:null==(t=$.adresse)?void 0:t.trim(),statut:$.statut,notes:null==(n=$.notes)?void 0:n.trim(),creePar:(null==G?void 0:G.nom)||"Système",dateCreation:(null==_?void 0:_.dateCreation)||i,dateModification:_?i:void 0};_?(await la.updateEmployee(a),P("Employé modifié avec succès")):(await la.addEmployee(a),P("Employé ajouté avec succès")),await ae(),De()}catch(i){console.error("Error saving employee:",i),w("Erreur lors de la sauvegarde de l'employé")}finally{M(!1)}else w("Le nom complet et le poste sont requis")},variant:"contained",disabled:R||!$.nomComplet.trim()||!$.poste.trim(),startIcon:R?i.jsx(qe,{size:20}):i.jsx(lt,{}),children:_?"Modifier":"Ajouter"})]})]})]})]})},pr=()=>{var e,t,n,i,a,r,s,o,l;const c=(()=>{const e=Ri();return{components:{MuiButton:{styleOverrides:{root:{minHeight:e.isMobile?48:36,fontSize:e.isMobile?"1rem":"0.875rem"}}},MuiIconButton:{styleOverrides:{root:{padding:e.isMobile?12:8}}},MuiTableCell:{styleOverrides:{root:{padding:e.isMobile?"12px 8px":"16px"}}}}}})();return Ct({palette:{primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",...null==(n=null==(t=null==(e=c.components)?void 0:e.MuiButton)?void 0:t.styleOverrides)?void 0:n.root}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}},MuiIconButton:{styleOverrides:{root:{...null==(r=null==(a=null==(i=c.components)?void 0:i.MuiIconButton)?void 0:a.styleOverrides)?void 0:r.root}}},MuiTableCell:{styleOverrides:{root:{...null==(l=null==(o=null==(s=c.components)?void 0:s.MuiTableCell)?void 0:o.styleOverrides)?void 0:l.root}}}}},yi)},xr=()=>{const[e,t]=vt.useState(null),[n,r]=vt.useState(!0),[s,o]=vt.useState(null),[l,c]=vt.useState(()=>pr());vt.useEffect(()=>{const e=()=>{document.querySelectorAll("input, textarea, [contenteditable]").forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})};e();const t=()=>{setTimeout(e,50)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),vt.useEffect(()=>{(async()=>{try{0,await la.initializeDefaultData();Ri().isMobile&&await la.migrateFromDesktop(),await ca.initialize();const e=ca.getCurrentUser();t(e),r(!1)}catch(e){console.error("SmartBoutique: Error during initialization:",e),o(e instanceof Error?e.message:"Unknown error"),r(!1)}})()},[]);return s?i.jsxs(jt,{theme:l,children:[i.jsx(St,{}),i.jsxs(a,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,children:[i.jsx("h2",{children:"Erreur de chargement"}),i.jsx("p",{children:"Une erreur s'est produite lors du chargement de l'application:"}),i.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:s}),i.jsx("button",{onClick:()=>window.location.reload(),children:"Recharger l'application"})]})]}):n?i.jsxs(jt,{theme:l,children:[i.jsx(St,{}),i.jsx(a,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",children:"Chargement..."})]}):i.jsxs(jt,{theme:l,children:[i.jsx(St,{}),i.jsx(mi,{children:i.jsxs(ci,{children:[i.jsx(oi,{path:"/login",element:e?i.jsx(ri,{to:"/dashboard",replace:!0}):i.jsx(ba,{onLogin:e=>{t(e)}})}),i.jsxs(oi,{path:"/",element:i.jsx(Da,{children:i.jsx(Ca,{currentUser:e,onLogout:async()=>{await ca.logout(),t(null)}})}),children:[i.jsx(oi,{index:!0,element:i.jsx(ri,{to:"/dashboard",replace:!0})}),i.jsx(oi,{path:"dashboard",element:i.jsx(Da,{requiredPermission:"canViewDashboard",children:i.jsx(Wa,{})})}),i.jsx(oi,{path:"products",element:i.jsx(Da,{requiredPermission:"canViewProducts",children:i.jsx(Ja,{})})}),i.jsx(oi,{path:"sales",element:i.jsx(Da,{requiredPermission:"canViewSales",children:i.jsx(Za,{})})}),i.jsx(oi,{path:"debts",element:i.jsx(Da,{requiredPermission:"canViewDebts",children:i.jsx(er,{})})}),i.jsx(oi,{path:"expenses",element:i.jsx(Da,{requiredPermission:"canViewExpenses",children:i.jsx(nr,{})})}),i.jsx(oi,{path:"employee-payments",element:i.jsx(Da,{requiredPermission:"canViewEmployeePayments",children:i.jsx(hr,{})})}),i.jsx(oi,{path:"reports",element:i.jsx(Da,{requiredPermission:"canViewReports",children:i.jsx(ar,{})})}),i.jsx(oi,{path:"users",element:i.jsx(Da,{requiredPermission:"canViewUsers",children:i.jsx(rr,{})})}),i.jsx(oi,{path:"settings",element:i.jsx(Da,{requiredPermission:"canViewSettings",children:i.jsx(mr,{})})})]}),i.jsx(oi,{path:"*",element:i.jsx(ri,{to:"/dashboard",replace:!0})})]})})]})};class gr extends vt.Component{constructor(e){super(e),n(this,"handleReload",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()}),n(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?i.jsx(a,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,bgcolor:"#f5f5f5",children:i.jsxs(c,{elevation:3,sx:{p:4,maxWidth:600,width:"100%"},children:[i.jsx(o,{variant:"h4",color:"error",gutterBottom:!0,children:"Oops! Une erreur s'est produite"}),i.jsx(o,{variant:"body1",paragraph:!0,children:"L'application a rencontré une erreur inattendue. Vous pouvez essayer de continuer ou recharger l'application."}),!1,i.jsxs(a,{display:"flex",gap:2,mt:3,children:[i.jsx(I,{variant:"contained",color:"primary",onClick:this.handleReset,children:"Continuer"}),i.jsx(I,{variant:"outlined",color:"secondary",onClick:this.handleReload,children:"Recharger l'application"})]})]})}):this.props.children}}_t.register(zt,$t,Wt,Xt,Qt,Jt,Ht,Yt,Gt);const yr=document.getElementById("root");yr?en.createRoot(yr).render(i.jsx(ft.StrictMode,{children:i.jsx(gr,{children:i.jsx(xr,{})})})):console.error("SmartBoutique: Root element not found!");export{Ei as W};
