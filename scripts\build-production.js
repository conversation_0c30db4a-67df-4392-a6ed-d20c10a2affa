#!/usr/bin/env node

/**
 * SmartBoutique Production Build Script
 * 
 * This script ensures proper production builds with all necessary optimizations
 * for client distribution.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 SmartBoutique - Production Build Script');
console.log('==========================================\n');

// Check if we're in the right directory
if (!fs.existsSync('package.json')) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Read package.json to get version
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
console.log(`📦 Building SmartBoutique v${packageJson.version}`);
console.log(`📝 Description: ${packageJson.description}\n`);

try {
  // Clean previous builds
  console.log('🧹 Cleaning previous builds...');
  if (fs.existsSync('dist')) {
    execSync('rmdir /s /q dist', { stdio: 'inherit', shell: true });
  }
  if (fs.existsSync('dist-electron')) {
    execSync('rmdir /s /q dist-electron', { stdio: 'inherit', shell: true });
  }
  if (fs.existsSync('release-final')) {
    execSync('rmdir /s /q release-final', { stdio: 'inherit', shell: true });
  }

  // Install dependencies if needed
  console.log('📥 Checking dependencies...');
  if (!fs.existsSync('node_modules')) {
    console.log('📥 Installing dependencies...');
    execSync('npm install', { stdio: 'inherit' });
  }

  // Build Vite application
  console.log('🔨 Building Vite application...');
  execSync('npm run build:vite', { stdio: 'inherit' });

  // Build Electron main process
  console.log('⚡ Building Electron main process...');
  execSync('npm run build:electron', { stdio: 'inherit' });

  // Verify build files exist
  console.log('✅ Verifying build files...');
  if (!fs.existsSync('dist/index.html')) {
    throw new Error('Vite build failed - dist/index.html not found');
  }
  if (!fs.existsSync('dist-electron/main.js')) {
    throw new Error('Electron build failed - dist-electron/main.js not found');
  }

  console.log('✅ Build completed successfully!');
  console.log('\n📋 Build Summary:');
  console.log(`   • Vite build: dist/`);
  console.log(`   • Electron build: dist-electron/`);
  console.log(`   • Ready for packaging with electron-builder`);
  
  console.log('\n🎯 Next steps:');
  console.log('   • Run "npm run dist:win" to create Windows installer');
  console.log('   • Run "npm run dist:portable" to create portable exe');
  console.log('   • Check release-final/ folder for distribution files');

} catch (error) {
  console.error('\n❌ Build failed:', error.message);
  process.exit(1);
}
