import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  Alert,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
} from '@mui/material';
import {
  TrendingUp,
  Inventory,
  PointOfSale,
  AccountBalance,
  Warning,
  CheckCircle,
  Error,
} from '@mui/icons-material';
import { Line, Doughnut } from 'react-chartjs-2';

// Import standardized currency utilities
import {
  roundCurrency,
  convertCDFToUSD,
  calculateChiffresAffaires,
  calculateBenefice,
  DEFAULT_EXCHANGE_RATE
} from '@/utils/currencyUtils.js';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';

// Hooks - removed usePostLoginFocusFix as it was causing input delays

// Types
import { Product, Sale, Debt, DashboardStats, TimePeriod } from '@/types';

// Components
import { RevenueDashboard } from '@/components/Revenue/RevenueDashboard';

// Utils
import { format, subDays, isAfter, isBefore, startOfDay, endOfDay } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatDualCurrencyForCard } from '@/utils';
import { generateRevenueAnalytics, RevenueAnalytics } from '@/utils/revenue';

const DashboardPage: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [lowStockProducts, setLowStockProducts] = useState<Product[]>([]);
  const [recentSales, setRecentSales] = useState<Sale[]>([]);
  const [salesChartData, setSalesChartData] = useState<any>(null);
  const [paymentMethodData, setPaymentMethodData] = useState<any>(null);
  const [revenueAnalytics, setRevenueAnalytics] = useState<RevenueAnalytics | null>(null);
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 }); // Default exchange rate
  // State for time period selector
  const [selectedTimePeriod, setSelectedTimePeriod] = useState<TimePeriod>('jour');

  const permissions = adaptiveAuthService.getUserPermissions();

  // Helper function to safely parse dates
  const parseDate = (dateString: string): Date | null => {
    try {
      const date = new Date(dateString);
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date found:', dateString);
        return null;
      }
      return date;
    } catch (error) {
      console.warn('Error parsing date:', dateString, error);
      return null;
    }
  };

  // Load settings and dashboard data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        const appSettings = await adaptiveStorageService.getSettings();
        setSettings(appSettings);
        // Load dashboard data after settings are loaded
        await loadDashboardData();
      } catch (error) {
        console.error('Error loading data:', error);
        // Load dashboard data with default settings if loading fails
        await loadDashboardData();
      }
    };
    loadData();
  }, []);

  // Helper function to get sales data based on selected time period
  const getSalesDataForPeriod = (timePeriod: TimePeriod, stats: DashboardStats) => {
    switch (timePeriod) {
      case 'jour':
        return stats.ventesDuJour;
      case 'semaine':
        return stats.ventesDeLaSemaine;
      case 'mois':
        return stats.ventesDuMois;
      default:
        return stats.ventesDuJour;
    }
  };

  // Helper function to get period label
  const getPeriodLabel = (timePeriod: TimePeriod) => {
    switch (timePeriod) {
      case 'jour':
        return 'Ventes du jour';
      case 'semaine':
        return 'Ventes de la semaine';
      case 'mois':
        return 'Ventes du mois';
      default:
        return 'Ventes du jour';
    }
  };

  const loadDashboardData = async () => {
    const products = await adaptiveStorageService.getProducts();
    const sales = await adaptiveStorageService.getSales();
    const debts = await adaptiveStorageService.getDebts();
    const settings = await adaptiveStorageService.getSettings();

    // Calculate stats
    const today = new Date();
    const startOfToday = startOfDay(today);
    const endOfToday = endOfDay(today);
    const weekAgo = subDays(today, 7);
    const monthAgo = subDays(today, 30);

    // Sales stats with safe date parsing
    const todaySales = sales.filter((sale: Sale) => {
      const saleDate = parseDate(sale.datevente);
      if (!saleDate) return false;
      return isAfter(saleDate, startOfToday) && isBefore(saleDate, endOfToday);
    });

    const weekSales = sales.filter((sale: Sale) => {
      const saleDate = parseDate(sale.datevente);
      if (!saleDate) return false;
      return isAfter(saleDate, weekAgo);
    });

    const monthSales = sales.filter((sale: Sale) => {
      const saleDate = parseDate(sale.datevente);
      if (!saleDate) return false;
      return isAfter(saleDate, monthAgo);
    });

    // Product stats
    const activeProducts = products.filter((p: Product) => p.stock > 0);
    const lowStock = products.filter((p: Product) => p.stock <= p.stockMin && p.stock > 0);
    // Out-of-stock products (Articles en rupture)
    const outOfStockProducts = products.filter((p: Product) => p.stock === 0);

    // Debt stats
    const activeDebts = debts.filter((d: Debt) => d.statut === 'active');
    const overdue = debts.filter((d: Debt) => d.statut === 'overdue');

    // Calculate totals using standardized currency utilities
    const todayRevenueCDF = roundCurrency(todaySales.reduce((sum: number, sale: Sale) => sum + sale.totalCDF, 0));
    const weekRevenueCDF = roundCurrency(weekSales.reduce((sum: number, sale: Sale) => sum + sale.totalCDF, 0));
    const monthRevenueCDF = roundCurrency(monthSales.reduce((sum: number, sale: Sale) => sum + sale.totalCDF, 0));

    const inventoryValueCDF = roundCurrency(products.reduce((sum: number, product: Product) =>
      sum + (product.prixCDF * product.stock), 0));

    const totalDebtsCDF = roundCurrency(activeDebts.reduce((sum: number, debt: Debt) =>
      sum + debt.montantRestantCDF, 0));

    // Use standardized exchange rate and conversion
    const exchangeRate = settings.tauxChangeUSDCDF || DEFAULT_EXCHANGE_RATE;

    const dashboardStats: DashboardStats = {
      ventesDuJour: {
        nombreVentes: todaySales.length,
        revenusCDF: todayRevenueCDF,
        revenusUSD: convertCDFToUSD(todayRevenueCDF, exchangeRate),
      },
      ventesDeLaSemaine: {
        nombreVentes: weekSales.length,
        revenusCDF: weekRevenueCDF,
        revenusUSD: convertCDFToUSD(weekRevenueCDF, exchangeRate),
      },
      ventesDuMois: {
        nombreVentes: monthSales.length,
        revenusCDF: monthRevenueCDF,
        revenusUSD: convertCDFToUSD(monthRevenueCDF, exchangeRate),
      },
      articlesActifs: activeProducts.length,
      produitsStockBas: lowStock.length,
      articlesEnRupture: outOfStockProducts.length,
      valeurInventaireCDF: inventoryValueCDF,
      valeurInventaireUSD: inventoryValueCDF / settings.tauxChangeUSDCDF,
      dettesActives: activeDebts.length,
      dettesEnRetard: overdue.length,
      montantDettesTotalCDF: totalDebtsCDF,
      montantDettesTotalUSD: totalDebtsCDF / settings.tauxChangeUSDCDF,
    };

    setStats(dashboardStats);
    setLowStockProducts(lowStock.slice(0, 5));
    setRecentSales(sales.slice(-5).reverse());

    // Generate revenue analytics for Super Admin
    if (permissions.canViewRevenue) {
      const analytics = generateRevenueAnalytics(products, sales);
      setRevenueAnalytics(analytics);
    }

    // Prepare chart data
    prepareSalesChart(sales);
    preparePaymentMethodChart(sales);
  };

  const prepareSalesChart = (sales: Sale[]) => {
    const last14Days = Array.from({ length: 14 }, (_, i) => {
      const date = subDays(new Date(), 13 - i);
      return {
        date,
        label: format(date, 'dd/MM', { locale: fr }),
        sales: 0,
        revenue: 0,
      };
    });

    sales.forEach((sale: Sale) => {
      const saleDate = parseDate(sale.datevente);
      if (!saleDate) return; // Skip invalid dates

      const dayData = last14Days.find(day =>
        format(day.date, 'yyyy-MM-dd') === format(saleDate, 'yyyy-MM-dd')
      );
      if (dayData) {
        dayData.sales += 1;
        dayData.revenue += sale.totalCDF;
      }
    });

    setSalesChartData({
      labels: last14Days.map(day => day.label),
      datasets: [
        {
          label: 'Profit (CDF)',
          data: last14Days.map(day => day.revenue),
          borderColor: 'rgb(75, 192, 192)',
          backgroundColor: 'rgba(75, 192, 192, 0.2)',
          tension: 0.1,
        },
      ],
    });
  };

  const preparePaymentMethodChart = (sales: Sale[]) => {
    const methods = sales.reduce((acc: any, sale: Sale) => {
      acc[sale.methodePaiement] = (acc[sale.methodePaiement] || 0) + 1;
      return acc;
    }, {});

    setPaymentMethodData({
      labels: ['Cash', 'Banque', 'Mobile Money'],
      datasets: [
        {
          data: [
            methods.cash || 0,
            methods.banque || 0,
            methods.mobile_money || 0,
          ],
          backgroundColor: [
            '#FF6384',
            '#36A2EB',
            '#FFCE56',
          ],
        },
      ],
    });
  };



  const getStockStatusIcon = (product: Product) => {
    if (product.stock === 0) return <Error color="error" />;
    if (product.stock <= product.stockMin) return <Warning color="warning" />;
    return <CheckCircle color="success" />;
  };

  const formatCurrency = (amount: number, currency: 'USD' | 'CDF') => {
    if (currency === 'USD') {
      return `$${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })}`;
    }
    return `${amount.toLocaleString('fr-FR')} CDF`;
  };

  if (!stats) {
    return <Typography>Chargement...</Typography>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Tableau de bord
      </Typography>

      {/* Revenue Analytics Section - Super Admin Only */}
      {permissions.canViewRevenue && revenueAnalytics && (
        <Box sx={{ mb: 4 }}>
          <RevenueDashboard
            analytics={revenueAnalytics}
            products={lowStockProducts} // Pass products for context
            todaySalesData={stats?.ventesDuJour} // Pass today's sales data
            debtsData={stats ? {
              montantDettesTotalCDF: stats.montantDettesTotalCDF,
              montantDettesTotalUSD: stats.montantDettesTotalUSD,
              dettesActives: stats.dettesActives
            } : undefined} // Pass debt data
            isLoading={!revenueAnalytics}
          />
        </Box>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Sales with Time Period Selector */}
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={1}>
                <FormControl size="small" sx={{ minWidth: 120 }}>
                  <InputLabel>Période</InputLabel>
                  <Select
                    value={selectedTimePeriod}
                    label="Période"
                    onChange={(e) => setSelectedTimePeriod(e.target.value as TimePeriod)}
                  >
                    <MenuItem value="jour">Jour</MenuItem>
                    <MenuItem value="semaine">Semaine</MenuItem>
                    <MenuItem value="mois">Mois</MenuItem>
                  </Select>
                </FormControl>
                <PointOfSale color="primary" sx={{ fontSize: 40 }} />
              </Box>
              <Box>
                <Typography color="textSecondary" gutterBottom variant="body2">
                  {getPeriodLabel(selectedTimePeriod)}
                </Typography>
                <Typography variant="h6">
                  {getSalesDataForPeriod(selectedTimePeriod, stats).nombreVentes}
                </Typography>
                {permissions.canViewFinancials && (
                  <>
                    <Typography variant="body2" color="primary" fontWeight="medium">
                      {formatDualCurrencyForCard(getSalesDataForPeriod(selectedTimePeriod, stats).revenusCDF, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(getSalesDataForPeriod(selectedTimePeriod, stats).revenusCDF, settings.tauxChangeUSDCDF).primaryCurrency}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ≈ ${formatDualCurrencyForCard(getSalesDataForPeriod(selectedTimePeriod, stats).revenusCDF, settings.tauxChangeUSDCDF).secondaryAmount}
                    </Typography>
                  </>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Week's Sales */}
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Ventes de la semaine
                  </Typography>
                  <Typography variant="h6">
                    {stats.ventesDeLaSemaine.nombreVentes}
                  </Typography>
                  {permissions.canViewFinancials && (
                    <>
                      <Typography variant="body2" color="primary" fontWeight="medium">
                        {formatDualCurrencyForCard(stats.ventesDeLaSemaine.revenusCDF, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(stats.ventesDeLaSemaine.revenusCDF, settings.tauxChangeUSDCDF).primaryCurrency}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        ≈ ${formatDualCurrencyForCard(stats.ventesDeLaSemaine.revenusCDF, settings.tauxChangeUSDCDF).secondaryAmount}
                      </Typography>
                    </>
                  )}
                </Box>
                <TrendingUp color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Active Products */}
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Articles actifs
                  </Typography>
                  <Typography variant="h6">
                    {stats.articlesActifs}
                  </Typography>
                  {stats.produitsStockBas > 0 && (
                    <Chip 
                      label={`${stats.produitsStockBas} stock bas`}
                      color="warning"
                      size="small"
                    />
                  )}
                </Box>
                <Inventory color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Inventory Value */}
        <Grid item xs={12} sm={6} md={4} lg={2.4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Valeur Inventaire
                  </Typography>
                  {permissions.canViewFinancials ? (
                    <>
                      <Typography variant="h6" color="primary" fontWeight="medium">
                        {formatDualCurrencyForCard(stats.valeurInventaireCDF, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(stats.valeurInventaireCDF, settings.tauxChangeUSDCDF).primaryCurrency}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        ≈ ${formatDualCurrencyForCard(stats.valeurInventaireCDF, settings.tauxChangeUSDCDF).secondaryAmount}
                      </Typography>
                    </>
                  ) : (
                    <Typography variant="h6">
                      {stats.articlesActifs} articles
                    </Typography>
                  )}
                </Box>
                <Inventory color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>


      </Grid>

      {/* Out of Stock Alert Section */}
      {stats.articlesEnRupture > 0 && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12}>
            <Alert severity="error" sx={{ mb: 2 }}>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography variant="h6" component="div">
                    Articles en rupture
                  </Typography>
                  <Typography variant="body2">
                    {stats.articlesEnRupture} article{stats.articlesEnRupture > 1 ? 's' : ''} en rupture de stock nécessite{stats.articlesEnRupture > 1 ? 'nt' : ''} un réapprovisionnement urgent
                  </Typography>
                </Box>
                <Error sx={{ fontSize: 40 }} />
              </Box>
            </Alert>
          </Grid>
        </Grid>
      )}

      {/* Charts Row */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {/* Sales Trend */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Tendance des ventes (14 derniers jours)
            </Typography>
            {salesChartData && (
              <Line 
                data={salesChartData}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: 'top' as const,
                    },
                  },
                  scales: {
                    y: {
                      beginAtZero: true,
                    },
                  },
                }}
              />
            )}
          </Paper>
        </Grid>

        {/* Payment Methods */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Méthodes de paiement
            </Typography>
            {paymentMethodData && (
              <Doughnut 
                data={paymentMethodData}
                options={{
                  responsive: true,
                  plugins: {
                    legend: {
                      position: 'bottom' as const,
                    },
                  },
                }}
              />
            )}
          </Paper>
        </Grid>
      </Grid>

      {/* Bottom Row */}
      <Grid container spacing={3}>
        {/* Low Stock Products */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Produits en stock bas
            </Typography>
            {lowStockProducts.length === 0 ? (
              <Alert severity="success">Aucun produit en stock bas</Alert>
            ) : (
              <List dense>
                {lowStockProducts.map((product) => (
                  <ListItem key={product.id}>
                    <ListItemIcon>
                      {getStockStatusIcon(product)}
                    </ListItemIcon>
                    <ListItemText
                      primary={product.nom}
                      secondary={`Stock: ${product.stock} (Min: ${product.stockMin})`}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Recent Sales */}
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Ventes récentes
            </Typography>
            {recentSales.length === 0 ? (
              <Alert severity="info">Aucune vente récente</Alert>
            ) : (
              <List dense>
                {recentSales.map((sale) => (
                  <ListItem key={sale.id}>
                    <ListItemText
                      primary={permissions.canViewFinancials
                        ? `${sale.nomClient} - ${formatDualCurrencyForCard(sale.totalCDF, settings.tauxChangeUSDCDF).primaryAmount} ${formatDualCurrencyForCard(sale.totalCDF, settings.tauxChangeUSDCDF).primaryCurrency}`
                        : sale.nomClient
                      }
                      secondary={(() => {
                        const saleDate = parseDate(sale.datevente);
                        const dateText = saleDate
                          ? format(saleDate, 'dd/MM/yyyy HH:mm', { locale: fr })
                          : 'Date invalide';

                        if (permissions.canViewFinancials) {
                          const usdAmount = formatDualCurrencyForCard(sale.totalCDF, settings.tauxChangeUSDCDF).secondaryAmount;
                          return `${dateText} • ≈ $${usdAmount}`;
                        }
                        return dateText;
                      })()}
                    />
                  </ListItem>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default DashboardPage;
