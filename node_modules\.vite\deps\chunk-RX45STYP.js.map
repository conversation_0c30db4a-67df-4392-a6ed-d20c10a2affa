{"version": 3, "sources": ["../../@capacitor/core/build/util.js", "../../@capacitor/core/build/runtime.js", "../../@capacitor/core/build/global.js", "../../@capacitor/core/build/web-plugin.js", "../../@capacitor/core/build/core-plugins.js"], "sourcesContent": ["export var ExceptionCode;\n(function (ExceptionCode) {\n    /**\n     * API is not implemented.\n     *\n     * This usually means the API can't be used because it is not implemented for\n     * the current platform.\n     */\n    ExceptionCode[\"Unimplemented\"] = \"UNIMPLEMENTED\";\n    /**\n     * API is not available.\n     *\n     * This means the API can't be used right now because:\n     *   - it is currently missing a prerequisite, such as network connectivity\n     *   - it requires a particular platform or browser version\n     */\n    ExceptionCode[\"Unavailable\"] = \"UNAVAILABLE\";\n})(ExceptionCode || (ExceptionCode = {}));\nexport class CapacitorException extends Error {\n    constructor(message, code, data) {\n        super(message);\n        this.message = message;\n        this.code = code;\n        this.data = data;\n    }\n}\nexport const getPlatformId = (win) => {\n    var _a, _b;\n    if (win === null || win === void 0 ? void 0 : win.androidBridge) {\n        return 'android';\n    }\n    else if ((_b = (_a = win === null || win === void 0 ? void 0 : win.webkit) === null || _a === void 0 ? void 0 : _a.messageHandlers) === null || _b === void 0 ? void 0 : _b.bridge) {\n        return 'ios';\n    }\n    else {\n        return 'web';\n    }\n};\n//# sourceMappingURL=util.js.map", "import { CapacitorException, getPlatformId, ExceptionCode } from './util';\nexport const createCapacitor = (win) => {\n    const capCustomPlatform = win.CapacitorCustomPlatform || null;\n    const cap = win.Capacitor || {};\n    const Plugins = (cap.Plugins = cap.Plugins || {});\n    const getPlatform = () => {\n        return capCustomPlatform !== null ? capCustomPlatform.name : getPlatformId(win);\n    };\n    const isNativePlatform = () => getPlatform() !== 'web';\n    const isPluginAvailable = (pluginName) => {\n        const plugin = registeredPlugins.get(pluginName);\n        if (plugin === null || plugin === void 0 ? void 0 : plugin.platforms.has(getPlatform())) {\n            // JS implementation available for the current platform.\n            return true;\n        }\n        if (getPluginHeader(pluginName)) {\n            // Native implementation available.\n            return true;\n        }\n        return false;\n    };\n    const getPluginHeader = (pluginName) => { var _a; return (_a = cap.PluginHeaders) === null || _a === void 0 ? void 0 : _a.find((h) => h.name === pluginName); };\n    const handleError = (err) => win.console.error(err);\n    const registeredPlugins = new Map();\n    const registerPlugin = (pluginName, jsImplementations = {}) => {\n        const registeredPlugin = registeredPlugins.get(pluginName);\n        if (registeredPlugin) {\n            console.warn(`Capacitor plugin \"${pluginName}\" already registered. Cannot register plugins twice.`);\n            return registeredPlugin.proxy;\n        }\n        const platform = getPlatform();\n        const pluginHeader = getPluginHeader(pluginName);\n        let jsImplementation;\n        const loadPluginImplementation = async () => {\n            if (!jsImplementation && platform in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations[platform] === 'function'\n                        ? (jsImplementation = await jsImplementations[platform]())\n                        : (jsImplementation = jsImplementations[platform]);\n            }\n            else if (capCustomPlatform !== null && !jsImplementation && 'web' in jsImplementations) {\n                jsImplementation =\n                    typeof jsImplementations['web'] === 'function'\n                        ? (jsImplementation = await jsImplementations['web']())\n                        : (jsImplementation = jsImplementations['web']);\n            }\n            return jsImplementation;\n        };\n        const createPluginMethod = (impl, prop) => {\n            var _a, _b;\n            if (pluginHeader) {\n                const methodHeader = pluginHeader === null || pluginHeader === void 0 ? void 0 : pluginHeader.methods.find((m) => prop === m.name);\n                if (methodHeader) {\n                    if (methodHeader.rtype === 'promise') {\n                        return (options) => cap.nativePromise(pluginName, prop.toString(), options);\n                    }\n                    else {\n                        return (options, callback) => cap.nativeCallback(pluginName, prop.toString(), options, callback);\n                    }\n                }\n                else if (impl) {\n                    return (_a = impl[prop]) === null || _a === void 0 ? void 0 : _a.bind(impl);\n                }\n            }\n            else if (impl) {\n                return (_b = impl[prop]) === null || _b === void 0 ? void 0 : _b.bind(impl);\n            }\n            else {\n                throw new CapacitorException(`\"${pluginName}\" plugin is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n            }\n        };\n        const createPluginMethodWrapper = (prop) => {\n            let remove;\n            const wrapper = (...args) => {\n                const p = loadPluginImplementation().then((impl) => {\n                    const fn = createPluginMethod(impl, prop);\n                    if (fn) {\n                        const p = fn(...args);\n                        remove = p === null || p === void 0 ? void 0 : p.remove;\n                        return p;\n                    }\n                    else {\n                        throw new CapacitorException(`\"${pluginName}.${prop}()\" is not implemented on ${platform}`, ExceptionCode.Unimplemented);\n                    }\n                });\n                if (prop === 'addListener') {\n                    p.remove = async () => remove();\n                }\n                return p;\n            };\n            // Some flair ✨\n            wrapper.toString = () => `${prop.toString()}() { [capacitor code] }`;\n            Object.defineProperty(wrapper, 'name', {\n                value: prop,\n                writable: false,\n                configurable: false,\n            });\n            return wrapper;\n        };\n        const addListener = createPluginMethodWrapper('addListener');\n        const removeListener = createPluginMethodWrapper('removeListener');\n        const addListenerNative = (eventName, callback) => {\n            const call = addListener({ eventName }, callback);\n            const remove = async () => {\n                const callbackId = await call;\n                removeListener({\n                    eventName,\n                    callbackId,\n                }, callback);\n            };\n            const p = new Promise((resolve) => call.then(() => resolve({ remove })));\n            p.remove = async () => {\n                console.warn(`Using addListener() without 'await' is deprecated.`);\n                await remove();\n            };\n            return p;\n        };\n        const proxy = new Proxy({}, {\n            get(_, prop) {\n                switch (prop) {\n                    // https://github.com/facebook/react/issues/20030\n                    case '$$typeof':\n                        return undefined;\n                    case 'toJSON':\n                        return () => ({});\n                    case 'addListener':\n                        return pluginHeader ? addListenerNative : addListener;\n                    case 'removeListener':\n                        return removeListener;\n                    default:\n                        return createPluginMethodWrapper(prop);\n                }\n            },\n        });\n        Plugins[pluginName] = proxy;\n        registeredPlugins.set(pluginName, {\n            name: pluginName,\n            proxy,\n            platforms: new Set([...Object.keys(jsImplementations), ...(pluginHeader ? [platform] : [])]),\n        });\n        return proxy;\n    };\n    // Add in convertFileSrc for web, it will already be available in native context\n    if (!cap.convertFileSrc) {\n        cap.convertFileSrc = (filePath) => filePath;\n    }\n    cap.getPlatform = getPlatform;\n    cap.handleError = handleError;\n    cap.isNativePlatform = isNativePlatform;\n    cap.isPluginAvailable = isPluginAvailable;\n    cap.registerPlugin = registerPlugin;\n    cap.Exception = CapacitorException;\n    cap.DEBUG = !!cap.DEBUG;\n    cap.isLoggingEnabled = !!cap.isLoggingEnabled;\n    return cap;\n};\nexport const initCapacitorGlobal = (win) => (win.Capacitor = createCapacitor(win));\n//# sourceMappingURL=runtime.js.map", "import { initCapacitorGlobal } from './runtime';\nexport const Capacitor = /*#__PURE__*/ initCapacitorGlobal(typeof globalThis !== 'undefined'\n    ? globalThis\n    : typeof self !== 'undefined'\n        ? self\n        : typeof window !== 'undefined'\n            ? window\n            : typeof global !== 'undefined'\n                ? global\n                : {});\nexport const registerPlugin = Capacitor.registerPlugin;\n//# sourceMappingURL=global.js.map", "import { Capacitor } from './global';\nimport { ExceptionCode } from './util';\n/**\n * Base class web plugins should extend.\n */\nexport class WebPlugin {\n    constructor() {\n        this.listeners = {};\n        this.retainedEventArguments = {};\n        this.windowListeners = {};\n    }\n    addListener(eventName, listenerFunc) {\n        let firstListener = false;\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            this.listeners[eventName] = [];\n            firstListener = true;\n        }\n        this.listeners[eventName].push(listenerFunc);\n        // If we haven't added a window listener for this event and it requires one,\n        // go ahead and add it\n        const windowListener = this.windowListeners[eventName];\n        if (windowListener && !windowListener.registered) {\n            this.addWindowListener(windowListener);\n        }\n        if (firstListener) {\n            this.sendRetainedArgumentsForEvent(eventName);\n        }\n        const remove = async () => this.removeListener(eventName, listenerFunc);\n        const p = Promise.resolve({ remove });\n        return p;\n    }\n    async removeAllListeners() {\n        this.listeners = {};\n        for (const listener in this.windowListeners) {\n            this.removeWindowListener(this.windowListeners[listener]);\n        }\n        this.windowListeners = {};\n    }\n    notifyListeners(eventName, data, retainUntilConsumed) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            if (retainUntilConsumed) {\n                let args = this.retainedEventArguments[eventName];\n                if (!args) {\n                    args = [];\n                }\n                args.push(data);\n                this.retainedEventArguments[eventName] = args;\n            }\n            return;\n        }\n        listeners.forEach((listener) => listener(data));\n    }\n    hasListeners(eventName) {\n        var _a;\n        return !!((_a = this.listeners[eventName]) === null || _a === void 0 ? void 0 : _a.length);\n    }\n    registerWindowListener(windowEventName, pluginEventName) {\n        this.windowListeners[pluginEventName] = {\n            registered: false,\n            windowEventName,\n            pluginEventName,\n            handler: (event) => {\n                this.notifyListeners(pluginEventName, event);\n            },\n        };\n    }\n    unimplemented(msg = 'not implemented') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unimplemented);\n    }\n    unavailable(msg = 'not available') {\n        return new Capacitor.Exception(msg, ExceptionCode.Unavailable);\n    }\n    async removeListener(eventName, listenerFunc) {\n        const listeners = this.listeners[eventName];\n        if (!listeners) {\n            return;\n        }\n        const index = listeners.indexOf(listenerFunc);\n        this.listeners[eventName].splice(index, 1);\n        // If there are no more listeners for this type of event,\n        // remove the window listener\n        if (!this.listeners[eventName].length) {\n            this.removeWindowListener(this.windowListeners[eventName]);\n        }\n    }\n    addWindowListener(handle) {\n        window.addEventListener(handle.windowEventName, handle.handler);\n        handle.registered = true;\n    }\n    removeWindowListener(handle) {\n        if (!handle) {\n            return;\n        }\n        window.removeEventListener(handle.windowEventName, handle.handler);\n        handle.registered = false;\n    }\n    sendRetainedArgumentsForEvent(eventName) {\n        const args = this.retainedEventArguments[eventName];\n        if (!args) {\n            return;\n        }\n        delete this.retainedEventArguments[eventName];\n        args.forEach((arg) => {\n            this.notifyListeners(eventName, arg);\n        });\n    }\n}\n//# sourceMappingURL=web-plugin.js.map", "import { registerPlugin } from './global';\nimport { WebPlugin } from './web-plugin';\nexport const WebView = /*#__PURE__*/ registerPlugin('WebView');\n/******** END WEB VIEW PLUGIN ********/\n/******** COOKIES PLUGIN ********/\n/**\n * Safely web encode a string value (inspired by js-cookie)\n * @param str The string value to encode\n */\nconst encode = (str) => encodeURIComponent(str)\n    .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n    .replace(/[()]/g, escape);\n/**\n * Safely web decode a string value (inspired by js-cookie)\n * @param str The string value to decode\n */\nconst decode = (str) => str.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\nexport class CapacitorCookiesPluginWeb extends WebPlugin {\n    async getCookies() {\n        const cookies = document.cookie;\n        const cookieMap = {};\n        cookies.split(';').forEach((cookie) => {\n            if (cookie.length <= 0)\n                return;\n            // Replace first \"=\" with CAP_COOKIE to prevent splitting on additional \"=\"\n            let [key, value] = cookie.replace(/=/, 'CAP_COOKIE').split('CAP_COOKIE');\n            key = decode(key).trim();\n            value = decode(value).trim();\n            cookieMap[key] = value;\n        });\n        return cookieMap;\n    }\n    async setCookie(options) {\n        try {\n            // Safely Encoded Key/Value\n            const encodedKey = encode(options.key);\n            const encodedValue = encode(options.value);\n            // Clean & sanitize options\n            const expires = `; expires=${(options.expires || '').replace('expires=', '')}`; // Default is \"; expires=\"\n            const path = (options.path || '/').replace('path=', ''); // Default is \"path=/\"\n            const domain = options.url != null && options.url.length > 0 ? `domain=${options.url}` : '';\n            document.cookie = `${encodedKey}=${encodedValue || ''}${expires}; path=${path}; ${domain};`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async deleteCookie(options) {\n        try {\n            document.cookie = `${options.key}=; Max-Age=0`;\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearCookies() {\n        try {\n            const cookies = document.cookie.split(';') || [];\n            for (const cookie of cookies) {\n                document.cookie = cookie.replace(/^ +/, '').replace(/=.*/, `=;expires=${new Date().toUTCString()};path=/`);\n            }\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n    async clearAllCookies() {\n        try {\n            await this.clearCookies();\n        }\n        catch (error) {\n            return Promise.reject(error);\n        }\n    }\n}\nexport const CapacitorCookies = registerPlugin('CapacitorCookies', {\n    web: () => new CapacitorCookiesPluginWeb(),\n});\n// UTILITY FUNCTIONS\n/**\n * Read in a Blob value and return it as a base64 string\n * @param blob The blob value to convert to a base64 string\n */\nexport const readBlobAsBase64 = async (blob) => new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n        const base64String = reader.result;\n        // remove prefix \"data:application/pdf;base64,\"\n        resolve(base64String.indexOf(',') >= 0 ? base64String.split(',')[1] : base64String);\n    };\n    reader.onerror = (error) => reject(error);\n    reader.readAsDataURL(blob);\n});\n/**\n * Normalize an HttpHeaders map by lowercasing all of the values\n * @param headers The HttpHeaders object to normalize\n */\nconst normalizeHttpHeaders = (headers = {}) => {\n    const originalKeys = Object.keys(headers);\n    const loweredKeys = Object.keys(headers).map((k) => k.toLocaleLowerCase());\n    const normalized = loweredKeys.reduce((acc, key, index) => {\n        acc[key] = headers[originalKeys[index]];\n        return acc;\n    }, {});\n    return normalized;\n};\n/**\n * Builds a string of url parameters that\n * @param params A map of url parameters\n * @param shouldEncode true if you should encodeURIComponent() the values (true by default)\n */\nconst buildUrlParams = (params, shouldEncode = true) => {\n    if (!params)\n        return null;\n    const output = Object.entries(params).reduce((accumulator, entry) => {\n        const [key, value] = entry;\n        let encodedValue;\n        let item;\n        if (Array.isArray(value)) {\n            item = '';\n            value.forEach((str) => {\n                encodedValue = shouldEncode ? encodeURIComponent(str) : str;\n                item += `${key}=${encodedValue}&`;\n            });\n            // last character will always be \"&\" so slice it off\n            item.slice(0, -1);\n        }\n        else {\n            encodedValue = shouldEncode ? encodeURIComponent(value) : value;\n            item = `${key}=${encodedValue}`;\n        }\n        return `${accumulator}&${item}`;\n    }, '');\n    // Remove initial \"&\" from the reduce\n    return output.substr(1);\n};\n/**\n * Build the RequestInit object based on the options passed into the initial request\n * @param options The Http plugin options\n * @param extra Any extra RequestInit values\n */\nexport const buildRequestInit = (options, extra = {}) => {\n    const output = Object.assign({ method: options.method || 'GET', headers: options.headers }, extra);\n    // Get the content-type\n    const headers = normalizeHttpHeaders(options.headers);\n    const type = headers['content-type'] || '';\n    // If body is already a string, then pass it through as-is.\n    if (typeof options.data === 'string') {\n        output.body = options.data;\n    }\n    // Build request initializers based off of content-type\n    else if (type.includes('application/x-www-form-urlencoded')) {\n        const params = new URLSearchParams();\n        for (const [key, value] of Object.entries(options.data || {})) {\n            params.set(key, value);\n        }\n        output.body = params.toString();\n    }\n    else if (type.includes('multipart/form-data') || options.data instanceof FormData) {\n        const form = new FormData();\n        if (options.data instanceof FormData) {\n            options.data.forEach((value, key) => {\n                form.append(key, value);\n            });\n        }\n        else {\n            for (const key of Object.keys(options.data)) {\n                form.append(key, options.data[key]);\n            }\n        }\n        output.body = form;\n        const headers = new Headers(output.headers);\n        headers.delete('content-type'); // content-type will be set by `window.fetch` to includy boundary\n        output.headers = headers;\n    }\n    else if (type.includes('application/json') || typeof options.data === 'object') {\n        output.body = JSON.stringify(options.data);\n    }\n    return output;\n};\n// WEB IMPLEMENTATION\nexport class CapacitorHttpPluginWeb extends WebPlugin {\n    /**\n     * Perform an Http request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async request(options) {\n        const requestInit = buildRequestInit(options, options.webFetchExtra);\n        const urlParams = buildUrlParams(options.params, options.shouldEncodeUrlParams);\n        const url = urlParams ? `${options.url}?${urlParams}` : options.url;\n        const response = await fetch(url, requestInit);\n        const contentType = response.headers.get('content-type') || '';\n        // Default to 'text' responseType so no parsing happens\n        let { responseType = 'text' } = response.ok ? options : {};\n        // If the response content-type is json, force the response to be json\n        if (contentType.includes('application/json')) {\n            responseType = 'json';\n        }\n        let data;\n        let blob;\n        switch (responseType) {\n            case 'arraybuffer':\n            case 'blob':\n                blob = await response.blob();\n                data = await readBlobAsBase64(blob);\n                break;\n            case 'json':\n                data = await response.json();\n                break;\n            case 'document':\n            case 'text':\n            default:\n                data = await response.text();\n        }\n        // Convert fetch headers to Capacitor HttpHeaders\n        const headers = {};\n        response.headers.forEach((value, key) => {\n            headers[key] = value;\n        });\n        return {\n            data,\n            headers,\n            status: response.status,\n            url: response.url,\n        };\n    }\n    /**\n     * Perform an Http GET request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async get(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'GET' }));\n    }\n    /**\n     * Perform an Http POST request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async post(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'POST' }));\n    }\n    /**\n     * Perform an Http PUT request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async put(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PUT' }));\n    }\n    /**\n     * Perform an Http PATCH request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async patch(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'PATCH' }));\n    }\n    /**\n     * Perform an Http DELETE request given a set of options\n     * @param options Options to build the HTTP request\n     */\n    async delete(options) {\n        return this.request(Object.assign(Object.assign({}, options), { method: 'DELETE' }));\n    }\n}\nexport const CapacitorHttp = registerPlugin('CapacitorHttp', {\n    web: () => new CapacitorHttpPluginWeb(),\n});\n/******** END HTTP PLUGIN ********/\n//# sourceMappingURL=core-plugins.js.map"], "mappings": ";AAAU,IAAC;CACV,SAAUA,gBAAe;AAOtB,EAAAA,eAAc,eAAe,IAAI;AAQjC,EAAAA,eAAc,aAAa,IAAI;AACnC,GAAG,kBAAkB,gBAAgB,CAAA,EAAG;AACjC,IAAM,qBAAN,cAAiC,MAAM;EAC1C,YAAY,SAAS,MAAM,MAAM;AAC7B,UAAM,OAAO;AACb,SAAK,UAAU;AACf,SAAK,OAAO;AACZ,SAAK,OAAO;EACpB;AACA;AACO,IAAM,gBAAgB,CAAC,QAAQ;AAClC,MAAI,IAAI;AACR,MAAI,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,eAAe;AAC7D,WAAO;EACf,YACc,MAAM,KAAK,QAAQ,QAAQ,QAAQ,SAAS,SAAS,IAAI,YAAY,QAAQ,OAAO,SAAS,SAAS,GAAG,qBAAqB,QAAQ,OAAO,SAAS,SAAS,GAAG,QAAQ;AAChL,WAAO;EACf,OACS;AACD,WAAO;EACf;AACA;ACpCO,IAAM,kBAAkB,CAAC,QAAQ;AACpC,QAAM,oBAAoB,IAAI,2BAA2B;AACzD,QAAM,MAAM,IAAI,aAAa,CAAA;AAC7B,QAAM,UAAW,IAAI,UAAU,IAAI,WAAW,CAAA;AAC9C,QAAM,cAAc,MAAM;AACtB,WAAO,sBAAsB,OAAO,kBAAkB,OAAO,cAAc,GAAG;EACtF;AACI,QAAM,mBAAmB,MAAM,YAAW,MAAO;AACjD,QAAM,oBAAoB,CAAC,eAAe;AACtC,UAAM,SAAS,kBAAkB,IAAI,UAAU;AAC/C,QAAI,WAAW,QAAQ,WAAW,SAAS,SAAS,OAAO,UAAU,IAAI,YAAW,CAAE,GAAG;AAErF,aAAO;IACnB;AACQ,QAAI,gBAAgB,UAAU,GAAG;AAE7B,aAAO;IACnB;AACQ,WAAO;EACf;AACI,QAAM,kBAAkB,CAAC,eAAe;AAAE,QAAI;AAAI,YAAQ,KAAK,IAAI,mBAAmB,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,CAAC,MAAM,EAAE,SAAS,UAAU;EAAE;AAC7J,QAAM,cAAc,CAAC,QAAQ,IAAI,QAAQ,MAAM,GAAG;AAClD,QAAM,oBAAoB,oBAAI,IAAG;AACjC,QAAMC,kBAAiB,CAAC,YAAY,oBAAoB,CAAA,MAAO;AAC3D,UAAM,mBAAmB,kBAAkB,IAAI,UAAU;AACzD,QAAI,kBAAkB;AAClB,cAAQ,KAAK,qBAAqB,UAAU,sDAAsD;AAClG,aAAO,iBAAiB;IACpC;AACQ,UAAM,WAAW,YAAW;AAC5B,UAAM,eAAe,gBAAgB,UAAU;AAC/C,QAAI;AACJ,UAAM,2BAA2B,YAAY;AACzC,UAAI,CAAC,oBAAoB,YAAY,mBAAmB;AACpD,2BACI,OAAO,kBAAkB,QAAQ,MAAM,aAChC,mBAAmB,MAAM,kBAAkB,QAAQ,EAAC,IACpD,mBAAmB,kBAAkB,QAAQ;MACxE,WACqB,sBAAsB,QAAQ,CAAC,oBAAoB,SAAS,mBAAmB;AACpF,2BACI,OAAO,kBAAkB,KAAK,MAAM,aAC7B,mBAAmB,MAAM,kBAAkB,KAAK,EAAC,IACjD,mBAAmB,kBAAkB,KAAK;MACrE;AACY,aAAO;IACnB;AACQ,UAAM,qBAAqB,CAAC,MAAM,SAAS;AACvC,UAAI,IAAI;AACR,UAAI,cAAc;AACd,cAAM,eAAe,iBAAiB,QAAQ,iBAAiB,SAAS,SAAS,aAAa,QAAQ,KAAK,CAAC,MAAM,SAAS,EAAE,IAAI;AACjI,YAAI,cAAc;AACd,cAAI,aAAa,UAAU,WAAW;AAClC,mBAAO,CAAC,YAAY,IAAI,cAAc,YAAY,KAAK,SAAQ,GAAI,OAAO;UAClG,OACyB;AACD,mBAAO,CAAC,SAAS,aAAa,IAAI,eAAe,YAAY,KAAK,SAAQ,GAAI,SAAS,QAAQ;UACvH;QACA,WACyB,MAAM;AACX,kBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;QAC9F;MACA,WACqB,MAAM;AACX,gBAAQ,KAAK,KAAK,IAAI,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,KAAK,IAAI;MAC1F,OACiB;AACD,cAAM,IAAI,mBAAmB,IAAI,UAAU,kCAAkC,QAAQ,IAAI,cAAc,aAAa;MACpI;IACA;AACQ,UAAM,4BAA4B,CAAC,SAAS;AACxC,UAAI;AACJ,YAAM,UAAU,IAAI,SAAS;AACzB,cAAM,IAAI,yBAAwB,EAAG,KAAK,CAAC,SAAS;AAChD,gBAAM,KAAK,mBAAmB,MAAM,IAAI;AACxC,cAAI,IAAI;AACJ,kBAAMC,KAAI,GAAG,GAAG,IAAI;AACpB,qBAASA,OAAM,QAAQA,OAAM,SAAS,SAASA,GAAE;AACjD,mBAAOA;UAC/B,OACyB;AACD,kBAAM,IAAI,mBAAmB,IAAI,UAAU,IAAI,IAAI,6BAA6B,QAAQ,IAAI,cAAc,aAAa;UAC/I;QACA,CAAiB;AACD,YAAI,SAAS,eAAe;AACxB,YAAE,SAAS,YAAY,OAAM;QACjD;AACgB,eAAO;MACvB;AAEY,cAAQ,WAAW,MAAM,GAAG,KAAK,SAAQ,CAAE;AAC3C,aAAO,eAAe,SAAS,QAAQ;QACnC,OAAO;QACP,UAAU;QACV,cAAc;MAC9B,CAAa;AACD,aAAO;IACnB;AACQ,UAAM,cAAc,0BAA0B,aAAa;AAC3D,UAAM,iBAAiB,0BAA0B,gBAAgB;AACjE,UAAM,oBAAoB,CAAC,WAAW,aAAa;AAC/C,YAAM,OAAO,YAAY,EAAE,UAAS,GAAI,QAAQ;AAChD,YAAM,SAAS,YAAY;AACvB,cAAM,aAAa,MAAM;AACzB,uBAAe;UACX;UACA;QACpB,GAAmB,QAAQ;MAC3B;AACY,YAAM,IAAI,IAAI,QAAQ,CAAC,YAAY,KAAK,KAAK,MAAM,QAAQ,EAAE,OAAM,CAAE,CAAC,CAAC;AACvE,QAAE,SAAS,YAAY;AACnB,gBAAQ,KAAK,oDAAoD;AACjE,cAAM,OAAM;MAC5B;AACY,aAAO;IACnB;AACQ,UAAM,QAAQ,IAAI,MAAM,CAAA,GAAI;MACxB,IAAI,GAAG,MAAM;AACT,gBAAQ,MAAI;UAER,KAAK;AACD,mBAAO;UACX,KAAK;AACD,mBAAO,OAAO,CAAA;UAClB,KAAK;AACD,mBAAO,eAAe,oBAAoB;UAC9C,KAAK;AACD,mBAAO;UACX;AACI,mBAAO,0BAA0B,IAAI;QAC7D;MACA;IACA,CAAS;AACD,YAAQ,UAAU,IAAI;AACtB,sBAAkB,IAAI,YAAY;MAC9B,MAAM;MACN;MACA,WAAW,oBAAI,IAAI,CAAC,GAAG,OAAO,KAAK,iBAAiB,GAAG,GAAI,eAAe,CAAC,QAAQ,IAAI,CAAA,CAAG,CAAC;IACvG,CAAS;AACD,WAAO;EACf;AAEI,MAAI,CAAC,IAAI,gBAAgB;AACrB,QAAI,iBAAiB,CAAC,aAAa;EAC3C;AACI,MAAI,cAAc;AAClB,MAAI,cAAc;AAClB,MAAI,mBAAmB;AACvB,MAAI,oBAAoB;AACxB,MAAI,iBAAiBD;AACrB,MAAI,YAAY;AAChB,MAAI,QAAQ,CAAC,CAAC,IAAI;AAClB,MAAI,mBAAmB,CAAC,CAAC,IAAI;AAC7B,SAAO;AACX;AACO,IAAM,sBAAsB,CAAC,QAAS,IAAI,YAAY,gBAAgB,GAAG;AC3JpE,IAAC,YAA0B,oBAAoB,OAAO,eAAe,cAC3E,aACA,OAAO,SAAS,cACZ,OACA,OAAO,WAAW,cACd,SACA,OAAO,WAAW,cACd,SACA,CAAA,CAAE;AACR,IAAC,iBAAiB,UAAU;ACLjC,IAAM,YAAN,MAAgB;EACnB,cAAc;AACV,SAAK,YAAY,CAAA;AACjB,SAAK,yBAAyB,CAAA;AAC9B,SAAK,kBAAkB,CAAA;EAC/B;EACI,YAAY,WAAW,cAAc;AACjC,QAAI,gBAAgB;AACpB,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACZ,WAAK,UAAU,SAAS,IAAI,CAAA;AAC5B,sBAAgB;IAC5B;AACQ,SAAK,UAAU,SAAS,EAAE,KAAK,YAAY;AAG3C,UAAM,iBAAiB,KAAK,gBAAgB,SAAS;AACrD,QAAI,kBAAkB,CAAC,eAAe,YAAY;AAC9C,WAAK,kBAAkB,cAAc;IACjD;AACQ,QAAI,eAAe;AACf,WAAK,8BAA8B,SAAS;IACxD;AACQ,UAAM,SAAS,YAAY,KAAK,eAAe,WAAW,YAAY;AACtE,UAAM,IAAI,QAAQ,QAAQ,EAAE,OAAM,CAAE;AACpC,WAAO;EACf;EACI,MAAM,qBAAqB;AACvB,SAAK,YAAY,CAAA;AACjB,eAAW,YAAY,KAAK,iBAAiB;AACzC,WAAK,qBAAqB,KAAK,gBAAgB,QAAQ,CAAC;IACpE;AACQ,SAAK,kBAAkB,CAAA;EAC/B;EACI,gBAAgB,WAAW,MAAM,qBAAqB;AAClD,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACZ,UAAI,qBAAqB;AACrB,YAAI,OAAO,KAAK,uBAAuB,SAAS;AAChD,YAAI,CAAC,MAAM;AACP,iBAAO,CAAA;QAC3B;AACgB,aAAK,KAAK,IAAI;AACd,aAAK,uBAAuB,SAAS,IAAI;MACzD;AACY;IACZ;AACQ,cAAU,QAAQ,CAAC,aAAa,SAAS,IAAI,CAAC;EACtD;EACI,aAAa,WAAW;AACpB,QAAI;AACJ,WAAO,CAAC,GAAG,KAAK,KAAK,UAAU,SAAS,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG;EAC3F;EACI,uBAAuB,iBAAiB,iBAAiB;AACrD,SAAK,gBAAgB,eAAe,IAAI;MACpC,YAAY;MACZ;MACA;MACA,SAAS,CAAC,UAAU;AAChB,aAAK,gBAAgB,iBAAiB,KAAK;MAC3D;IACA;EACA;EACI,cAAc,MAAM,mBAAmB;AACnC,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,aAAa;EACvE;EACI,YAAY,MAAM,iBAAiB;AAC/B,WAAO,IAAI,UAAU,UAAU,KAAK,cAAc,WAAW;EACrE;EACI,MAAM,eAAe,WAAW,cAAc;AAC1C,UAAM,YAAY,KAAK,UAAU,SAAS;AAC1C,QAAI,CAAC,WAAW;AACZ;IACZ;AACQ,UAAM,QAAQ,UAAU,QAAQ,YAAY;AAC5C,SAAK,UAAU,SAAS,EAAE,OAAO,OAAO,CAAC;AAGzC,QAAI,CAAC,KAAK,UAAU,SAAS,EAAE,QAAQ;AACnC,WAAK,qBAAqB,KAAK,gBAAgB,SAAS,CAAC;IACrE;EACA;EACI,kBAAkB,QAAQ;AACtB,WAAO,iBAAiB,OAAO,iBAAiB,OAAO,OAAO;AAC9D,WAAO,aAAa;EAC5B;EACI,qBAAqB,QAAQ;AACzB,QAAI,CAAC,QAAQ;AACT;IACZ;AACQ,WAAO,oBAAoB,OAAO,iBAAiB,OAAO,OAAO;AACjE,WAAO,aAAa;EAC5B;EACI,8BAA8B,WAAW;AACrC,UAAM,OAAO,KAAK,uBAAuB,SAAS;AAClD,QAAI,CAAC,MAAM;AACP;IACZ;AACQ,WAAO,KAAK,uBAAuB,SAAS;AAC5C,SAAK,QAAQ,CAAC,QAAQ;AAClB,WAAK,gBAAgB,WAAW,GAAG;IAC/C,CAAS;EACT;AACA;AC1GY,IAAC,UAAwB,eAAe,SAAS;AAO7D,IAAM,SAAS,CAAC,QAAQ,mBAAmB,GAAG,EACzC,QAAQ,wBAAwB,kBAAkB,EAClD,QAAQ,SAAS,MAAM;AAK5B,IAAM,SAAS,CAAC,QAAQ,IAAI,QAAQ,oBAAoB,kBAAkB;AACnE,IAAM,4BAAN,cAAwC,UAAU;EACrD,MAAM,aAAa;AACf,UAAM,UAAU,SAAS;AACzB,UAAM,YAAY,CAAA;AAClB,YAAQ,MAAM,GAAG,EAAE,QAAQ,CAAC,WAAW;AACnC,UAAI,OAAO,UAAU;AACjB;AAEJ,UAAI,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,KAAK,YAAY,EAAE,MAAM,YAAY;AACvE,YAAM,OAAO,GAAG,EAAE,KAAI;AACtB,cAAQ,OAAO,KAAK,EAAE,KAAI;AAC1B,gBAAU,GAAG,IAAI;IAC7B,CAAS;AACD,WAAO;EACf;EACI,MAAM,UAAU,SAAS;AACrB,QAAI;AAEA,YAAM,aAAa,OAAO,QAAQ,GAAG;AACrC,YAAM,eAAe,OAAO,QAAQ,KAAK;AAEzC,YAAM,UAAU,cAAc,QAAQ,WAAW,IAAI,QAAQ,YAAY,EAAE,CAAC;AAC5E,YAAM,QAAQ,QAAQ,QAAQ,KAAK,QAAQ,SAAS,EAAE;AACtD,YAAM,SAAS,QAAQ,OAAO,QAAQ,QAAQ,IAAI,SAAS,IAAI,UAAU,QAAQ,GAAG,KAAK;AACzF,eAAS,SAAS,GAAG,UAAU,IAAI,gBAAgB,EAAE,GAAG,OAAO,UAAU,IAAI,KAAK,MAAM;IACpG,SACe,OAAO;AACV,aAAO,QAAQ,OAAO,KAAK;IACvC;EACA;EACI,MAAM,aAAa,SAAS;AACxB,QAAI;AACA,eAAS,SAAS,GAAG,QAAQ,GAAG;IAC5C,SACe,OAAO;AACV,aAAO,QAAQ,OAAO,KAAK;IACvC;EACA;EACI,MAAM,eAAe;AACjB,QAAI;AACA,YAAM,UAAU,SAAS,OAAO,MAAM,GAAG,KAAK,CAAA;AAC9C,iBAAW,UAAU,SAAS;AAC1B,iBAAS,SAAS,OAAO,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,cAAa,oBAAI,KAAI,GAAG,YAAW,CAAE,SAAS;MACzH;IACA,SACe,OAAO;AACV,aAAO,QAAQ,OAAO,KAAK;IACvC;EACA;EACI,MAAM,kBAAkB;AACpB,QAAI;AACA,YAAM,KAAK,aAAY;IACnC,SACe,OAAO;AACV,aAAO,QAAQ,OAAO,KAAK;IACvC;EACA;AACA;AACY,IAAC,mBAAmB,eAAe,oBAAoB;EAC/D,KAAK,MAAM,IAAI,0BAAyB;AAC5C,CAAC;AAMM,IAAM,mBAAmB,OAAO,SAAS,IAAI,QAAQ,CAAC,SAAS,WAAW;AAC7E,QAAM,SAAS,IAAI,WAAU;AAC7B,SAAO,SAAS,MAAM;AAClB,UAAM,eAAe,OAAO;AAE5B,YAAQ,aAAa,QAAQ,GAAG,KAAK,IAAI,aAAa,MAAM,GAAG,EAAE,CAAC,IAAI,YAAY;EAC1F;AACI,SAAO,UAAU,CAAC,UAAU,OAAO,KAAK;AACxC,SAAO,cAAc,IAAI;AAC7B,CAAC;AAKD,IAAM,uBAAuB,CAAC,UAAU,CAAA,MAAO;AAC3C,QAAM,eAAe,OAAO,KAAK,OAAO;AACxC,QAAM,cAAc,OAAO,KAAK,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,kBAAiB,CAAE;AACzE,QAAM,aAAa,YAAY,OAAO,CAAC,KAAK,KAAK,UAAU;AACvD,QAAI,GAAG,IAAI,QAAQ,aAAa,KAAK,CAAC;AACtC,WAAO;EACf,GAAO,CAAA,CAAE;AACL,SAAO;AACX;AAMA,IAAM,iBAAiB,CAAC,QAAQ,eAAe,SAAS;AACpD,MAAI,CAAC;AACD,WAAO;AACX,QAAM,SAAS,OAAO,QAAQ,MAAM,EAAE,OAAO,CAAC,aAAa,UAAU;AACjE,UAAM,CAAC,KAAK,KAAK,IAAI;AACrB,QAAI;AACJ,QAAI;AACJ,QAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,aAAO;AACP,YAAM,QAAQ,CAAC,QAAQ;AACnB,uBAAe,eAAe,mBAAmB,GAAG,IAAI;AACxD,gBAAQ,GAAG,GAAG,IAAI,YAAY;MAC9C,CAAa;AAED,WAAK,MAAM,GAAG,EAAE;IAC5B,OACa;AACD,qBAAe,eAAe,mBAAmB,KAAK,IAAI;AAC1D,aAAO,GAAG,GAAG,IAAI,YAAY;IACzC;AACQ,WAAO,GAAG,WAAW,IAAI,IAAI;EACrC,GAAO,EAAE;AAEL,SAAO,OAAO,OAAO,CAAC;AAC1B;AAMY,IAAC,mBAAmB,CAAC,SAAS,QAAQ,CAAA,MAAO;AACrD,QAAM,SAAS,OAAO,OAAO,EAAE,QAAQ,QAAQ,UAAU,OAAO,SAAS,QAAQ,QAAO,GAAI,KAAK;AAEjG,QAAM,UAAU,qBAAqB,QAAQ,OAAO;AACpD,QAAM,OAAO,QAAQ,cAAc,KAAK;AAExC,MAAI,OAAO,QAAQ,SAAS,UAAU;AAClC,WAAO,OAAO,QAAQ;EAC9B,WAEa,KAAK,SAAS,mCAAmC,GAAG;AACzD,UAAM,SAAS,IAAI,gBAAe;AAClC,eAAW,CAAC,KAAK,KAAK,KAAK,OAAO,QAAQ,QAAQ,QAAQ,CAAA,CAAE,GAAG;AAC3D,aAAO,IAAI,KAAK,KAAK;IACjC;AACQ,WAAO,OAAO,OAAO,SAAQ;EACrC,WACa,KAAK,SAAS,qBAAqB,KAAK,QAAQ,gBAAgB,UAAU;AAC/E,UAAM,OAAO,IAAI,SAAQ;AACzB,QAAI,QAAQ,gBAAgB,UAAU;AAClC,cAAQ,KAAK,QAAQ,CAAC,OAAO,QAAQ;AACjC,aAAK,OAAO,KAAK,KAAK;MACtC,CAAa;IACb,OACa;AACD,iBAAW,OAAO,OAAO,KAAK,QAAQ,IAAI,GAAG;AACzC,aAAK,OAAO,KAAK,QAAQ,KAAK,GAAG,CAAC;MAClD;IACA;AACQ,WAAO,OAAO;AACd,UAAME,WAAU,IAAI,QAAQ,OAAO,OAAO;AAC1C,IAAAA,SAAQ,OAAO,cAAc;AAC7B,WAAO,UAAUA;EACzB,WACa,KAAK,SAAS,kBAAkB,KAAK,OAAO,QAAQ,SAAS,UAAU;AAC5E,WAAO,OAAO,KAAK,UAAU,QAAQ,IAAI;EACjD;AACI,SAAO;AACX;AAEO,IAAM,yBAAN,cAAqC,UAAU;;;;;EAKlD,MAAM,QAAQ,SAAS;AACnB,UAAM,cAAc,iBAAiB,SAAS,QAAQ,aAAa;AACnE,UAAM,YAAY,eAAe,QAAQ,QAAQ,QAAQ,qBAAqB;AAC9E,UAAM,MAAM,YAAY,GAAG,QAAQ,GAAG,IAAI,SAAS,KAAK,QAAQ;AAChE,UAAM,WAAW,MAAM,MAAM,KAAK,WAAW;AAC7C,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAc,KAAK;AAE5D,QAAI,EAAE,eAAe,OAAM,IAAK,SAAS,KAAK,UAAU,CAAA;AAExD,QAAI,YAAY,SAAS,kBAAkB,GAAG;AAC1C,qBAAe;IAC3B;AACQ,QAAI;AACJ,QAAI;AACJ,YAAQ,cAAY;MAChB,KAAK;MACL,KAAK;AACD,eAAO,MAAM,SAAS,KAAI;AAC1B,eAAO,MAAM,iBAAiB,IAAI;AAClC;MACJ,KAAK;AACD,eAAO,MAAM,SAAS,KAAI;AAC1B;MACJ,KAAK;MACL,KAAK;MACL;AACI,eAAO,MAAM,SAAS,KAAI;IAC1C;AAEQ,UAAM,UAAU,CAAA;AAChB,aAAS,QAAQ,QAAQ,CAAC,OAAO,QAAQ;AACrC,cAAQ,GAAG,IAAI;IAC3B,CAAS;AACD,WAAO;MACH;MACA;MACA,QAAQ,SAAS;MACjB,KAAK,SAAS;IAC1B;EACA;;;;;EAKI,MAAM,IAAI,SAAS;AACf,WAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAA,GAAI,OAAO,GAAG,EAAE,QAAQ,MAAK,CAAE,CAAC;EACxF;;;;;EAKI,MAAM,KAAK,SAAS;AAChB,WAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAA,GAAI,OAAO,GAAG,EAAE,QAAQ,OAAM,CAAE,CAAC;EACzF;;;;;EAKI,MAAM,IAAI,SAAS;AACf,WAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAA,GAAI,OAAO,GAAG,EAAE,QAAQ,MAAK,CAAE,CAAC;EACxF;;;;;EAKI,MAAM,MAAM,SAAS;AACjB,WAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAA,GAAI,OAAO,GAAG,EAAE,QAAQ,QAAO,CAAE,CAAC;EAC1F;;;;;EAKI,MAAM,OAAO,SAAS;AAClB,WAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,OAAO,CAAA,GAAI,OAAO,GAAG,EAAE,QAAQ,SAAQ,CAAE,CAAC;EAC3F;AACA;AACY,IAAC,gBAAgB,eAAe,iBAAiB;EACzD,KAAK,MAAM,IAAI,uBAAsB;AACzC,CAAC;", "names": ["ExceptionCode", "registerPlugin", "p", "headers"]}