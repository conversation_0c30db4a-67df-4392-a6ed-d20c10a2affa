/**
 * Performance Test Script for SmartBoutique
 * Tests the application with realistic data volumes for commercial use
 */

// Test data generators
function generateTestProducts(count = 1000) {
  const categories = ['Alimentation', 'Vêtements', 'Électronique', 'Boissons', 'Épicerie', 'Livres', 'Cosmétiques'];
  const products = [];
  
  for (let i = 1; i <= count; i++) {
    const prixAchatCDF = Math.floor(Math.random() * 50000) + 1000; // 1000-51000 CDF
    const prixVenteCDF = Math.floor(prixAchatCDF * (1.2 + Math.random() * 0.8)); // 20-100% markup
    
    products.push({
      id: `prod_${i}`,
      nom: `Produit Test ${i}`,
      description: `Description détaillée du produit ${i}`,
      prixAchatCDF: prixAchatCDF,
      prixAchatUSD: Math.round((prixAchatCDF / 2800) * 100) / 100,
      prixCDF: prixVenteCDF,
      prixUSD: Math.round((prixVenteCDF / 2800) * 100) / 100,
      beneficeUnitaireCDF: prixVenteCDF - prixAchatCDF,
      beneficeUnitaireUSD: Math.round(((prixVenteCDF - prixAchatCDF) / 2800) * 100) / 100,
      codeQR: `QR${i.toString().padStart(6, '0')}`,
      categorie: categories[Math.floor(Math.random() * categories.length)],
      stock: Math.floor(Math.random() * 100) + 1,
      stockMin: Math.floor(Math.random() * 10) + 1,
      codeBarres: `BC${i.toString().padStart(10, '0')}`,
      dateCreation: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      dateModification: new Date().toISOString()
    });
  }
  
  return products;
}

function generateTestSales(products, count = 5000) {
  const paymentMethods = ['cash', 'mobile_money', 'bank'];
  const sales = [];
  
  for (let i = 1; i <= count; i++) {
    const saleDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000); // Last 90 days
    const numItems = Math.floor(Math.random() * 5) + 1; // 1-5 items per sale
    const items = [];
    let totalCDF = 0;
    
    for (let j = 0; j < numItems; j++) {
      const product = products[Math.floor(Math.random() * products.length)];
      const quantity = Math.floor(Math.random() * 3) + 1;
      const itemTotal = product.prixCDF * quantity;
      
      items.push({
        productId: product.id,
        nom: product.nom,
        prix: product.prixCDF,
        quantite: quantity,
        total: itemTotal
      });
      
      totalCDF += itemTotal;
    }
    
    sales.push({
      id: `sale_${i}`,
      items: items,
      totalCDF: totalCDF,
      totalUSD: Math.round((totalCDF / 2800) * 100) / 100,
      methodePaiement: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
      client: Math.random() > 0.7 ? `Client ${Math.floor(Math.random() * 100)}` : '',
      vendeur: 'Test User',
      date: saleDate.toISOString(),
      numeroRecu: `REC${i.toString().padStart(6, '0')}`
    });
  }
  
  return sales;
}

function generateTestDebts(sales, count = 500) {
  const debts = [];
  const relevantSales = sales.filter(sale => Math.random() > 0.9); // 10% of sales become debts
  
  for (let i = 0; i < Math.min(count, relevantSales.length); i++) {
    const sale = relevantSales[i];
    const paidAmount = Math.floor(sale.totalCDF * Math.random() * 0.8); // 0-80% paid
    const remainingAmount = sale.totalCDF - paidAmount;
    
    debts.push({
      id: `debt_${i + 1}`,
      saleId: sale.id,
      client: sale.client || `Client Dette ${i + 1}`,
      montantTotalCDF: sale.totalCDF,
      montantTotalUSD: sale.totalUSD,
      montantPayeCDF: paidAmount,
      montantPayeUSD: Math.round((paidAmount / 2800) * 100) / 100,
      montantRestantCDF: remainingAmount,
      montantRestantUSD: Math.round((remainingAmount / 2800) * 100) / 100,
      dateCreation: sale.date,
      dateEcheance: new Date(new Date(sale.date).getTime() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      statut: remainingAmount === 0 ? 'paid' : (Math.random() > 0.8 ? 'overdue' : 'pending'),
      statutPaiement: remainingAmount === 0 ? 'paye' : 'impaye',
      paiements: paidAmount > 0 ? [{
        id: `payment_${i + 1}`,
        montantCDF: paidAmount,
        montantUSD: Math.round((paidAmount / 2800) * 100) / 100,
        date: sale.date,
        methodePaiement: 'cash'
      }] : []
    });
  }
  
  return debts;
}

function generateTestExpenses(count = 1000) {
  const categories = ['Loyer', 'Électricité', 'Eau', 'Salaires', 'Approvisionnement', 'Marketing', 'Transport', 'Autres'];
  const expenses = [];
  
  for (let i = 1; i <= count; i++) {
    const montantCDF = Math.floor(Math.random() * 100000) + 5000; // 5000-105000 CDF
    const expenseDate = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000); // Last 180 days
    
    expenses.push({
      id: `expense_${i}`,
      description: `Dépense ${categories[Math.floor(Math.random() * categories.length)]} ${i}`,
      montantCDF: montantCDF,
      montantUSD: Math.round((montantCDF / 2800) * 100) / 100,
      categorie: categories[Math.floor(Math.random() * categories.length)],
      dateDepense: expenseDate.toISOString(),
      notes: `Notes pour la dépense ${i}`,
      creePar: 'Test User',
      numeroRecu: `EXP${i.toString().padStart(6, '0')}`
    });
  }
  
  return expenses;
}

function generateTestUsers(count = 20) {
  const roles = ['employee', 'admin', 'super_admin'];
  const users = [];
  
  for (let i = 1; i <= count; i++) {
    users.push({
      id: `user_${i}`,
      nom: `Utilisateur Test ${i}`,
      email: `user${i}@smartboutique.test`,
      role: roles[Math.floor(Math.random() * roles.length)],
      motDePasse: 'test123',
      dateCreation: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
      actif: Math.random() > 0.1 // 90% active users
    });
  }
  
  return users;
}

// Performance test functions
function measurePerformance(name, fn) {
  console.log(`\n🧪 Testing ${name}...`);
  const startTime = performance.now();
  const result = fn();
  const endTime = performance.now();
  const duration = endTime - startTime;
  
  console.log(`✅ ${name} completed in ${duration.toFixed(2)}ms`);
  return { result, duration };
}

// Main test execution
function runPerformanceTests() {
  console.log('🚀 Starting SmartBoutique Performance Tests');
  console.log('=' .repeat(50));
  
  // Generate test data
  const { result: products, duration: productGenTime } = measurePerformance(
    'Product Generation (1000 items)', 
    () => generateTestProducts(1000)
  );
  
  const { result: sales, duration: salesGenTime } = measurePerformance(
    'Sales Generation (5000 items)', 
    () => generateTestSales(products, 5000)
  );
  
  const { result: debts, duration: debtsGenTime } = measurePerformance(
    'Debts Generation (500 items)', 
    () => generateTestDebts(sales, 500)
  );
  
  const { result: expenses, duration: expensesGenTime } = measurePerformance(
    'Expenses Generation (1000 items)', 
    () => generateTestExpenses(1000)
  );
  
  const { result: users, duration: usersGenTime } = measurePerformance(
    'Users Generation (20 items)', 
    () => generateTestUsers(20)
  );
  
  // Data processing tests
  const { duration: filterTime } = measurePerformance(
    'Product Filtering (by category)', 
    () => products.filter(p => p.categorie === 'Alimentation')
  );
  
  const { duration: sortTime } = measurePerformance(
    'Sales Sorting (by date)', 
    () => sales.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
  );
  
  const { duration: aggregateTime } = measurePerformance(
    'Revenue Calculation', 
    () => {
      const totalRevenue = sales.reduce((sum, sale) => sum + sale.totalCDF, 0);
      const totalExpenses = expenses.reduce((sum, expense) => sum + expense.montantCDF, 0);
      return { totalRevenue, totalExpenses, profit: totalRevenue - totalExpenses };
    }
  );
  
  // Memory usage estimation
  const dataSize = JSON.stringify({ products, sales, debts, expenses, users }).length;
  const memorySizeMB = (dataSize / (1024 * 1024)).toFixed(2);
  
  // Results summary
  console.log('\n📊 PERFORMANCE TEST RESULTS');
  console.log('=' .repeat(50));
  console.log(`📦 Data Generated:`);
  console.log(`   • Products: ${products.length}`);
  console.log(`   • Sales: ${sales.length}`);
  console.log(`   • Debts: ${debts.length}`);
  console.log(`   • Expenses: ${expenses.length}`);
  console.log(`   • Users: ${users.length}`);
  console.log(`   • Total Memory: ${memorySizeMB} MB`);
  
  console.log(`\n⏱️  Performance Metrics:`);
  console.log(`   • Product Generation: ${productGenTime.toFixed(2)}ms`);
  console.log(`   • Sales Generation: ${salesGenTime.toFixed(2)}ms`);
  console.log(`   • Data Filtering: ${filterTime.toFixed(2)}ms`);
  console.log(`   • Data Sorting: ${sortTime.toFixed(2)}ms`);
  console.log(`   • Aggregation: ${aggregateTime.toFixed(2)}ms`);
  
  console.log(`\n✅ COMMERCIAL READINESS ASSESSMENT:`);
  console.log(`   • Data Volume: ${dataSize > 1000000 ? '✅ EXCELLENT' : '⚠️  MODERATE'} (${memorySizeMB} MB)`);
  console.log(`   • Generation Speed: ${productGenTime < 100 ? '✅ FAST' : '⚠️  SLOW'}`);
  console.log(`   • Processing Speed: ${filterTime < 50 ? '✅ FAST' : '⚠️  SLOW'}`);
  console.log(`   • Memory Efficiency: ${memorySizeMB < 50 ? '✅ EFFICIENT' : '⚠️  HIGH'}`);
  
  // Export test data for manual testing
  const testData = {
    products: products.slice(0, 100), // First 100 for manual testing
    sales: sales.slice(0, 200),
    debts: debts.slice(0, 50),
    expenses: expenses.slice(0, 100),
    users: users.slice(0, 10),
    metadata: {
      generatedAt: new Date().toISOString(),
      totalItems: products.length + sales.length + debts.length + expenses.length + users.length,
      memorySizeMB: memorySizeMB
    }
  };
  
  console.log(`\n💾 Test data sample exported for manual testing`);
  console.log(`   • Use this data to test the application manually`);
  console.log(`   • Import via Settings > Data Import`);
  
  return testData;
}

// Export for use in browser console or Node.js
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runPerformanceTests, generateTestProducts, generateTestSales };
} else if (typeof window !== 'undefined') {
  window.SmartBoutiquePerformanceTest = { runPerformanceTests, generateTestProducts, generateTestSales };
}

// Auto-run if executed directly
if (typeof require !== 'undefined' && require.main === module) {
  runPerformanceTests();
}
