<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CSV Encoding - SmartBoutique</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #2980b9;
        }
        .success {
            color: #27ae60;
            font-weight: bold;
        }
        .info {
            background-color: #e8f4fd;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test d'Encodage CSV - SmartBoutique</h1>
        
        <div class="info">
            <strong>Objectif:</strong> Tester que les caractères français (é, è, à, ç, etc.) s'affichent correctement dans Excel après export CSV.
        </div>

        <div class="test-section">
            <h3>Test 1: CSV sans BOM (Problème)</h3>
            <p>Ce test génère un CSV sans BOM UTF-8. Les caractères français apparaîtront corrompus dans Excel.</p>
            <button onclick="downloadCSVWithoutBOM()">Télécharger CSV sans BOM</button>
        </div>

        <div class="test-section">
            <h3>Test 2: CSV avec BOM UTF-8 (Solution)</h3>
            <p>Ce test génère un CSV avec BOM UTF-8. Les caractères français s'afficheront correctement dans Excel.</p>
            <button onclick="downloadCSVWithBOM()">Télécharger CSV avec BOM</button>
        </div>

        <div class="warning">
            <strong>Instructions de test:</strong>
            <ol>
                <li>Téléchargez les deux fichiers CSV</li>
                <li>Ouvrez-les dans Microsoft Excel</li>
                <li>Comparez l'affichage des caractères français</li>
                <li>Le fichier "avec BOM" devrait afficher correctement: Catégorie, Créé, Dépense, etc.</li>
                <li>Le fichier "sans BOM" devrait afficher: CatÃ©gorie, CrÃ©Ã©, DÃ©pense, etc.</li>
            </ol>
        </div>

        <div id="result" class="success" style="display: none;">
            ✅ Fichiers téléchargés avec succès! Ouvrez-les dans Excel pour comparer.
        </div>
    </div>

    <script>
        // Test data with French accented characters
        const testData = [
            {
                description: "Achat de fournitures de bureau",
                categorie: "Fournitures de bureau", 
                montantCDF: "25000",
                montantUSD: "10.50",
                dateDepense: "23/07/2025",
                creePar: "Créé par Admin",
                notes: "Dépense nécessaire pour l'équipe"
            },
            {
                description: "Frais de transport - Livraison",
                categorie: "Transport",
                montantCDF: "14000", 
                montantUSD: "5.00",
                dateDepense: "22/07/2025",
                creePar: "Créé par Vendeur",
                notes: "Livraison à Gombe - très urgent"
            },
            {
                description: "Réparation équipement électronique",
                categorie: "Maintenance",
                montantCDF: "70000",
                montantUSD: "25.00", 
                dateDepense: "21/07/2025",
                creePar: "Créé par Technicien",
                notes: "Réparation nécessaire - garantie expirée"
            }
        ];

        function createCSVContent(includeBOM = false) {
            const headers = 'Description,Catégorie,Montant CDF,Montant USD,Date Dépense,Créé par,Notes';
            
            const rows = testData.map(item => {
                return [
                    `"${item.description}"`,
                    `"${item.categorie}"`,
                    item.montantCDF,
                    item.montantUSD,
                    item.dateDepense,
                    `"${item.creePar}"`,
                    `"${item.notes}"`
                ].join(',');
            });

            const csvContent = [headers, ...rows].join('\n');
            
            // Add UTF-8 BOM if requested
            return includeBOM ? '\uFEFF' + csvContent : csvContent;
        }

        function downloadCSV(content, filename) {
            const blob = new Blob([content], { type: 'text/csv;charset=utf-8' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
            
            document.getElementById('result').style.display = 'block';
        }

        function downloadCSVWithoutBOM() {
            const content = createCSVContent(false);
            downloadCSV(content, 'test_sans_BOM_probleme.csv');
        }

        function downloadCSVWithBOM() {
            const content = createCSVContent(true);
            downloadCSV(content, 'test_avec_BOM_solution.csv');
        }
    </script>
</body>
</html>
