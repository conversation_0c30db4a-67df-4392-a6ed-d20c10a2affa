# SmartBoutique Currency and Financial Calculation Standardization

## Overview

This document outlines the comprehensive currency and financial calculation standardization implemented in SmartBoutique to ensure consistency, accuracy, and maintainability across all financial operations.

## Key Features

### 1. Centralized Currency Utilities (`src/utils/currencyUtils.js`)

All currency operations now use a single, standardized utility module that provides:

- **Consistent rounding** to 2 decimal places using `Math.round(value * 100) / 100`
- **Standard exchange rate** of 2800 CDF/USD as the default constant
- **French number formatting** with spaces as thousands separators
- **Comprehensive input validation** with French error messages
- **Error handling** with fallback values and logging

### 2. Function Naming and Terminology

#### Revenue Functions (Chiffre d'affaires - Money coming in from sales)
- `calculateChiffresAffaires()` - Total sales revenue
- `getRevenusParPeriode()` - Period-based revenue
- `calculateRevenue()` - Single item revenue calculation

#### Profit Functions (Bénéfice - Actual gain after costs)
- `calculateBenefice()` - Net profit (Revenue - Purchase costs)
- `calculateBeneficeUnitaire()` - Unit profit calculation
- `calculateMargeProfit()` - Profit margin percentage
- `calculatePotentialProfit()` - Inventory potential profit

### 3. Currency Conversion

```javascript
// Standardized conversion functions
convertCurrency(amount, fromCurrency, toCurrency, exchangeRate = 2800)
convertCDFToUSD(amountCDF, exchangeRate = 2800)
convertUSDToCDF(amountUSD, exchangeRate = 2800)
```

### 4. French Number Formatting

```javascript
// Examples of French formatting
formatCurrency(2800.50, 'CDF') // "2 800,50 CDF"
formatCurrency(1.50, 'USD')    // "$1,50"
formatNumberFrench(1234.56)    // "1 234,56"
```

### 5. Input Validation

#### Financial Input Validation
```javascript
validateFinancialInput(value, fieldName, options = {
  allowZero: false,
  allowNegative: false,
  minValue: null,
  maxValue: null
})
```

#### Pricing Validation
```javascript
validatePricing(prixAchat, prixVente, fieldNames = {})
```

#### Exchange Rate Validation
```javascript
validateExchangeRate(rate) // Ensures rate > 0
```

### 6. Error Messages (French)

Standardized French error messages for common validation scenarios:

- `NEGATIVE_PRICE`: "Le prix ne peut pas être négatif"
- `ZERO_PRICE`: "Le prix doit être supérieur à zéro"
- `INVALID_EXCHANGE_RATE`: "Le taux de change doit être supérieur à zéro"
- `NEGATIVE_QUANTITY`: "La quantité doit être un nombre positif"
- `SELLING_PRICE_TOO_LOW`: "Le prix de vente doit être supérieur au prix d'achat"

## Implementation Across Components

### 1. Updated Components

#### CurrencyInput Component
- Uses standardized validation for all financial inputs
- Prevents negative values and validates ranges
- Displays French error messages

#### ProductsPage
- Validates purchase and selling prices using `validatePricing()`
- Uses standardized profit calculations
- Implements comprehensive input validation

#### EmployeePaymentsPage
- Validates payment amounts using `validateFinancialInput()`
- Prevents negative payment amounts
- Uses standardized currency formatting

#### SalesPage
- Uses `roundCurrency()` for all total calculations
- Implements standardized revenue calculations
- Validates transaction amounts

#### DashboardPage
- Uses standardized conversion functions for USD calculations
- Implements consistent rounding for all financial displays
- Uses default exchange rate constant

#### ReceiptService
- Uses standardized currency formatting for receipts
- Ensures consistent number formatting across all receipts

### 2. Calculation Logic Separation

#### Revenue vs Profit Distinction
- **Revenue (Chiffre d'affaires)**: Total sales amount (Prix de vente × Quantité)
- **Profit (Bénéfice)**: Revenue minus purchase costs (Prix de vente - Prix d'achat) × Quantité
- **Profit Margin**: (Total Profit / Total Revenue) × 100

#### Dashboard Displays
- Clear separation between "Chiffre d'affaires" and "Bénéfice" sections
- Consistent dual currency display (CDF primary, USD secondary)
- Standardized formatting across all financial metrics

## Usage Examples

### Basic Currency Operations
```javascript
import { 
  convertCDFToUSD, 
  formatCurrency, 
  validateFinancialInput 
} from '@/utils/currencyUtils.js';

// Convert currency
const usdAmount = convertCDFToUSD(2800, 2800); // Returns 1.00

// Format currency
const formatted = formatCurrency(2800.50, 'CDF'); // "2 800,50 CDF"

// Validate input
const validation = validateFinancialInput(100, 'Prix de vente');
if (!validation.isValid) {
  console.error(validation.errors);
}
```

### Revenue and Profit Calculations
```javascript
import { 
  calculateChiffresAffaires, 
  calculateBenefice, 
  calculateMargeProfit 
} from '@/utils/currencyUtils.js';

// Calculate total revenue from sales
const revenue = calculateChiffresAffaires(salesData);

// Calculate total profit
const profit = calculateBenefice(salesData, productsData);

// Calculate profit margin
const margin = calculateMargeProfit(revenue, profit);
```

### Dual Currency Display
```javascript
import { formatDualCurrency } from '@/utils/currencyUtils.js';

const dualDisplay = formatDualCurrency(2800, 2800);
// Returns: {
//   primary: "2 800 CDF",
//   secondary: "$1,00",
//   primaryAmount: 2800,
//   secondaryAmount: 1.00
// }
```

## Testing

Comprehensive test suite (`src/utils/currencyUtils.test.js`) covers:

- Currency conversion accuracy
- French number formatting
- Input validation scenarios
- Revenue and profit calculations
- Error handling and edge cases
- Large number handling
- Empty data handling

## Benefits

1. **Consistency**: All financial calculations use the same rounding and formatting rules
2. **Accuracy**: Standardized rounding prevents floating-point precision errors
3. **Maintainability**: Centralized utilities make updates and bug fixes easier
4. **User Experience**: Consistent French formatting and error messages
5. **Reliability**: Comprehensive validation prevents invalid financial data
6. **Scalability**: Easy to extend with new financial calculation functions

## Migration Notes

- All existing financial components have been updated to use the new utilities
- Legacy calculation functions are maintained for backward compatibility
- Exchange rate constant can be easily updated in one location
- French error messages are centralized and easily translatable

## Future Enhancements

- Support for additional currencies
- Advanced financial analytics functions
- Integration with external exchange rate APIs
- Enhanced validation rules for specific business scenarios
- Automated financial reporting utilities
