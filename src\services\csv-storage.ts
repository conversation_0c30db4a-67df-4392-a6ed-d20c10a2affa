/**
 * CSV-based Storage Service for SmartBoutique Mobile
 * Replaces JSON storage with CSV format for improved data portability
 */

import { Preferences } from '@capacitor/preferences';
import {
  CSVUtils,
  PRODUCT_COLUMNS,
  USER_COLUMNS,
  SALES_COLUMNS,
  DEBT_COLUMNS,
  EXPENSE_COLUMNS,
  EMPLOYEE_COLUMNS
} from './csv-utils';

export class CSVStorageService {
  private readonly CSV_PREFIX = 'smartboutique_csv_';

  /**
   * Generic method to store CSV data
   */
  private async setCSV(key: string, data: any[], columns: any[]): Promise<void> {
    try {
      const csvString = CSVUtils.arrayToCSV(data, columns);
      await Preferences.set({
        key: this.CSV_PREFIX + key,
        value: csvString
      });
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde CSV ${key}:`, error);
      throw error;
    }
  }

  /**
   * Generic method to retrieve CSV data
   */
  private async getCSV(key: string, columns: any[], defaultData: any[] = []): Promise<any[]> {
    try {
      const result = await Preferences.get({ key: this.CSV_PREFIX + key });
      if (result.value) {
        return CSVUtils.csvToArray(result.value, columns);
      }
      return defaultData;
    } catch (error) {
      console.error(`Erreur lors de la lecture CSV ${key}:`, error);
      return defaultData;
    }
  }

  // Products
  async getProducts(): Promise<any[]> {
    const products = await this.getCSV('products', PRODUCT_COLUMNS, []);
    // Ensure all product amounts are valid numbers
    return products.map((product: any) => {
      // Legacy support: if no purchase price, use selling price as purchase price
      if (!product.prixAchatCDF && product.prixCDF) {
        product.prixAchatCDF = product.prixCDF * 0.7; // Assume 30% margin for legacy products
      }

      product.prixAchatCDF = Number(product.prixAchatCDF) || 0;
      product.prixAchatUSD = product.prixAchatUSD ? Number(product.prixAchatUSD) : undefined;
      product.prixCDF = Number(product.prixCDF) || 0;
      product.prixUSD = product.prixUSD ? Number(product.prixUSD) : undefined;

      // Calculate profit fields if they don't exist or are invalid
      if (!product.beneficeUnitaireCDF || isNaN(Number(product.beneficeUnitaireCDF))) {
        product.beneficeUnitaireCDF = product.prixCDF - product.prixAchatCDF;
      } else {
        product.beneficeUnitaireCDF = Number(product.beneficeUnitaireCDF);
      }

      if (!product.beneficeUnitaireUSD || isNaN(Number(product.beneficeUnitaireUSD))) {
        // Calculate USD profit using default exchange rate
        const DEFAULT_EXCHANGE_RATE = 2800;
        product.beneficeUnitaireUSD = Math.round((product.beneficeUnitaireCDF / DEFAULT_EXCHANGE_RATE) * 100) / 100;
      } else {
        product.beneficeUnitaireUSD = Number(product.beneficeUnitaireUSD);
      }

      product.stock = Number(product.stock) || 0;
      product.stockMin = Number(product.stockMin) || 0;

      // Ensure valid dates
      const now = new Date().toISOString();
      if (!product.dateCreation || isNaN(new Date(product.dateCreation).getTime())) {
        product.dateCreation = now;
      }
      if (!product.dateModification || isNaN(new Date(product.dateModification).getTime())) {
        product.dateModification = now;
      }

      return product;
    });
  }

  async setProducts(products: any[]): Promise<void> {
    await this.setCSV('products', products, PRODUCT_COLUMNS);
  }

  // Users
  async getUsers(): Promise<any[]> {
    return this.getCSV('users', USER_COLUMNS, []);
  }

  async setUsers(users: any[]): Promise<void> {
    await this.setCSV('users', users, USER_COLUMNS);
  }

  // Sales
  async getSales(): Promise<any[]> {
    const sales = await this.getCSV('sales', SALES_COLUMNS, []);
    // Ensure all sale amounts are valid numbers
    return sales.map((sale: any) => {
      sale.totalCDF = Number(sale.totalCDF) || 0;
      sale.totalUSD = sale.totalUSD ? Number(sale.totalUSD) : undefined;

      // Ensure produits array exists and all item amounts are numbers
      sale.produits = (sale.produits || []).map((item: any) => ({
        ...item,
        quantite: Number(item.quantite) || 0,
        prixUnitaireCDF: Number(item.prixUnitaireCDF) || 0,
        prixUnitaireUSD: item.prixUnitaireUSD ? Number(item.prixUnitaireUSD) : undefined,
        totalCDF: Number(item.totalCDF) || 0,
        totalUSD: item.totalUSD ? Number(item.totalUSD) : undefined
      }));

      return sale;
    });
  }

  async setSales(sales: any[]): Promise<void> {
    await this.setCSV('sales', sales, SALES_COLUMNS);
  }

  // Debts
  async getDebts(): Promise<any[]> {
    const debts = await this.getCSV('debts', DEBT_COLUMNS, []);
    // Ensure all debt amounts are valid numbers and add missing fields
    return debts.map((debt: any) => {
      if (!debt.statutPaiement) {
        // Set default payment status based on current status
        debt.statutPaiement = debt.statut === 'paid' ? 'paye' : 'impaye';
      }

      // Ensure all amount fields are valid numbers - preserve actual values, don't default to 0
      debt.montantTotalCDF = debt.montantTotalCDF !== undefined && debt.montantTotalCDF !== null && debt.montantTotalCDF !== ''
        ? Number(debt.montantTotalCDF)
        : 0;
      debt.montantTotalUSD = debt.montantTotalUSD ? Number(debt.montantTotalUSD) : undefined;
      debt.montantPayeCDF = debt.montantPayeCDF !== undefined && debt.montantPayeCDF !== null && debt.montantPayeCDF !== ''
        ? Number(debt.montantPayeCDF)
        : 0;
      debt.montantPayeUSD = debt.montantPayeUSD ? Number(debt.montantPayeUSD) : undefined;
      debt.montantRestantCDF = debt.montantRestantCDF !== undefined && debt.montantRestantCDF !== null && debt.montantRestantCDF !== ''
        ? Number(debt.montantRestantCDF)
        : (debt.montantTotalCDF - debt.montantPayeCDF); // Calculate if missing
      debt.montantRestantUSD = debt.montantRestantUSD ? Number(debt.montantRestantUSD) : undefined;

      // Ensure paiements array exists and all payment amounts are numbers
      debt.paiements = (debt.paiements || []).map((payment: any) => ({
        ...payment,
        montantCDF: Number(payment.montantCDF) || 0,
        montantUSD: payment.montantUSD ? Number(payment.montantUSD) : undefined
      }));

      return debt;
    });
  }

  async setDebts(debts: any[]): Promise<void> {
    await this.setCSV('debts', debts, DEBT_COLUMNS);
  }

  // Expenses
  async getExpenses(): Promise<any[]> {
    return this.getCSV('expenses', EXPENSE_COLUMNS, []);
  }

  async setExpenses(expenses: any[]): Promise<void> {
    await this.setCSV('expenses', expenses, EXPENSE_COLUMNS);
  }

  // Employees
  async getEmployees(): Promise<any[]> {
    return this.getCSV('employees', EMPLOYEE_COLUMNS, []);
  }

  async setEmployees(employees: any[]): Promise<void> {
    await this.setCSV('employees', employees, EMPLOYEE_COLUMNS);
  }

  async addEmployee(employee: any): Promise<void> {
    const employees = await this.getEmployees();
    employees.push(employee);
    await this.setEmployees(employees);
  }

  async updateEmployee(updatedEmployee: any): Promise<void> {
    const employees = await this.getEmployees();
    const index = employees.findIndex(emp => emp.id === updatedEmployee.id);
    if (index !== -1) {
      employees[index] = updatedEmployee;
      await this.setEmployees(employees);
    }
  }

  async deleteEmployee(id: string): Promise<void> {
    const employees = await this.getEmployees();
    const filteredEmployees = employees.filter(emp => emp.id !== id);
    await this.setEmployees(filteredEmployees);
  }

  // Settings (stored as JSON for complex structure)
  async getSettings() {
    const result = await Preferences.get({ key: this.CSV_PREFIX + 'settings' });
    if (result.value) {
      try {
        return JSON.parse(result.value);
      } catch (error) {
        console.error('Erreur lors du parsing des paramètres:', error);
      }
    }

    return {
      tauxChangeUSDCDF: 2800,
      seuilStockBas: 10,
      categories: [
        { id: '1', nom: 'Électronique', description: 'Appareils électroniques', couleur: '#2196F3' },
        { id: '2', nom: 'Vêtements', description: 'Vêtements et accessoires', couleur: '#4CAF50' },
        { id: '3', nom: 'Alimentation', description: 'Produits alimentaires', couleur: '#FF9800' },
        { id: '4', nom: 'Maison', description: 'Articles pour la maison', couleur: '#9C27B0' },
        { id: '5', nom: 'Beauté', description: 'Produits de beauté', couleur: '#E91E63' },
        { id: '6', nom: 'Boissons', description: 'Boissons et breuvages', couleur: '#00BCD4' },
        { id: '7', nom: 'Épicerie', description: 'Produits d\'épicerie', couleur: '#795548' },
        { id: '8', nom: 'Livres', description: 'Livres et éducation', couleur: '#607D8B' },
        { id: '9', nom: 'Sport', description: 'Articles de sport', couleur: '#FF5722' },
        { id: '10', nom: 'Santé', description: 'Produits de santé', couleur: '#8BC34A' }
      ],
      entreprise: {
        nom: 'SmartBoutique',
        adresse: 'Kinshasa, RDC',
        telephone: '+243 000 000 000',
        email: '<EMAIL>',
        rccm: '', // Registre de Commerce et du Crédit Mobilier
        idNat: '', // Identification Nationale
        logo: '' // Base64 encoded logo image data
      }
    };
  }

  async setSettings(settings: any): Promise<void> {
    await Preferences.set({
      key: this.CSV_PREFIX + 'settings',
      value: JSON.stringify(settings)
    });
  }

  // Current User
  async getCurrentUser(): Promise<any> {
    const result = await Preferences.get({ key: this.CSV_PREFIX + 'currentUser' });
    return result.value ? JSON.parse(result.value) : null;
  }

  async setCurrentUser(user: any): Promise<void> {
    await Preferences.set({
      key: this.CSV_PREFIX + 'currentUser',
      value: JSON.stringify(user)
    });
  }

  // Initialize default data with comprehensive product catalog
  async initializeDefaultData(): Promise<void> {
    // Initialize users if empty
    const users = await this.getUsers();
    if (users.length === 0) {
      const defaultUsers = [
        {
          id: '1',
          nom: 'Super Admin',
          email: '<EMAIL>',
          role: 'super_admin',
          motDePasse: 'admin123',
          dateCreation: new Date().toISOString(),
          actif: true
        },
        {
          id: '2',
          nom: 'Gestionnaire',
          email: '<EMAIL>',
          role: 'admin',
          motDePasse: 'manager123',
          dateCreation: new Date().toISOString(),
          actif: true
        },
        {
          id: '3',
          nom: 'Employé',
          email: '<EMAIL>',
          role: 'employee',
          motDePasse: 'employee123',
          dateCreation: new Date().toISOString(),
          actif: true
        }
      ];
      await this.setUsers(defaultUsers);
    }

    // Initialize comprehensive product catalog if empty
    const products = await this.getProducts();
    if (products.length === 0) {
      await this.initializeProductCatalog();
    }

    // Initialize sample debts if empty
    const debts = await this.getDebts();
    if (debts.length === 0) {
      await this.initializeSampleDebts();
    }
  }

  // Initialize comprehensive product catalog
  private async initializeProductCatalog(): Promise<void> {
    const sampleProducts = [
      // Existing products (preserved)
      {
        id: '1',
        nom: 'iPhone 15',
        description: 'Smartphone Apple iPhone 15 128GB',
        prixCDF: 2240000,
        prixUSD: 800,
        codeQR: 'SB12345678ABCD',
        categorie: 'Électronique',
        stock: 25,
        stockMin: 5,
        codeBarres: '1234567890123',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '2',
        nom: 'T-shirt Nike',
        description: 'T-shirt Nike en coton, taille M',
        prixCDF: 98000,
        prixUSD: 35,
        codeQR: 'SB12345679EFGH',
        categorie: 'Vêtements',
        stock: 50,
        stockMin: 10,
        codeBarres: '1234567890124',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '3',
        nom: 'Café Arabica',
        description: 'Café Arabica premium 500g',
        prixCDF: 33600,
        prixUSD: 12,
        codeQR: 'SB12345680IJKL',
        categorie: 'Alimentation',
        stock: 8,
        stockMin: 15,
        codeBarres: '1234567890125',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Alimentation & Épicerie
      {
        id: '4',
        nom: 'Sucre',
        description: 'Sucre blanc cristallisé 1kg',
        prixCDF: 8400,
        prixUSD: 3,
        codeQR: 'SB12345681MNOP',
        categorie: 'Épicerie',
        stock: 120,
        stockMin: 20,
        codeBarres: '1234567890126',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '5',
        nom: 'Riz',
        description: 'Riz blanc parfumé 5kg',
        prixCDF: 42000,
        prixUSD: 15,
        codeQR: 'SB12345682QRST',
        categorie: 'Alimentation',
        stock: 80,
        stockMin: 15,
        codeBarres: '1234567890127',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '6',
        nom: 'Sel',
        description: 'Sel de cuisine iodé 500g',
        prixCDF: 2800,
        prixUSD: 1,
        codeQR: 'SB12345683UVWX',
        categorie: 'Épicerie',
        stock: 200,
        stockMin: 30,
        codeBarres: '1234567890128',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Boissons
      {
        id: '7',
        nom: 'Lait',
        description: 'Lait entier UHT 1 litre',
        prixCDF: 5600,
        prixUSD: 2,
        codeQR: 'SB12345684YZAB',
        categorie: 'Boissons',
        stock: 60,
        stockMin: 12,
        codeBarres: '1234567890129',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '8',
        nom: 'Thé',
        description: 'Thé noir en sachets, boîte de 25',
        prixCDF: 11200,
        prixUSD: 4,
        codeQR: 'SB12345685CDEF',
        categorie: 'Boissons',
        stock: 45,
        stockMin: 10,
        codeBarres: '1234567890130',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Vêtements
      {
        id: '9',
        nom: 'Vestes',
        description: 'Veste en jean unisexe, taille L',
        prixCDF: 140000,
        prixUSD: 50,
        codeQR: 'SB12345686GHIJ',
        categorie: 'Vêtements',
        stock: 30,
        stockMin: 5,
        codeBarres: '1234567890131',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Livres
      {
        id: '10',
        nom: 'Livres',
        description: 'Roman français "Le Petit Prince"',
        prixCDF: 22400,
        prixUSD: 8,
        codeQR: 'SB12345687KLMN',
        categorie: 'Livres',
        stock: 25,
        stockMin: 5,
        codeBarres: '1234567890132',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // ADDITIONAL COMPLEMENTARY PRODUCTS
      {
        id: '11',
        nom: 'Huile de Palme',
        description: 'Huile de palme rouge 1 litre',
        prixCDF: 16800,
        prixUSD: 6,
        codeQR: 'SB12345688OPQR',
        categorie: 'Alimentation',
        stock: 40,
        stockMin: 8,
        codeBarres: '1234567890133',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '12',
        nom: 'Farine de Maïs',
        description: 'Farine de maïs blanche 2kg',
        prixCDF: 11200,
        prixUSD: 4,
        codeQR: 'SB12345689STUV',
        categorie: 'Alimentation',
        stock: 75,
        stockMin: 15,
        codeBarres: '1234567890134',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      }
    ];

    await this.setProducts(sampleProducts);
  }

  /**
   * Clear all CSV data from mobile storage
   */
  async clearAllData(): Promise<void> {
    try {
      const keysToRemove = [
        'products',
        'users',
        'sales',
        'debts',
        'expenses',
        'settings',
        'currentUser'
      ];

      for (const key of keysToRemove) {
        await Preferences.remove({ key: this.CSV_PREFIX + key });
      }

      console.log('✅ All CSV data cleared from mobile storage');
    } catch (error) {
      console.error('❌ Error clearing CSV data:', error);
      throw error;
    }
  }

  // Initialize sample debt data
  private async initializeSampleDebts(): Promise<void> {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const twoWeeksFromNow = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000);

    const sampleDebts = [
      // New unpaid debt (0% paid)
      {
        id: 'DET-001',
        venteId: 'VTE-CREDIT-001',
        nomClient: 'Jean Baptiste Mukendi',
        telephoneClient: '+243 812 345 678',
        adresseClient: 'Avenue Lumumba, Kinshasa',
        montantTotalCDF: 150000,
        montantTotalUSD: 53.57,
        montantPayeCDF: 0,
        montantPayeUSD: 0,
        montantRestantCDF: 150000,
        montantRestantUSD: 53.57,
        dateCreation: oneWeekAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [],
        notes: 'Vente à crédit - Produits électroniques'
      },
      // Partially paid debt (40% paid)
      {
        id: 'DET-002',
        venteId: 'VTE-CREDIT-002',
        nomClient: 'Marie Kabila Tshisekedi',
        telephoneClient: '+243 998 765 432',
        adresseClient: 'Boulevard du 30 Juin, Kinshasa',
        montantTotalCDF: 250000,
        montantTotalUSD: 89.29,
        montantPayeCDF: 100000,
        montantPayeUSD: 35.71,
        montantRestantCDF: 150000,
        montantRestantUSD: 53.57,
        dateCreation: twoWeeksAgo.toISOString(),
        dateEcheance: twoWeeksFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [
          {
            id: 'PAY-001',
            montantCDF: 100000,
            montantUSD: 35.71,
            methodePaiement: 'cash',
            datePaiement: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement partiel en espèces'
          }
        ],
        notes: 'Paiement partiel effectué - Vêtements et accessoires'
      },
      // Fully paid debt (100% paid)
      {
        id: 'DET-003',
        venteId: 'VTE-CREDIT-003',
        nomClient: 'Joseph Kabila Kabange',
        telephoneClient: '+243 811 222 333',
        adresseClient: 'Avenue de la Paix, Lubumbashi',
        montantTotalCDF: 180000,
        montantTotalUSD: 64.29,
        montantPayeCDF: 180000,
        montantPayeUSD: 64.29,
        montantRestantCDF: 0,
        montantRestantUSD: 0,
        dateCreation: twoWeeksAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'paid',
        statutPaiement: 'paye',
        paiements: [
          {
            id: 'PAY-002',
            montantCDF: 180000,
            montantUSD: 64.29,
            methodePaiement: 'mobile_money',
            datePaiement: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement complet via Mobile Money'
          }
        ],
        notes: 'Dette entièrement payée - Alimentation et boissons'
      },
      // Overdue debt (past due date)
      {
        id: 'DET-004',
        venteId: 'VTE-CREDIT-004',
        nomClient: 'Fatou Diallo Sankara',
        telephoneClient: '+243 977 888 999',
        adresseClient: 'Quartier Matonge, Kinshasa',
        montantTotalCDF: 320000,
        montantTotalUSD: 114.29,
        montantPayeCDF: 80000,
        montantPayeUSD: 28.57,
        montantRestantCDF: 240000,
        montantRestantUSD: 85.71,
        dateCreation: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        dateEcheance: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString(), // Past due
        statut: 'overdue',
        statutPaiement: 'impaye',
        paiements: [
          {
            id: 'PAY-003',
            montantCDF: 80000,
            montantUSD: 28.57,
            methodePaiement: 'cash',
            datePaiement: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement partiel initial'
          }
        ],
        notes: 'Dette en retard - Nécessite suivi'
      }
    ];

    await this.setDebts(sampleDebts);
  }
}

export const csvStorageService = new CSVStorageService();
