import {
  WebPlugin
} from "./chunk-RX45STYP.js";
import "./chunk-EWTE5DHJ.js";

// node_modules/@capacitor/preferences/dist/esm/web.js
var PreferencesWeb = class extends WebPlugin {
  constructor() {
    super(...arguments);
    this.group = "CapacitorStorage";
  }
  async configure({ group }) {
    if (typeof group === "string") {
      this.group = group;
    }
  }
  async get(options) {
    const value = this.impl.getItem(this.applyPrefix(options.key));
    return { value };
  }
  async set(options) {
    this.impl.setItem(this.applyPrefix(options.key), options.value);
  }
  async remove(options) {
    this.impl.removeItem(this.applyPrefix(options.key));
  }
  async keys() {
    const keys = this.rawKeys().map((k) => k.substring(this.prefix.length));
    return { keys };
  }
  async clear() {
    for (const key of this.rawKeys()) {
      this.impl.removeItem(key);
    }
  }
  async migrate() {
    var _a;
    const migrated = [];
    const existing = [];
    const oldprefix = "_cap_";
    const keys = Object.keys(this.impl).filter((k) => k.indexOf(oldprefix) === 0);
    for (const oldkey of keys) {
      const key = oldkey.substring(oldprefix.length);
      const value = (_a = this.impl.getItem(oldkey)) !== null && _a !== void 0 ? _a : "";
      const { value: currentValue } = await this.get({ key });
      if (typeof currentValue === "string") {
        existing.push(key);
      } else {
        await this.set({ key, value });
        migrated.push(key);
      }
    }
    return { migrated, existing };
  }
  async removeOld() {
    const oldprefix = "_cap_";
    const keys = Object.keys(this.impl).filter((k) => k.indexOf(oldprefix) === 0);
    for (const oldkey of keys) {
      this.impl.removeItem(oldkey);
    }
  }
  get impl() {
    return window.localStorage;
  }
  get prefix() {
    return this.group === "NativeStorage" ? "" : `${this.group}.`;
  }
  rawKeys() {
    return Object.keys(this.impl).filter((k) => k.indexOf(this.prefix) === 0);
  }
  applyPrefix(key) {
    return this.prefix + key;
  }
};
export {
  PreferencesWeb
};
//# sourceMappingURL=web-IHTF6Q3K.js.map
