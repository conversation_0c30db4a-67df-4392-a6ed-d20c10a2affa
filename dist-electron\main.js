"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const electron_1 = require("electron");
const path_1 = require("path");
const isDev = process.env.NODE_ENV === 'development' || !electron_1.app.isPackaged;
function createWindow() {
    // Create the browser window.
    const mainWindow = new electron_1.BrowserWindow({
        width: 1200,
        height: 800,
        minWidth: 1000,
        minHeight: 700,
        show: false, // Don't show immediately to prevent focus issues
        autoHideMenuBar: true,
        webPreferences: {
            nodeIntegration: false,
            contextIsolation: false, // Disable context isolation to fix localStorage issues
            webSecurity: !isDev, // Enable security in production
            allowRunningInsecureContent: isDev // Only allow in development
        }
    });
    // Show window only when ready and focus it properly
    mainWindow.on('ready-to-show', () => {
        mainWindow.show();
        mainWindow.focus(); // Ensure window has focus
        // Additional focus fix for input fields after a short delay
        setTimeout(() => {
            mainWindow.webContents.focus();
        }, 100);
    });
    mainWindow.webContents.setWindowOpenHandler((details) => {
        electron_1.shell.openExternal(details.url);
        return { action: 'deny' };
    });
    // Add comprehensive error handling
    mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription, validatedURL) => {
        console.error('Failed to load:', errorCode, errorDescription, validatedURL);
    });
    mainWindow.webContents.on('render-process-gone', (_, details) => {
        console.error('Render process gone:', details.reason, 'Exit code:', details.exitCode);
    });
    mainWindow.webContents.on('unresponsive', () => {
        console.error('WebContents became unresponsive');
    });
    mainWindow.webContents.on('responsive', () => {
        console.log('WebContents became responsive again');
    });
    // Add console message logging
    mainWindow.webContents.on('console-message', (_, level, message, line, sourceId) => {
        console.log(`Console [${level}]: ${message} (${sourceId}:${line})`);
    });
    // Handle print requests
    electron_1.ipcMain.handle('print-receipt', async (_, options = {}) => {
        try {
            const printOptions = {
                silent: false,
                printBackground: true,
                color: false,
                margin: {
                    marginType: 'none'
                },
                landscape: false,
                pagesPerSheet: 1,
                collate: false,
                copies: 1,
                header: '',
                footer: '',
                ...options
            };
            return new Promise((resolve, reject) => {
                mainWindow.webContents.print(printOptions, (success, failureReason) => {
                    if (success) {
                        console.log('Print job completed successfully');
                        resolve({ success: true });
                    }
                    else {
                        console.error('Print job failed:', failureReason);
                        reject(new Error(failureReason));
                    }
                });
            });
        }
        catch (error) {
            console.error('Error handling print request:', error);
            throw error;
        }
    });
    // Handle print preview requests
    electron_1.ipcMain.handle('print-preview', async () => {
        try {
            mainWindow.webContents.printToPDF({
                pageSize: 'A4',
                printBackground: true,
                landscape: false
            }).then((data) => {
                console.log('PDF preview generated successfully');
                return { success: true, data };
            }).catch((error) => {
                console.error('PDF preview generation failed:', error);
                throw error;
            });
        }
        catch (error) {
            console.error('Error generating print preview:', error);
            throw error;
        }
    });
    // Removed DOM-ready focus fix script - now handled by React useFocusFix hook
    // This prevents competing focus management systems that were causing input delays
    // Load the app
    if (isDev) {
        console.log('Loading in development mode from http://localhost:5173');
        mainWindow.loadURL('http://localhost:5173')
            .then(() => {
            console.log('URL loaded successfully');
            // Open DevTools for debugging but detached to avoid interference
            mainWindow.webContents.openDevTools({ mode: 'detach' });
        })
            .catch(err => {
            console.error('Failed to load URL:', err);
        });
    }
    else {
        console.log('Loading in production mode from', (0, path_1.join)(__dirname, '../dist/index.html'));
        mainWindow.loadFile((0, path_1.join)(__dirname, '../dist/index.html'))
            .then(() => {
            console.log('SmartBoutique loaded successfully in production mode');
            // Set application title for production
            mainWindow.setTitle('SmartBoutique - Gestion de Boutique');
        })
            .catch(err => {
            console.error('Failed to load file:', err);
            // Show error dialog in production
            const { dialog } = require('electron');
            dialog.showErrorBox('Erreur de chargement', 'Impossible de charger SmartBoutique. Veuillez réinstaller l\'application.');
        });
    }
    // Add error event listeners
    mainWindow.webContents.on('did-fail-load', (_, errorCode, errorDescription) => {
        console.error('Failed to load:', errorCode, errorDescription);
    });
    // Handle crashes
    mainWindow.webContents.on('render-process-gone', (_, details) => {
        console.error('WebContents crashed:', details.reason);
    });
}
// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
electron_1.app.whenReady().then(() => {
    createWindow();
    electron_1.app.on('activate', function () {
        // On macOS it's common to re-create a window in the app when the
        // dock icon is clicked and there are no other windows open.
        if (electron_1.BrowserWindow.getAllWindows().length === 0)
            createWindow();
    });
});
// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
electron_1.app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
        electron_1.app.quit();
    }
});
// In this file you can include the rest of your app"s main process code.
// You can also put them in separate files and require them here.
