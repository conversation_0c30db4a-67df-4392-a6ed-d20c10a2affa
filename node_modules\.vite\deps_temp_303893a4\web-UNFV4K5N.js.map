{"version": 3, "sources": ["../../@capacitor-community/sqlite/src/web.ts"], "sourcesContent": ["import { WebPlugin } from '@capacitor/core';\n\nimport type {\n  CapacitorSQLitePlugin,\n  capConnectionOptions,\n  capAllConnectionsOptions,\n  capChangeSecretOptions,\n  capEchoOptions,\n  capEchoResult,\n  capNCConnectionOptions,\n  capNCDatabasePathOptions,\n  capNCDatabasePathResult,\n  capNCOptions,\n  capSetSecretOptions,\n  capSQLiteChanges,\n  capSQLiteExecuteOptions,\n  capSQLiteExportOptions,\n  capSQLiteFromAssetsOptions,\n  capSQLiteHTTPOptions,\n  capSQLiteLocalDiskOptions,\n  capSQLiteImportOptions,\n  capSQLiteJson,\n  capSQLiteOptions,\n  capSQLitePathOptions,\n  capSQLiteQueryOptions,\n  capSQLiteResult,\n  capSQLiteRunOptions,\n  capSQLiteSetOptions,\n  capSQLiteSyncDate,\n  capSQLiteSyncDateOptions,\n  capSQLiteTableOptions,\n  capSQLiteUpgradeOptions,\n  capSQLiteUrl,\n  capSQLiteValues,\n  capVersionResult,\n  capSQLiteExtensionPath,\n  capSQLiteExtensionEnable,\n} from './definitions';\n\nexport class CapacitorSQLiteWeb extends WebPlugin implements CapacitorSQLitePlugin {\n  private jeepSqliteElement: any = null;\n  private isWebStoreOpen = false;\n\n  async initWebStore(): Promise<void> {\n    await customElements.whenDefined('jeep-sqlite');\n\n    this.jeepSqliteElement = document.querySelector('jeep-sqlite');\n    this.ensureJeepSqliteIsAvailable();\n\n    this.jeepSqliteElement.addEventListener('jeepSqliteImportProgress', (event: CustomEvent) => {\n      this.notifyListeners('sqliteImportProgressEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteExportProgress', (event: CustomEvent) => {\n      this.notifyListeners('sqliteExportProgressEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteHTTPRequestEnded', (event: CustomEvent) => {\n      this.notifyListeners('sqliteHTTPRequestEndedEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqlitePickDatabaseEnded', (event: CustomEvent) => {\n      this.notifyListeners('sqlitePickDatabaseEndedEvent', event.detail);\n    });\n    this.jeepSqliteElement.addEventListener('jeepSqliteSaveDatabaseToDisk', (event: CustomEvent) => {\n      this.notifyListeners('sqliteSaveDatabaseToDiskEvent', event.detail);\n    });\n\n    if (!this.isWebStoreOpen) {\n      this.isWebStoreOpen = await this.jeepSqliteElement.isStoreOpen();\n    }\n\n    return;\n  }\n\n  async saveToStore(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.saveToStore(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async getFromLocalDiskToStore(options: capSQLiteLocalDiskOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.getFromLocalDiskToStore(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async saveToLocalDisk(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.saveToLocalDisk(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async echo(options: capEchoOptions): Promise<capEchoResult> {\n    this.ensureJeepSqliteIsAvailable();\n\n    const echoResult = await this.jeepSqliteElement.echo(options);\n    return echoResult;\n  }\n\n  async createConnection(options: capConnectionOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.createConnection(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async open(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.open(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async closeConnection(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.closeConnection(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async getVersion(options: capSQLiteOptions): Promise<capVersionResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const versionResult: capVersionResult = await this.jeepSqliteElement.getVersion(options);\n      return versionResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async checkConnectionsConsistency(options: capAllConnectionsOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n\n    try {\n      const consistencyResult: capSQLiteResult = await this.jeepSqliteElement.checkConnectionsConsistency(options);\n      return consistencyResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async close(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.close(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async beginTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const changes: capSQLiteChanges = await this.jeepSqliteElement.beginTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async commitTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const changes: capSQLiteChanges = await this.jeepSqliteElement.commitTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async rollbackTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const changes: capSQLiteChanges = await this.jeepSqliteElement.rollbackTransaction(options);\n      return changes;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isTransactionActive(options: capSQLiteOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const result: capSQLiteResult = await this.jeepSqliteElement.isTransactionActive(options);\n      return result;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async getTableList(options: capSQLiteOptions): Promise<capSQLiteValues> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const tableListResult: capSQLiteValues = await this.jeepSqliteElement.getTableList(options);\n      return tableListResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async execute(options: capSQLiteExecuteOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const executeResult: capSQLiteChanges = await this.jeepSqliteElement.execute(options);\n      return executeResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async executeSet(options: capSQLiteSetOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const executeResult: capSQLiteChanges = await this.jeepSqliteElement.executeSet(options);\n      return executeResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async run(options: capSQLiteRunOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const runResult: capSQLiteChanges = await this.jeepSqliteElement.run(options);\n      return runResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async query(options: capSQLiteQueryOptions): Promise<capSQLiteValues> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const queryResult: capSQLiteValues = await this.jeepSqliteElement.query(options);\n      return queryResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isDBExists(options: capSQLiteOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const dbExistsResult: capSQLiteResult = await this.jeepSqliteElement.isDBExists(options);\n      return dbExistsResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async isDBOpen(options: capSQLiteOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const isDBOpenResult: capSQLiteResult = await this.jeepSqliteElement.isDBOpen(options);\n      return isDBOpenResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async isDatabase(options: capSQLiteOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const isDatabaseResult: capSQLiteResult = await this.jeepSqliteElement.isDatabase(options);\n      return isDatabaseResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async isTableExists(options: capSQLiteTableOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const tableExistsResult = await this.jeepSqliteElement.isTableExists(options);\n      return tableExistsResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async deleteDatabase(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.deleteDatabase(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async isJsonValid(options: capSQLiteImportOptions): Promise<capSQLiteResult> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const isJsonValidResult = await this.jeepSqliteElement.isJsonValid(options);\n      return isJsonValidResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async importFromJson(options: capSQLiteImportOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const importFromJsonResult: capSQLiteChanges = await this.jeepSqliteElement.importFromJson(options);\n      return importFromJsonResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async exportToJson(options: capSQLiteExportOptions): Promise<capSQLiteJson> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const exportToJsonResult: capSQLiteJson = await this.jeepSqliteElement.exportToJson(options);\n      return exportToJsonResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async createSyncTable(options: capSQLiteOptions): Promise<capSQLiteChanges> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const createSyncTableResult: capSQLiteChanges = await this.jeepSqliteElement.createSyncTable(options);\n      return createSyncTableResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async setSyncDate(options: capSQLiteSyncDateOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.setSyncDate(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async getSyncDate(options: capSQLiteOptions): Promise<capSQLiteSyncDate> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const getSyncDateResult: capSQLiteSyncDate = await this.jeepSqliteElement.getSyncDate(options);\n      return getSyncDateResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n  async deleteExportedRows(options: capSQLiteOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n    try {\n      await this.jeepSqliteElement.deleteExportedRows(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async addUpgradeStatement(options: capSQLiteUpgradeOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.addUpgradeStatement(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async copyFromAssets(options: capSQLiteFromAssetsOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.copyFromAssets(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async getFromHTTPRequest(options: capSQLiteHTTPOptions): Promise<void> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      await this.jeepSqliteElement.getFromHTTPRequest(options);\n      return;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  async getDatabaseList(): Promise<capSQLiteValues> {\n    this.ensureJeepSqliteIsAvailable();\n    this.ensureWebstoreIsOpen();\n\n    try {\n      const databaseListResult: capSQLiteValues = await this.jeepSqliteElement.getDatabaseList();\n      return databaseListResult;\n    } catch (err) {\n      throw new Error(`${err}`);\n    }\n  }\n\n  /**\n   * Checks if the `jeep-sqlite` element is present in the DOM.\n   * If it's not in the DOM, this method throws an Error.\n   *\n   * Attention: This will always fail, if the `intWebStore()` method wasn't called before.\n   */\n  private ensureJeepSqliteIsAvailable() {\n    if (this.jeepSqliteElement === null) {\n      throw new Error(\n        `The jeep-sqlite element is not present in the DOM! Please check the @capacitor-community/sqlite documentation for instructions regarding the web platform.`,\n      );\n    }\n  }\n\n  private ensureWebstoreIsOpen() {\n    if (!this.isWebStoreOpen) {\n      /**\n       * if (!this.isWebStoreOpen)\n        this.isWebStoreOpen = await this.jeepSqliteElement.isStoreOpen();\n       */\n      throw new Error('WebStore is not open yet. You have to call \"initWebStore()\" first.');\n    }\n  }\n\n  ////////////////////////////////////\n  ////// UNIMPLEMENTED METHODS\n  ////////////////////////////////////\n\n  async getUrl(): Promise<capSQLiteUrl> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async getMigratableDbList(options: capSQLitePathOptions): Promise<capSQLiteValues> {\n    console.log('getMigratableDbList', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async addSQLiteSuffix(options: capSQLitePathOptions): Promise<void> {\n    console.log('addSQLiteSuffix', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async deleteOldDatabases(options: capSQLitePathOptions): Promise<void> {\n    console.log('deleteOldDatabases', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async moveDatabasesAndAddSuffix(options: capSQLitePathOptions): Promise<void> {\n    console.log('moveDatabasesAndAddSuffix', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async isSecretStored(): Promise<capSQLiteResult> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async setEncryptionSecret(options: capSetSecretOptions): Promise<void> {\n    console.log('setEncryptionSecret', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async changeEncryptionSecret(options: capChangeSecretOptions): Promise<void> {\n    console.log('changeEncryptionSecret', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async clearEncryptionSecret(): Promise<void> {\n    console.log('clearEncryptionSecret');\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async checkEncryptionSecret(options: capSetSecretOptions): Promise<capSQLiteResult> {\n    console.log('checkEncryptionPassPhrase', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async getNCDatabasePath(options: capNCDatabasePathOptions): Promise<capNCDatabasePathResult> {\n    console.log('getNCDatabasePath', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async createNCConnection(options: capNCConnectionOptions): Promise<void> {\n    console.log('createNCConnection', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async closeNCConnection(options: capNCOptions): Promise<void> {\n    console.log('closeNCConnection', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async isNCDatabase(options: capNCOptions): Promise<capSQLiteResult> {\n    console.log('isNCDatabase', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async isDatabaseEncrypted(options: capSQLiteOptions): Promise<capSQLiteResult> {\n    console.log('isDatabaseEncrypted', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async isInConfigEncryption(): Promise<capSQLiteResult> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n\n  async isInConfigBiometricAuth(): Promise<capSQLiteResult> {\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async loadExtension(options: capSQLiteExtensionPath): Promise<void> {\n    console.log('loadExtension', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n  async enableLoadExtension(options: capSQLiteExtensionEnable): Promise<void> {\n    console.log('enableLoadExtension', options);\n    throw this.unimplemented('Not implemented on web.');\n  }\n}\n"], "mappings": ";;;;;;AAuCM,IAAO,qBAAP,cAAkC,UAAS;EAAjD,cAAA;;AACU,SAAA,oBAAyB;AACzB,SAAA,iBAAiB;EAmiB3B;EAjiBE,MAAM,eAAY;AAChB,UAAM,eAAe,YAAY,aAAa;AAE9C,SAAK,oBAAoB,SAAS,cAAc,aAAa;AAC7D,SAAK,4BAA2B;AAEhC,SAAK,kBAAkB,iBAAiB,4BAA4B,CAAC,UAAsB;AACzF,WAAK,gBAAgB,6BAA6B,MAAM,MAAM;IAChE,CAAC;AACD,SAAK,kBAAkB,iBAAiB,4BAA4B,CAAC,UAAsB;AACzF,WAAK,gBAAgB,6BAA6B,MAAM,MAAM;IAChE,CAAC;AACD,SAAK,kBAAkB,iBAAiB,8BAA8B,CAAC,UAAsB;AAC3F,WAAK,gBAAgB,+BAA+B,MAAM,MAAM;IAClE,CAAC;AACD,SAAK,kBAAkB,iBAAiB,+BAA+B,CAAC,UAAsB;AAC5F,WAAK,gBAAgB,gCAAgC,MAAM,MAAM;IACnE,CAAC;AACD,SAAK,kBAAkB,iBAAiB,gCAAgC,CAAC,UAAsB;AAC7F,WAAK,gBAAgB,iCAAiC,MAAM,MAAM;IACpE,CAAC;AAED,QAAI,CAAC,KAAK,gBAAgB;AACxB,WAAK,iBAAiB,MAAM,KAAK,kBAAkB,YAAW;;AAGhE;EACF;EAEA,MAAM,YAAY,SAAyB;AACzC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,YAAY,OAAO;AAChD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,wBAAwB,SAAkC;AAC9D,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,wBAAwB,OAAO;AAC5D;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,gBAAgB,SAAyB;AAC7C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,gBAAgB,OAAO;AACpD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,KAAK,SAAuB;AAChC,SAAK,4BAA2B;AAEhC,UAAM,aAAa,MAAM,KAAK,kBAAkB,KAAK,OAAO;AAC5D,WAAO;EACT;EAEA,MAAM,iBAAiB,SAA6B;AAClD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,iBAAiB,OAAO;AACrD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,KAAK,SAAyB;AAClC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,KAAK,OAAO;AACzC;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,gBAAgB,SAAyB;AAC7C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,gBAAgB,OAAO;AACpD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,WAAW,SAAyB;AACxC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,gBAAkC,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACvF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,4BAA4B,SAAiC;AACjE,SAAK,4BAA2B;AAEhC,QAAI;AACF,YAAM,oBAAqC,MAAM,KAAK,kBAAkB,4BAA4B,OAAO;AAC3G,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,MAAM,SAAyB;AACnC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,MAAM,OAAO;AAC1C;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,iBAAiB,SAAyB;AAC9C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,kBAAkB,iBAAiB,OAAO;AACvF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,kBAAkB,SAAyB;AAC/C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,kBAAkB,kBAAkB,OAAO;AACxF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,oBAAoB,SAAyB;AACjD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,kBAAkB,oBAAoB,OAAO;AAC1F,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,oBAAoB,SAAyB;AACjD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,SAA0B,MAAM,KAAK,kBAAkB,oBAAoB,OAAO;AACxF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,aAAa,SAAyB;AAC1C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,kBAAmC,MAAM,KAAK,kBAAkB,aAAa,OAAO;AAC1F,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,QAAQ,SAAgC;AAC5C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,gBAAkC,MAAM,KAAK,kBAAkB,QAAQ,OAAO;AACpF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,WAAW,SAA4B;AAC3C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,gBAAkC,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACvF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,IAAI,SAA4B;AACpC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,YAA8B,MAAM,KAAK,kBAAkB,IAAI,OAAO;AAC5E,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,MAAM,SAA8B;AACxC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,cAA+B,MAAM,KAAK,kBAAkB,MAAM,OAAO;AAC/E,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,WAAW,SAAyB;AACxC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,iBAAkC,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACvF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,SAAS,SAAyB;AACtC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,iBAAkC,MAAM,KAAK,kBAAkB,SAAS,OAAO;AACrF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,WAAW,SAAyB;AACxC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,mBAAoC,MAAM,KAAK,kBAAkB,WAAW,OAAO;AACzF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,cAAc,SAA8B;AAChD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,oBAAoB,MAAM,KAAK,kBAAkB,cAAc,OAAO;AAC5E,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,eAAe,SAAyB;AAC5C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,eAAe,OAAO;AACnD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,YAAY,SAA+B;AAC/C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,oBAAoB,MAAM,KAAK,kBAAkB,YAAY,OAAO;AAC1E,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,eAAe,SAA+B;AAClD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,uBAAyC,MAAM,KAAK,kBAAkB,eAAe,OAAO;AAClG,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,aAAa,SAA+B;AAChD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,qBAAoC,MAAM,KAAK,kBAAkB,aAAa,OAAO;AAC3F,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,gBAAgB,SAAyB;AAC7C,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,wBAA0C,MAAM,KAAK,kBAAkB,gBAAgB,OAAO;AACpG,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,YAAY,SAAiC;AACjD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AACzB,QAAI;AACF,YAAM,KAAK,kBAAkB,YAAY,OAAO;AAChD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,YAAY,SAAyB;AACzC,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,oBAAuC,MAAM,KAAK,kBAAkB,YAAY,OAAO;AAC7F,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EACA,MAAM,mBAAmB,SAAyB;AAChD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AACzB,QAAI;AACF,YAAM,KAAK,kBAAkB,mBAAmB,OAAO;AACvD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,oBAAoB,SAAgC;AACxD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,oBAAoB,OAAO;AACxD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,eAAe,SAAmC;AACtD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,eAAe,OAAO;AACnD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,mBAAmB,SAA6B;AACpD,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,KAAK,kBAAkB,mBAAmB,OAAO;AACvD;aACO,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;EAEA,MAAM,kBAAe;AACnB,SAAK,4BAA2B;AAChC,SAAK,qBAAoB;AAEzB,QAAI;AACF,YAAM,qBAAsC,MAAM,KAAK,kBAAkB,gBAAe;AACxF,aAAO;aACA,KAAK;AACZ,YAAM,IAAI,MAAM,GAAG,GAAG,EAAE;;EAE5B;;;;;;;EAQQ,8BAA2B;AACjC,QAAI,KAAK,sBAAsB,MAAM;AACnC,YAAM,IAAI,MACR,4JAA4J;;EAGlK;EAEQ,uBAAoB;AAC1B,QAAI,CAAC,KAAK,gBAAgB;AAKxB,YAAM,IAAI,MAAM,oEAAoE;;EAExF;;;;EAMA,MAAM,SAAM;AACV,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,oBAAoB,SAA6B;AACrD,YAAQ,IAAI,uBAAuB,OAAO;AAC1C,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,gBAAgB,SAA6B;AACjD,YAAQ,IAAI,mBAAmB,OAAO;AACtC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,mBAAmB,SAA6B;AACpD,YAAQ,IAAI,sBAAsB,OAAO;AACzC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,0BAA0B,SAA6B;AAC3D,YAAQ,IAAI,6BAA6B,OAAO;AAChD,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,iBAAc;AAClB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,oBAAoB,SAA4B;AACpD,YAAQ,IAAI,uBAAuB,OAAO;AAC1C,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,uBAAuB,SAA+B;AAC1D,YAAQ,IAAI,0BAA0B,OAAO;AAC7C,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,wBAAqB;AACzB,YAAQ,IAAI,uBAAuB;AACnC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,sBAAsB,SAA4B;AACtD,YAAQ,IAAI,6BAA6B,OAAO;AAChD,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,kBAAkB,SAAiC;AACvD,YAAQ,IAAI,qBAAqB,OAAO;AACxC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,mBAAmB,SAA+B;AACtD,YAAQ,IAAI,sBAAsB,OAAO;AACzC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,kBAAkB,SAAqB;AAC3C,YAAQ,IAAI,qBAAqB,OAAO;AACxC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,aAAa,SAAqB;AACtC,YAAQ,IAAI,gBAAgB,OAAO;AACnC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,oBAAoB,SAAyB;AACjD,YAAQ,IAAI,uBAAuB,OAAO;AAC1C,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,uBAAoB;AACxB,UAAM,KAAK,cAAc,yBAAyB;EACpD;EAEA,MAAM,0BAAuB;AAC3B,UAAM,KAAK,cAAc,yBAAyB;EACpD;EACA,MAAM,cAAc,SAA+B;AACjD,YAAQ,IAAI,iBAAiB,OAAO;AACpC,UAAM,KAAK,cAAc,yBAAyB;EACpD;EACA,MAAM,oBAAoB,SAAiC;AACzD,YAAQ,IAAI,uBAAuB,OAAO;AAC1C,UAAM,KAAK,cAAc,yBAAyB;EACpD;;", "names": []}