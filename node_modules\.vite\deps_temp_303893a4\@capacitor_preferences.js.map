{"version": 3, "sources": ["../../@capacitor/preferences/src/index.ts"], "sourcesContent": ["import { registerPlugin } from '@capacitor/core';\n\nimport type { PreferencesPlugin } from './definitions';\n\nconst Preferences = registerPlugin<PreferencesPlugin>('Preferences', {\n  web: () => import('./web').then(m => new m.PreferencesWeb()),\n});\n\nexport * from './definitions';\nexport { Preferences };\n"], "mappings": ";;;;;;AAIA,IAAM,cAAc,eAAkC,eAAe;EACnE,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,OAAK,IAAI,EAAE,eAAc,CAAE;CAC5D;", "names": []}