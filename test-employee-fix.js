/**
 * Test script to verify employee management functionality
 * This script tests the employee creation and retrieval workflow
 */

// Mock the environment for testing
global.window = {
  localStorage: {
    data: {},
    getItem: function(key) {
      return this.data[key] || null;
    },
    setItem: function(key, value) {
      this.data[key] = value;
    },
    removeItem: function(key) {
      delete this.data[key];
    }
  }
};

// Import the storage service
const { StorageService } = require('./src/services/storage.ts');

async function testEmployeeManagement() {
  console.log('=== Testing Employee Management Fix ===\n');
  
  try {
    // Create storage service instance
    const storageService = new StorageService();
    
    // Test 1: Check initial employee list (should be empty)
    console.log('Test 1: Getting initial employee list...');
    const initialEmployees = storageService.getEmployees();
    console.log('Initial employees:', initialEmployees);
    console.log('Initial count:', initialEmployees.length);
    
    // Test 2: Add a new employee
    console.log('\nTest 2: Adding a new employee...');
    const newEmployee = {
      id: `emp_${Date.now()}_test`,
      nomComplet: '<PERSON>',
      poste: 'Vendeur',
      salaireCDF: 500000,
      salaireUSD: 178.57,
      dateEmbauche: '2025-01-23',
      telephone: '+243123456789',
      adresse: 'Kinshasa, RDC',
      statut: 'actif',
      notes: 'Employé test',
      creePar: 'Test System',
      dateCreation: new Date().toISOString(),
    };
    
    storageService.addEmployee(newEmployee);
    console.log('Employee added successfully');
    
    // Test 3: Retrieve employees after adding
    console.log('\nTest 3: Getting employee list after adding...');
    const employeesAfterAdd = storageService.getEmployees();
    console.log('Employees after add:', employeesAfterAdd);
    console.log('Count after add:', employeesAfterAdd.length);
    
    // Test 4: Verify the employee was saved correctly
    console.log('\nTest 4: Verifying employee data...');
    const addedEmployee = employeesAfterAdd.find(emp => emp.id === newEmployee.id);
    if (addedEmployee) {
      console.log('✓ Employee found in list');
      console.log('✓ Name:', addedEmployee.nomComplet);
      console.log('✓ Position:', addedEmployee.poste);
      console.log('✓ Salary:', addedEmployee.salaireCDF, 'CDF');
    } else {
      console.log('✗ Employee NOT found in list');
    }
    
    // Test 5: Add another employee
    console.log('\nTest 5: Adding second employee...');
    const secondEmployee = {
      id: `emp_${Date.now()}_test2`,
      nomComplet: 'Marie Kabila',
      poste: 'Caissière',
      salaireCDF: 450000,
      salaireUSD: 160.71,
      dateEmbauche: '2025-01-23',
      telephone: '+243987654321',
      adresse: 'Lubumbashi, RDC',
      statut: 'actif',
      notes: 'Deuxième employé test',
      creePar: 'Test System',
      dateCreation: new Date().toISOString(),
    };
    
    storageService.addEmployee(secondEmployee);
    console.log('Second employee added successfully');
    
    // Test 6: Final employee list
    console.log('\nTest 6: Final employee list...');
    const finalEmployees = storageService.getEmployees();
    console.log('Final employees count:', finalEmployees.length);
    finalEmployees.forEach((emp, index) => {
      console.log(`Employee ${index + 1}:`, emp.nomComplet, '-', emp.poste);
    });
    
    // Test 7: Test update functionality
    console.log('\nTest 7: Testing employee update...');
    const updatedEmployee = { ...addedEmployee, poste: 'Chef Vendeur', salaireCDF: 600000 };
    storageService.updateEmployee(updatedEmployee);
    
    const employeesAfterUpdate = storageService.getEmployees();
    const updatedEmp = employeesAfterUpdate.find(emp => emp.id === newEmployee.id);
    console.log('Updated employee position:', updatedEmp.poste);
    console.log('Updated employee salary:', updatedEmp.salaireCDF, 'CDF');
    
    // Test 8: Test delete functionality
    console.log('\nTest 8: Testing employee deletion...');
    storageService.deleteEmployee(secondEmployee.id);
    
    const employeesAfterDelete = storageService.getEmployees();
    console.log('Employees count after deletion:', employeesAfterDelete.length);
    
    const deletedEmp = employeesAfterDelete.find(emp => emp.id === secondEmployee.id);
    if (!deletedEmp) {
      console.log('✓ Employee successfully deleted');
    } else {
      console.log('✗ Employee deletion failed');
    }
    
    console.log('\n=== All Tests Completed ===');
    
  } catch (error) {
    console.error('Test failed with error:', error);
  }
}

// Run the test
testEmployeeManagement();
