/**
 * LogoUpload Component for SmartBoutique
 * Handles business logo upload with validation, preview, and base64 conversion
 */

import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  Alert,
  Card,
  CardContent,
  IconButton,
  CircularProgress
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon
} from '@mui/icons-material';

interface LogoUploadProps {
  value?: string; // Base64 encoded logo data
  onChange: (logoData: string) => void;
  disabled?: boolean;
  maxSizeKB?: number; // Maximum file size in KB
  acceptedFormats?: string[]; // Accepted image formats
}

export const LogoUpload: React.FC<LogoUploadProps> = ({
  value = '',
  onChange,
  disabled = false,
  maxSizeKB = 500, // 500KB default limit
  acceptedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
}) => {
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError('');
    setIsLoading(true);

    // Validate file type
    if (!acceptedFormats.includes(file.type)) {
      setError(`Format non supporté. Formats acceptés: ${acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')}`);
      setIsLoading(false);
      return;
    }

    // Validate file size
    const fileSizeKB = file.size / 1024;
    if (fileSizeKB > maxSizeKB) {
      setError(`Fichier trop volumineux. Taille maximale: ${maxSizeKB}KB`);
      setIsLoading(false);
      return;
    }

    // Convert to base64
    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      if (result) {
        onChange(result);
      }
      setIsLoading(false);
    };

    reader.onerror = () => {
      setError('Erreur lors de la lecture du fichier');
      setIsLoading(false);
    };

    reader.readAsDataURL(file);
  };

  // Handle logo removal
  const handleRemoveLogo = () => {
    onChange('');
    setError('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Trigger file input click
  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  return (
    <Box>
      <Typography variant="subtitle2" gutterBottom>
        Logo de l'entreprise
      </Typography>
      
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedFormats.join(',')}
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        disabled={disabled}
      />

      {/* Logo preview or upload area */}
      <Card variant="outlined" sx={{ mb: 2 }}>
        <CardContent sx={{ textAlign: 'center', py: 3 }}>
          {value ? (
            // Logo preview
            <Box>
              <Box
                component="img"
                src={value}
                alt="Logo de l'entreprise"
                sx={{
                  maxWidth: '200px',
                  maxHeight: '100px',
                  objectFit: 'contain',
                  border: '1px solid #e0e0e0',
                  borderRadius: 1,
                  mb: 2
                }}
              />
              <Box>
                <Button
                  variant="outlined"
                  startIcon={<UploadIcon />}
                  onClick={handleUploadClick}
                  disabled={disabled || isLoading}
                  sx={{ mr: 1 }}
                >
                  Changer
                </Button>
                <IconButton
                  color="error"
                  onClick={handleRemoveLogo}
                  disabled={disabled || isLoading}
                  title="Supprimer le logo"
                >
                  <DeleteIcon />
                </IconButton>
              </Box>
            </Box>
          ) : (
            // Upload area
            <Box>
              <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Aucun logo configuré
              </Typography>
              <Button
                variant="contained"
                startIcon={isLoading ? <CircularProgress size={20} /> : <UploadIcon />}
                onClick={handleUploadClick}
                disabled={disabled || isLoading}
              >
                {isLoading ? 'Chargement...' : 'Télécharger un logo'}
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Error message */}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Help text */}
      <Typography variant="caption" color="text.secondary">
        Formats acceptés: {acceptedFormats.map(f => f.split('/')[1].toUpperCase()).join(', ')} • 
        Taille maximale: {maxSizeKB}KB • 
        Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques
      </Typography>
    </Box>
  );
};

export default LogoUpload;
