{"version": 3, "sources": ["../../@capacitor-community/sqlite/src/definitions.ts", "../../@capacitor-community/sqlite/src/index.ts"], "sourcesContent": ["//import { Capacitor } from '@capacitor/core';\n\n/**\n * CapacitorSQLitePlugin Interface\n */\nexport interface CapacitorSQLitePlugin {\n  /**\n   * Initialize the web store\n   *\n   * @return Promise<void>\n   * @since 3.2.3-1\n   */\n\n  initWebStore(): Promise<void>;\n  /**\n   * Save database to  the web store\n   *\n   * @param options: capSQLiteOptions\n   * @return Promise<void>\n   * @since 3.2.3-1\n   */\n\n  saveToStore(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Get database from local disk and save it to store\n   *\n   * @param options: capSQLiteLocalDiskOptions\n   * @return Promise<void>\n   * @since 4.6.3\n   */\n\n  getFromLocalDiskToStore(options: capSQLiteLocalDiskOptions): Promise<void>;\n  /**\n   * Save database to local disk\n   *\n   * @param options: capSQLiteOptions\n   * @return Promise<void>\n   * @since 4.6.3\n   */\n\n  saveToLocalDisk(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Check if a passphrase exists in a secure store\n   *\n   * @return Promise<capSQLiteResult>\n   * @since 3.0.0-beta.13\n   */\n  isSecretStored(): Promise<capSQLiteResult>;\n  /**\n   * Store a passphrase in a secure store\n   * Update the secret of previous encrypted databases with GlobalSQLite\n   * !!! Only to be used once if you wish to encrypt database !!!\n   *\n   * @param options capSetSecretOptions\n   * @return Promise<void>\n   * @since 3.0.0-beta.13\n   */\n  setEncryptionSecret(options: capSetSecretOptions): Promise<void>;\n  /**\n   * Change the passphrase in a secure store\n   * Update the secret of previous encrypted databases with passphrase\n   * in secure store\n   *\n   * @param options capChangeSecretOptions\n   * @return Promise<void>\n   * @since 3.0.0-beta.13\n   */\n  changeEncryptionSecret(options: capChangeSecretOptions): Promise<void>;\n  /**\n   * Clear the passphrase in the secure store\n   *\n   * @return Promise<void>\n   * @since 3.5.1\n   */\n  clearEncryptionSecret(): Promise<void>;\n  /**\n   * Check encryption passphrase\n   *\n   * @return Promise<capSQLiteResult>\n   * @since 4.6.1\n   */\n\n  checkEncryptionSecret(options: capSetSecretOptions): Promise<capSQLiteResult>;\n\n  /**\n   * create a database connection\n   * @param options capConnectionOptions\n   * @return Promise<void>\n   * @since 2.9.0 refactor\n   */\n  createConnection(options: capConnectionOptions): Promise<void>;\n  /**\n   * close a database connection\n   * @param options capSQLiteOptions\n   * @return Promise<void>\n   * @since 2.9.0 refactor\n   */\n  closeConnection(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Echo a given string\n   *\n   * @param options: capEchoOptions\n   * @return Promise<capEchoResult>\n   * @since 0.0.1\n   */\n  echo(options: capEchoOptions): Promise<capEchoResult>;\n  /**\n   * Opens a SQLite database.\n   * Attention: This re-opens a database if it's already open!\n   *\n   * @param options: capSQLiteOptions\n   * @returns Promise<void>\n   * @since 0.0.1\n   */\n  open(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Close a SQLite database\n   * @param options: capSQLiteOptions\n   * @returns Promise<void>\n   * @since 0.0.1\n   */\n  close(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Begin Database Transaction\n   * @param options\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  beginTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges>;\n  /**\n   * Commit Database Transaction\n   * @param options\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  commitTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges>;\n  /**\n   * Rollback Database Transaction\n   * @param options\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  rollbackTransaction(options: capSQLiteOptions): Promise<capSQLiteChanges>;\n  /**\n   * Is Database Transaction Active\n   * @param options\n   * @returns capSQLiteResult\n   * @since 5.0.7\n   */\n  isTransactionActive(options: capSQLiteOptions): Promise<capSQLiteResult>;\n  /**\n   * Load a SQlite extension\n   * @param options :capSQLiteExtensionPath\n   * @returns Promise<void>\n   * @since 5.0.6\n   */\n  //  loadExtension(options: capSQLiteExtensionPath): Promise<void>;\n  /**\n   * Enable Or Disable Extension Loading\n   * @param options\n   * @returns Promise<void>\n   * @since 5.0.6\n   */\n  //  enableLoadExtension(options: capSQLiteExtensionEnable): Promise<void>;\n  /**\n   * GetUrl get the database Url\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteUrl>\n   * @since 3.3.3-4\n   */\n  getUrl(options: capSQLiteOptions): Promise<capSQLiteUrl>;\n  /**\n   * Get a SQLite database version\n   * @param options: capSQLiteOptions\n   * @returns Promise<void>\n   * @since 3.2.0\n   */\n  getVersion(options: capSQLiteOptions): Promise<capVersionResult>;\n  /**\n   * Execute a Batch of Raw Statements as String\n   * @param options: capSQLiteExecuteOptions\n   * @returns Promise<capSQLiteChanges>\n   * @since 0.0.1\n   */\n  execute(options: capSQLiteExecuteOptions): Promise<capSQLiteChanges>;\n  /**\n   * Execute a Set of Raw Statements as Array of CapSQLiteSet\n   * @param options: capSQLiteSetOptions\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.2.0-2\n   */\n  executeSet(options: capSQLiteSetOptions): Promise<capSQLiteChanges>;\n  /**\n   * Execute a Single Statement\n   * @param options: capSQLiteRunOptions\n   * @returns Promise<capSQLiteChanges>\n   * @since 0.0.1\n   */\n  run(options: capSQLiteRunOptions): Promise<capSQLiteChanges>;\n  /**\n   * Query a Single Statement\n   * @param options: capSQLiteQueryOptions\n   * @returns Promise<capSQLiteValues>\n   * @since 0.0.1\n   */\n  query(options: capSQLiteQueryOptions): Promise<capSQLiteValues>;\n  /**\n   * Check if a SQLite database exists with opened connection\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 2.0.1-1\n   */\n  isDBExists(options: capSQLiteOptions): Promise<capSQLiteResult>;\n  /**\n   * Check if a SQLite database is opened\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isDBOpen(options: capSQLiteOptions): Promise<capSQLiteResult>;\n  /**\n   * Check if a SQLite database is encrypted\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isDatabaseEncrypted(options: capSQLiteOptions): Promise<capSQLiteResult>;\n  /**\n   * Check encryption value in capacitor.config\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isInConfigEncryption(): Promise<capSQLiteResult>;\n  /**\n   * Check encryption value in capacitor.config\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isInConfigBiometricAuth(): Promise<capSQLiteResult>;\n  /**\n   * Check if a SQLite database exists without connection\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isDatabase(options: capSQLiteOptions): Promise<capSQLiteResult>;\n  /**\n   * Check if a table exists in a SQLite database\n   * @param options: capSQLiteTableOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isTableExists(options: capSQLiteTableOptions): Promise<capSQLiteResult>;\n  /**\n   * Delete a SQLite database\n   * @param options: capSQLiteOptions\n   * @returns Promise<void>\n   * @since 0.0.1\n   */\n  deleteDatabase(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Is Json Object Valid\n   * @param options: capSQLiteImportOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 2.0.1-1\n   */\n  isJsonValid(options: capSQLiteImportOptions): Promise<capSQLiteResult>;\n  /**\n   * Import from Json Object\n   * @param options: capSQLiteImportOptions\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.0.0-3\n   */\n  importFromJson(options: capSQLiteImportOptions): Promise<capSQLiteChanges>;\n  /**\n   * Export to Json Object\n   * @param options: capSQLiteExportOptions\n   * @returns Promise<capSQLiteJson>\n   * @since 2.0.1-1\n   */\n  exportToJson(options: capSQLiteExportOptions): Promise<capSQLiteJson>;\n  /**\n   * Create a synchronization table\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.0.1-1\n   */\n  createSyncTable(options: capSQLiteOptions): Promise<capSQLiteChanges>;\n  /**\n   * Set the synchronization date\n   * @param options: capSQLiteSyncDateOptions\n   * @returns Promise<void>\n   * @since 2.0.1-1\n   */\n  setSyncDate(options: capSQLiteSyncDateOptions): Promise<void>;\n  /**\n   * Get the synchronization date\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteSyncDate>\n   * @since 2.9.0\n   */\n  getSyncDate(options: capSQLiteOptions): Promise<capSQLiteSyncDate>;\n  /**\n   * Remove rows with sql_deleted = 1 after an export\n   * @param options\n   * @returns Promise<void>\n   * @since 3.4.3-2\n   */\n  deleteExportedRows(options: capSQLiteOptions): Promise<void>;\n  /**\n   * Add the upgrade Statement for database version upgrading\n   * @param options: capSQLiteUpgradeOptions\n   * @returns Promise<void>\n   * @since 2.4.2-6 iOS & Electron 2.4.2-7 Android\n   */\n  addUpgradeStatement(options: capSQLiteUpgradeOptions): Promise<void>;\n  /**\n   * Copy databases from public/assets/databases folder to application databases folder\n   * @param options: capSQLiteFromAssets  since 3.2.5-2\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  copyFromAssets(options: capSQLiteFromAssetsOptions): Promise<void>;\n  /**\n   * Get database or zipped database(s) from url\n   * @param options: capSQLiteHTTPOptions\n   * @returns Promise<void>\n   * @since 4.1.1\n   */\n  getFromHTTPRequest(options: capSQLiteHTTPOptions): Promise<void>;\n  /**\n   * Get the database list\n   * @returns Promise<capSQLiteValues>\n   * @since 3.0.0-beta.5\n   */\n  getDatabaseList(): Promise<capSQLiteValues>;\n  /**\n   * Get the database's table list\n   * @param options\n   * @returns Promise<capSQLiteValues>\n   * @since 3.4.2-3\n   */\n  getTableList(options: capSQLiteOptions): Promise<capSQLiteValues>;\n  /**\n   * Get the Migratable database list\n   * @param options: capSQLitePathOptions // only iOS & Android since 3.2.4-2\n   * @returns Promise<capSQLiteValues>\n   * @since 3.0.0-beta.5\n   */\n  getMigratableDbList(options: capSQLitePathOptions): Promise<capSQLiteValues>;\n  /**\n   * Add SQLIte Suffix to existing databases\n   * @param options: capSQLitePathOptions\n   * @returns Promise<void>\n   * @since 3.0.0-beta.5\n   */\n  addSQLiteSuffix(options: capSQLitePathOptions): Promise<void>;\n  /**\n   * Delete Old Cordova databases\n   * @param options: capSQLitePathOptions\n   * @returns Promise<void>\n   * @since 3.0.0-beta.5\n   */\n  deleteOldDatabases(options: capSQLitePathOptions): Promise<void>;\n  /**\n   * Moves databases to the location the plugin can read them, and adds sqlite suffix\n   * This resembles calling addSQLiteSuffix and deleteOldDatabases, but it is more performant as it doesn't copy but moves the files\n   * @param options: capSQLitePathOptions\n   */\n  moveDatabasesAndAddSuffix(options: capSQLitePathOptions): Promise<void>;\n  /**\n   * Check Connection Consistency JS <=> Native\n   * return true : consistency, connections are opened\n   * return false : no consistency, connections are closed\n   * @param options: capAllConnectionsOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.11\n   */\n  checkConnectionsConsistency(options: capAllConnectionsOptions): Promise<capSQLiteResult>;\n  /**\n   * get a non conformed database path\n   * @param options capNCDatabasePathOptions\n   * @return Promise<capNCDatabasePathResult>\n   * @since 3.3.3-1\n   */\n  getNCDatabasePath(options: capNCDatabasePathOptions): Promise<capNCDatabasePathResult>;\n  /**\n   * create a non conformed database connection\n   * @param options capNCConnectionOptions\n   * @return Promise<void>\n   * @since 3.3.3-1\n   */\n  createNCConnection(options: capNCConnectionOptions): Promise<void>;\n  /**\n   * close a non conformed database connection\n   * @param options capNCOptions\n   * @return Promise<void>\n   * @since 3.3.3-1\n   */\n  closeNCConnection(options: capNCOptions): Promise<void>;\n  /**\n   * Check if a non conformed database exists without connection\n   * @param options: capNCOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 3.3.3-1\n   */\n  isNCDatabase(options: capNCOptions): Promise<capSQLiteResult>;\n}\n\nexport interface capSetSecretOptions {\n  /**\n   * The passphrase for Encrypted Databases\n   */\n  passphrase?: string;\n}\n\nexport interface capChangeSecretOptions {\n  /**\n   * The new passphrase for Encrypted Databases\n   */\n  passphrase?: string;\n  /**\n   * The old passphrase for Encrypted Databases\n   */\n  oldpassphrase?: string;\n}\nexport interface capEchoOptions {\n  /**\n   *  String to be echoed\n   */\n  value?: string;\n}\nexport interface capSQLiteExtensionPath {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The extension path\n   */\n  path?: string;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n}\nexport interface capSQLiteExtensionEnable {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The enabling toggle (1: ON, 0: OFF)\n   */\n  toggle?: boolean;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n}\nexport interface capConnectionOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The database  version\n   */\n  version?: number;\n  /**\n   * Set to true (database encryption) / false\n   */\n  encrypted?: boolean;\n  /**\n   * Set the mode for database encryption\n   * [\"encryption\", \"secret\", \"newsecret\"]\n   */\n  mode?: string;\n  /**\n   * Set to true (database in read-only mode) / false\n   */\n  readonly?: boolean;\n}\nexport interface capAllConnectionsOptions {\n  /**\n   * the dbName of all connections\n   * @since 3.0.0-beta.10\n   */\n  dbNames?: string[];\n  /**\n   * the openMode (\"RW\" read&write, \"RO\" readonly) of all connections\n   * @since 4.1.0\n   */\n  openModes?: string[];\n}\nexport interface capSQLiteOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * Set to true (database in read-only mode) / false\n   */\n  readonly?: boolean;\n}\n\nexport interface capNCDatabasePathOptions {\n  /**\n   * the database path\n   */\n  path?: string;\n  /**\n   * The database name\n   */\n  database?: string;\n}\nexport interface capNCConnectionOptions {\n  /**\n   * The database path\n   */\n  databasePath?: string;\n  /**\n   * The database  version\n   */\n  version?: number;\n}\n\nexport interface capNCOptions {\n  /**\n   * The database path\n   */\n  databasePath?: string;\n}\nexport interface capSQLiteExecuteOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The batch of raw SQL statements as string\n   */\n  statements?: string;\n  /**\n   * Enable / Disable transactions\n   * default Enable (true)\n   * @since 3.0.0-beta.10\n   */\n  transaction?: boolean;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n  /**\n   * Compatibility SQL92\n   * !!! ELECTRON ONLY\n   * default (true)\n   * @since 5.0.7\n   */\n  isSQL92?: boolean;\n}\nexport interface capSQLiteSetOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The batch of raw SQL statements as Array of capSQLLiteSet\n   */\n  set?: capSQLiteSet[];\n  /**\n   * Enable / Disable transactions\n   * default Enable (true)\n   * @since 3.0.0-beta.10\n   */\n  transaction?: boolean;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n  /**\n   * return mode\n   * default 'no'\n   * value 'all'\n   * value 'one' for Electron platform\n   * @since 5.0.5-3\n   */\n  returnMode?: string;\n  /**\n   * Compatibility SQL92\n   * !!! ELECTRON ONLY\n   * default (true)\n   * @since 5.0.7\n   */\n  isSQL92?: boolean;\n}\nexport interface capSQLiteRunOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * A statement\n   */\n  statement?: string;\n  /**\n   * A set of values for a statement\n   */\n  values?: any[];\n  /**\n   * Enable / Disable transactions\n   * default Enable (true)\n   * @since 3.0.0-beta.10\n   */\n  transaction?: boolean;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n  /**\n   * return mode\n   * default 'no'\n   * value 'all'\n   * value 'one' for Electron platform\n   * @since 5.0.5-3\n   */\n  returnMode?: string;\n  /**\n   * Compatibility SQL92\n   * !!! ELECTRON ONLY\n   * default (true)\n   * @since 5.0.7\n   */\n  isSQL92?: boolean;\n}\nexport interface capSQLiteQueryOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * A statement\n   */\n  statement?: string;\n  /**\n   * A set of values for a statement\n   * Change to any[]\n   * @since 3.0.0-beta.11\n   */\n  values?: any[];\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n  /**\n   * Compatibility SQL92\n   * !!! ELECTRON ONLY\n   * default (true)\n   * @since 5.0.7\n   */\n  isSQL92?: boolean;\n}\nexport interface capTask {\n  /**\n   * define task for executeTransaction\n   * @since 5.6.3\n   */\n  /**\n   * A SQLite statement\n   */\n  statement: string;\n  /**\n   * A set of values to bind to the statement (optional)\n   */\n  values?: any[];\n}\nexport interface capSQLiteImportOptions {\n  /**\n   * Set the JSON object to import\n   *\n   */\n  jsonstring?: string;\n}\nexport interface capSQLiteExportOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * Set the mode to export JSON Object:\n   * \"full\" or \"partial\"\n   *\n   */\n  jsonexportmode?: string;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n  /**\n   * Encrypted\n   * When your database is encrypted\n   * Choose the export Json Object\n   * Encrypted (true) / Unencrypted (false)\n   * default false\n   * @since 5.0.8\n   */\n  encrypted?: boolean;\n}\nexport interface capSQLiteFromAssetsOptions {\n  /**\n   * Set the overwrite mode for the copy from assets\n   * \"true\"/\"false\"  default to \"true\"\n   *\n   */\n  overwrite?: boolean;\n}\nexport interface capSQLiteLocalDiskOptions {\n  /**\n   * Set the overwrite mode for saving the database from local disk to store\n   * \"true\"/\"false\"  default to \"true\"\n   *\n   */\n  overwrite?: boolean;\n}\nexport interface capSQLiteHTTPOptions {\n  /**\n   * The url of the database or the zipped database(s)\n   */\n  url?: string;\n\n  /**\n   * Set the overwrite mode for the copy from assets\n   * \"true\"/\"false\"  default to \"true\"\n   *\n   */\n  overwrite?: boolean;\n}\nexport interface capSQLiteSyncDateOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * Set the synchronization date\n   * Format yyyy-MM-dd'T'HH:mm:ss.SSSZ\n   */\n  syncdate?: string;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n}\nexport interface capSQLiteSet {\n  /**\n   * A statement\n   */\n  statement?: string;\n  /**\n   * the data values list as an Array\n   */\n  values?: any[];\n}\nexport interface capSQLiteUpgradeOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The upgrade options for version upgrade\n   * Array of length 1 to easiest the iOS plugin\n   */\n  upgrade?: capSQLiteVersionUpgrade[];\n}\nexport interface capSQLitePathOptions {\n  /**\n   * The folder path of existing databases\n   * If not given folder path is \"default\"\n   */\n  folderPath?: string;\n  /**\n   * The database name's list to be copied and/or deleted\n   * since 3.2.4-1\n   * If not given all databases in the specify folder path\n   */\n  dbNameList?: string[];\n}\nexport interface capSQLiteTableOptions {\n  /**\n   * The database name\n   */\n  database?: string;\n  /**\n   * The table name\n   */\n  table?: string;\n  /**\n   * ReadOnly / ReadWrite\n   * default ReadWrite (false)\n   * @since 4.1.0-7\n   */\n  readonly?: boolean;\n}\nexport interface capEchoResult {\n  /**\n   * String returned\n   */\n  value?: string;\n}\nexport interface capNCDatabasePathResult {\n  /**\n   * String returned\n   */\n  path?: string;\n}\nexport interface capVersionResult {\n  /**\n   * Number returned\n   */\n  version?: number;\n}\nexport interface capSQLiteResult {\n  /**\n   * result set to true when successful else false\n   */\n  result?: boolean;\n}\nexport interface capSQLiteUrl {\n  /**\n   * a returned url\n   */\n  url?: string;\n}\nexport interface capSQLiteChanges {\n  /**\n   * a returned Changes\n   */\n  changes?: Changes;\n}\nexport interface Changes {\n  /**\n   * the number of changes from an execute or run command\n   */\n  changes?: number;\n  /**\n   * the lastId created from a run command\n   */\n  lastId?: number;\n  /**\n   * values when RETURNING\n   */\n  values?: any[];\n}\nexport interface capSQLiteValues {\n  /**\n   * the data values list as an Array\n   * iOS the first row is the returned ios_columns name list\n   */\n  values?: any[];\n}\nexport interface DBSQLiteValues {\n  /**\n   * the data values list as an Array\n   */\n  values?: any[];\n}\nexport interface capSQLiteJson {\n  /**\n   * an export JSON object\n   */\n  export?: JsonSQLite;\n}\nexport interface capSQLiteSyncDate {\n  /**\n   * the synchronization date\n   */\n  syncDate?: number;\n}\n\n/* JSON Types */\nexport interface EncryptJson {\n  /**\n   * The encrypted JsonSQLite base64 string\n   */\n  expData: string;\n}\nexport interface JsonSQLite {\n  /**\n   * The database name\n   */\n  database: string;\n  /**\n   *  The database version\n   */\n  version: number;\n  /**\n   * Delete the database prior to import (default false)\n   */\n  overwrite?: boolean;\n  /**\n   * Set to true (database encryption) / false\n   */\n  encrypted: boolean;\n  /***\n   * Set the mode\n   * [\"full\", \"partial\"]\n   */\n  mode: string;\n  /***\n   * Array of Table (JsonTable)\n   */\n  tables: JsonTable[];\n  /***\n   * Array of View (JsonView)\n   */\n  views?: JsonView[];\n}\nexport interface JsonTable {\n  /**\n   * The database name\n   */\n  name: string;\n  /***\n   * Array of Schema (JsonColumn)\n   */\n  schema?: JsonColumn[];\n  /***\n   * Array of Index (JsonIndex)\n   */\n  indexes?: JsonIndex[];\n  /***\n   * Array of Trigger (JsonTrigger)\n   */\n  triggers?: JsonTrigger[];\n  /***\n   * Array of Table data\n   */\n  values?: any[][];\n}\nexport interface JsonColumn {\n  /**\n   * The column name\n   */\n  column?: string;\n  /**\n   * The column data (type, unique, ...)\n   */\n  value: string;\n  /**\n   * The column foreign key constraints\n   */\n  foreignkey?: string;\n  /**\n   * the column constraint\n   */\n  constraint?: string;\n}\nexport interface JsonTrigger {\n  /**\n   * The trigger name\n   */\n  name: string;\n  /**\n   * The trigger time event fired\n   */\n  timeevent: string;\n\n  /**\n   * The trigger condition\n   */\n  condition?: string;\n\n  /**\n   * The logic of the trigger\n   */\n  logic: string;\n}\nexport interface JsonIndex {\n  /**\n   * The index name\n   */\n  name: string;\n  /**\n   * The value of the index can have the following formats:\n   * email\n   * email ASC\n   * email, MobileNumber\n   * email ASC, MobileNumber DESC\n   */\n  value: string;\n  /**\n   * the mode (Optional)\n   * UNIQUE\n   */\n  mode?: string;\n}\nexport interface JsonView {\n  /**\n   * The view name\n   */\n  name: string;\n  /**\n   * The view create statement\n   */\n  value: string;\n}\nexport interface capBiometricListener {\n  /**\n   * Biometric ready\n   */\n  result: boolean;\n  message: string;\n}\nexport interface capJsonProgressListener {\n  /**\n   * Progress message\n   */\n  progress?: string;\n}\nexport interface capHttpRequestEndedListener {\n  /**\n   * Message\n   */\n  message?: string;\n}\nexport interface capPickOrSaveDatabaseEndedListener {\n  /**\n   * Pick Database's name\n   */\n  db_name?: string;\n  /**\n   * Message\n   */\n  message?: string;\n}\nexport interface capSQLiteVersionUpgrade {\n  toVersion: number;\n  statements: string[];\n}\n\n/**\n * SQLiteConnection Interface\n */\nexport interface ISQLiteConnection {\n  /**\n   * Init the web store\n   * @returns Promise<void>\n   * @since 3.2.3-1\n   */\n  initWebStore(): Promise<void>;\n  /**\n   * Save the datbase to the web store\n   * @param database\n   * @returns Promise<void>\n   * @since 3.2.3-1\n   */\n  saveToStore(database: string): Promise<void>;\n  /**\n   * Get database from local disk and save it to store\n   *\n   * @param overwrite: boolean\n   * @return Promise<void>\n   * @since 4.6.3\n   */\n  getFromLocalDiskToStore(overwrite: boolean): Promise<void>;\n  /**\n   * Save database to local disk\n   *\n   * @param database: string\n   * @return Promise<void>\n   * @since 4.6.3\n   */\n  saveToLocalDisk(database: string): Promise<void>;\n  /**\n   * Echo a value\n   * @param value\n   * @returns Promise<capEchoResult>\n   * @since 2.9.0 refactor\n   */\n  echo(value: string): Promise<capEchoResult>;\n  /**\n   * Check if a secret is stored\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.13\n   */\n  isSecretStored(): Promise<capSQLiteResult>;\n\n  /**\n   * Set a passphrase in a secure store\n   * @param passphrase\n   * @returns Promise<void>\n   * @since 3.0.0-beta.13\n   */\n  setEncryptionSecret(passphrase: string): Promise<void>;\n  /**\n   * Change the passphrase in a secure store\n   * @param passphrase\n   * @param oldpassphrase\n   * @returns Promise<void>\n   * @since 3.0.0-beta.13\n   */\n  changeEncryptionSecret(passphrase: string, oldpassphrase: string): Promise<void>;\n  /**\n   * Clear the passphrase in a secure store\n   * @returns Promise<void>\n   * @since 3.5.1\n   */\n  clearEncryptionSecret(): Promise<void>;\n  /**\n   * Check the passphrase stored in a secure store\n   * @param oldPassphrase\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.1\n   */\n  checkEncryptionSecret(passphrase: string): Promise<capSQLiteResult>;\n  /**\n   * Add the upgrade Statement for database version upgrading\n   * @param database\n   * @param upgrade @since 5.6.4\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  addUpgradeStatement(database: string, upgrade: capSQLiteVersionUpgrade[]): Promise<void>;\n  /**\n   * Create a connection to a database\n   * @param database\n   * @param encrypted\n   * @param mode\n   * @param version\n   * @param readonly\n   * @returns Promise<SQLiteDBConnection>\n   * @since 2.9.0 refactor\n   */\n  createConnection(\n    database: string,\n    encrypted: boolean,\n    mode: string,\n    version: number,\n    readonly: boolean,\n  ): Promise<SQLiteDBConnection>;\n  /**\n   * Check if a connection exists\n   * @param database\n   * @param readonly\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isConnection(database: string, readonly: boolean): Promise<capSQLiteResult>;\n  /**\n   * Retrieve an existing database connection\n   * @param database\n   * @param readonly\n   * @returns Promise<SQLiteDBConnection>\n   * @since 2.9.0 refactor\n   */\n  retrieveConnection(database: string, readonly: boolean): Promise<SQLiteDBConnection>;\n  /**\n   * Retrieve all database connections\n   * @returns Promise<Map<string, SQLiteDBConnection>>\n   * @since 2.9.0 refactor\n   */\n  retrieveAllConnections(): Promise<Map<string, SQLiteDBConnection>>;\n  /**\n   * Close a database connection\n   * @param database\n   * @param readonly\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  closeConnection(database: string, readonly: boolean): Promise<void>;\n  /**\n   * Close all database connections\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  closeAllConnections(): Promise<void>;\n  /**\n   * Check the consistency between Js Connections\n   * and Native Connections\n   * if inconsistency all connections are removed\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.10\n   */\n  checkConnectionsConsistency(): Promise<capSQLiteResult>;\n  /**\n   * get a non-conformed database path\n   * @param path\n   * @param database\n   * @returns Promise<capNCDatabasePathResult>\n   * @since 3.3.3-1\n   */\n  getNCDatabasePath(path: string, database: string): Promise<capNCDatabasePathResult>;\n  /**\n   * Create a non-conformed database connection\n   * @param databasePath\n   * @param version\n   * @returns Promise<SQLiteDBConnection>\n   * @since 3.3.3-1\n   */\n  createNCConnection(databasePath: string, version: number): Promise<SQLiteDBConnection>;\n  /**\n   * Close a non-conformed database connection\n   * @param databasePath\n   * @returns Promise<void>\n   * @since 3.3.3-1\n   */\n  closeNCConnection(databasePath: string): Promise<void>;\n  /**\n   * Check if a non-conformed databaseconnection exists\n   * @param databasePath\n   * @returns Promise<capSQLiteResult>\n   * @since 3.3.3-1\n   */\n  isNCConnection(databasePath: string): Promise<capSQLiteResult>;\n  /**\n   * Retrieve an existing non-conformed database connection\n   * @param databasePath\n   * @returns Promise<SQLiteDBConnection>\n   * @since 3.3.3-1\n   */\n  retrieveNCConnection(databasePath: string): Promise<SQLiteDBConnection>;\n\n  /**\n   * Import a database From a JSON\n   * @param jsonstring string\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.9.0 refactor\n   */\n  importFromJson(jsonstring: string): Promise<capSQLiteChanges>;\n  /**\n   * Check the validity of a JSON Object\n   * @param jsonstring string\n   * @returns Promise<capSQLiteResult>\n   * @since 2.9.0 refactor\n   */\n  isJsonValid(jsonstring: string): Promise<capSQLiteResult>;\n  /**\n   * Copy databases from public/assets/databases folder to application databases folder\n   * @param overwrite  since 3.2.5-2\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  copyFromAssets(overwrite?: boolean): Promise<void>;\n  /**\n   *\n   * @param url\n   * @param overwrite\n   * @returns Promise<void>\n   * @since 4.1.1\n   */\n  getFromHTTPRequest(url?: string, overwrite?: boolean): Promise<void>;\n  /**\n   * Check if a SQLite database is encrypted\n   * @param options: capSQLiteOptions\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isDatabaseEncrypted(database: string): Promise<capSQLiteResult>;\n  /**\n   * Check encryption value in capacitor.config\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isInConfigEncryption(): Promise<capSQLiteResult>;\n  /**\n   * Check encryption value in capacitor.config\n   * @returns Promise<capSQLiteResult>\n   * @since 4.6.2-2\n   */\n  isInConfigBiometricAuth(): Promise<capSQLiteResult>;\n  /**\n   * Check if a database exists\n   * @param database\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isDatabase(database: string): Promise<capSQLiteResult>;\n  /**\n   * Check if a non conformed database exists\n   * @param databasePath\n   * @returns Promise<capSQLiteResult>\n   * @since 3.3.3-1\n   */\n  isNCDatabase(databasePath: string): Promise<capSQLiteResult>;\n  /**\n   * Get the database list\n   * @returns Promise<capSQLiteValues>\n   * @since 3.0.0-beta.5\n   */\n  getDatabaseList(): Promise<capSQLiteValues>;\n  /**\n   * Get the Migratable database list\n   * @param folderPath: string // only iOS & Android since 3.2.4-2\n   * @returns Promise<capSQLiteValues>\n   * @since 3.0.0-beta.5\n   */\n  getMigratableDbList(folderPath?: string): Promise<capSQLiteValues>;\n\n  /**\n   * Add SQLIte Suffix to existing databases\n   * @param folderPath\n   * @param dbNameList since 3.2.4-1\n   * @returns Promise<void>\n   * @since 3.0.0-beta.5\n   */\n  addSQLiteSuffix(folderPath?: string, dbNameList?: string[]): Promise<void>;\n  /**\n   * Delete Old Cordova databases\n   * @param folderPath\n   * @param dbNameList since 3.2.4-1\n   * @returns Promise<void>\n   * @since 3.0.0-beta.5\n   */\n  deleteOldDatabases(folderPath?: string, dbNameList?: string[]): Promise<void>;\n  /**\n   * Moves databases to the location the plugin can read them, and adds sqlite suffix\n   * This resembles calling addSQLiteSuffix and deleteOldDatabases, but it is more performant as it doesn't copy but moves the files\n   * @param folderPath the origin from where to move the databases\n   * @param dbNameList the names of the databases to move, check out the getMigratableDbList to get a list, an empty list will result in copying all the databases with '.db' extension.\n   */\n  moveDatabasesAndAddSuffix(folderPath?: string, dbNameList?: string[]): Promise<void>;\n}\n/**\n * SQLiteConnection Class\n */\nexport class SQLiteConnection implements ISQLiteConnection {\n  private _connectionDict: Map<string, SQLiteDBConnection> = new Map();\n  constructor(private sqlite: any) {}\n\n  async initWebStore(): Promise<void> {\n    try {\n      await this.sqlite.initWebStore();\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async saveToStore(database: string): Promise<void> {\n    try {\n      await this.sqlite.saveToStore({ database });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async saveToLocalDisk(database: string): Promise<void> {\n    try {\n      await this.sqlite.saveToLocalDisk({ database });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getFromLocalDiskToStore(overwrite?: boolean): Promise<void> {\n    const mOverwrite: boolean = overwrite != null ? overwrite : true;\n\n    try {\n      await this.sqlite.getFromLocalDiskToStore({ overwrite: mOverwrite });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async echo(value: string): Promise<capEchoResult> {\n    try {\n      const res = await this.sqlite.echo({ value });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isSecretStored(): Promise<capSQLiteResult> {\n    try {\n      const res: capSQLiteResult = await this.sqlite.isSecretStored();\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async setEncryptionSecret(passphrase: string): Promise<void> {\n    try {\n      await this.sqlite.setEncryptionSecret({ passphrase: passphrase });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async changeEncryptionSecret(passphrase: string, oldpassphrase: string): Promise<void> {\n    try {\n      await this.sqlite.changeEncryptionSecret({\n        passphrase: passphrase,\n        oldpassphrase: oldpassphrase,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async clearEncryptionSecret(): Promise<void> {\n    try {\n      await this.sqlite.clearEncryptionSecret();\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async checkEncryptionSecret(passphrase: string): Promise<capSQLiteResult> {\n    try {\n      const res: capSQLiteResult = await this.sqlite.checkEncryptionSecret({\n        passphrase: passphrase,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async addUpgradeStatement(database: string, upgrade: capSQLiteVersionUpgrade[]): Promise<void> {\n    try {\n      if (database.endsWith('.db')) database = database.slice(0, -3);\n      await this.sqlite.addUpgradeStatement({\n        database,\n        upgrade,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async createConnection(\n    database: string,\n    encrypted: boolean,\n    mode: string,\n    version: number,\n    readonly: boolean,\n  ): Promise<SQLiteDBConnection> {\n    try {\n      if (database.endsWith('.db')) database = database.slice(0, -3);\n      await this.sqlite.createConnection({\n        database,\n        encrypted,\n        mode,\n        version,\n        readonly,\n      });\n      const conn = new SQLiteDBConnection(database, readonly, this.sqlite);\n      const connName = readonly ? `RO_${database}` : `RW_${database}`;\n      this._connectionDict.set(connName, conn);\n      /*\n      console.log(`*** in createConnection connectionDict: ***`)\n      this._connectionDict.forEach((connection, key) => {\n        console.log(`Key: ${key}, Value: ${connection}`);\n      });\n*/\n      return Promise.resolve(conn);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async closeConnection(database: string, readonly: boolean): Promise<void> {\n    try {\n      if (database.endsWith('.db')) database = database.slice(0, -3);\n      await this.sqlite.closeConnection({ database, readonly });\n      const connName = readonly ? `RO_${database}` : `RW_${database}`;\n      this._connectionDict.delete(connName);\n      /*      console.log(`*** in closeConnection connectionDict: ***`)\n      this._connectionDict.forEach((connection, key) => {\n        console.log(`Key: ${key}, Value: ${connection}`);\n      });\n*/\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isConnection(database: string, readonly: boolean): Promise<capSQLiteResult> {\n    const res: capSQLiteResult = {} as capSQLiteResult;\n    if (database.endsWith('.db')) database = database.slice(0, -3);\n    const connName = readonly ? `RO_${database}` : `RW_${database}`;\n    res.result = this._connectionDict.has(connName);\n    return Promise.resolve(res);\n  }\n  async retrieveConnection(database: string, readonly: boolean): Promise<SQLiteDBConnection> {\n    if (database.endsWith('.db')) database = database.slice(0, -3);\n    const connName = readonly ? `RO_${database}` : `RW_${database}`;\n    if (this._connectionDict.has(connName)) {\n      const conn = this._connectionDict.get(connName);\n      if (typeof conn != 'undefined') return Promise.resolve(conn);\n      else {\n        return Promise.reject(`Connection ${database} is undefined`);\n      }\n    } else {\n      return Promise.reject(`Connection ${database} does not exist`);\n    }\n  }\n  async getNCDatabasePath(path: string, database: string): Promise<capNCDatabasePathResult> {\n    try {\n      const databasePath: capNCDatabasePathResult = await this.sqlite.getNCDatabasePath({\n        path,\n        database,\n      });\n      return Promise.resolve(databasePath);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async createNCConnection(databasePath: string, version: number): Promise<SQLiteDBConnection> {\n    try {\n      await this.sqlite.createNCConnection({\n        databasePath,\n        version,\n      });\n      const conn = new SQLiteDBConnection(databasePath, true, this.sqlite);\n      const connName = `RO_${databasePath})`;\n      this._connectionDict.set(connName, conn);\n      return Promise.resolve(conn);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async closeNCConnection(databasePath: string): Promise<void> {\n    try {\n      await this.sqlite.closeNCConnection({ databasePath });\n      const connName = `RO_${databasePath})`;\n      this._connectionDict.delete(connName);\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isNCConnection(databasePath: string): Promise<capSQLiteResult> {\n    const res: capSQLiteResult = {} as capSQLiteResult;\n    const connName = `RO_${databasePath})`;\n    res.result = this._connectionDict.has(connName);\n    return Promise.resolve(res);\n  }\n  async retrieveNCConnection(databasePath: string): Promise<SQLiteDBConnection> {\n    if (this._connectionDict.has(databasePath)) {\n      const connName = `RO_${databasePath})`;\n      const conn = this._connectionDict.get(connName);\n      if (typeof conn != 'undefined') return Promise.resolve(conn);\n      else {\n        return Promise.reject(`Connection ${databasePath} is undefined`);\n      }\n    } else {\n      return Promise.reject(`Connection ${databasePath} does not exist`);\n    }\n  }\n  async isNCDatabase(databasePath: string): Promise<capSQLiteResult> {\n    try {\n      const res = await this.sqlite.isNCDatabase({ databasePath });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async retrieveAllConnections(): Promise<Map<string, SQLiteDBConnection>> {\n    return this._connectionDict;\n  }\n  async closeAllConnections(): Promise<void> {\n    const delDict: Map<string, SQLiteDBConnection | null> = new Map();\n    try {\n      /*      console.log(`*** in closeAllConnections connectionDict: ***`)\n      this._connectionDict.forEach((connection, key) => {\n        console.log(`Key: ${key}, Value: ${connection}`);\n      });\n*/\n      for (const key of this._connectionDict.keys()) {\n        const database = key.substring(3);\n        const readonly = key.substring(0, 3) === 'RO_' ? true : false;\n        await this.sqlite.closeConnection({ database, readonly });\n        delDict.set(key, null);\n      }\n\n      for (const key of delDict.keys()) {\n        this._connectionDict.delete(key);\n      }\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async checkConnectionsConsistency(): Promise<capSQLiteResult> {\n    try {\n      const keys = [...this._connectionDict.keys()];\n      const openModes: string[] = [];\n      const dbNames: string[] = [];\n      for (const key of keys) {\n        openModes.push(key.substring(0, 2));\n        dbNames.push(key.substring(3));\n      }\n      const res: capSQLiteResult = await this.sqlite.checkConnectionsConsistency({\n        dbNames: dbNames,\n        openModes: openModes,\n      });\n      if (!res.result) this._connectionDict = new Map();\n      return Promise.resolve(res);\n    } catch (err) {\n      this._connectionDict = new Map();\n      return Promise.reject(err);\n    }\n  }\n  async importFromJson(jsonstring: string): Promise<capSQLiteChanges> {\n    try {\n      const ret = await this.sqlite.importFromJson({ jsonstring: jsonstring });\n      return Promise.resolve(ret);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isJsonValid(jsonstring: string): Promise<capSQLiteResult> {\n    try {\n      const ret = await this.sqlite.isJsonValid({ jsonstring: jsonstring });\n      return Promise.resolve(ret);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async copyFromAssets(overwrite?: boolean): Promise<void> {\n    const mOverwrite: boolean = overwrite != null ? overwrite : true;\n\n    try {\n      await this.sqlite.copyFromAssets({ overwrite: mOverwrite });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getFromHTTPRequest(url: string, overwrite?: boolean): Promise<void> {\n    const mOverwrite: boolean = overwrite != null ? overwrite : true;\n    try {\n      await this.sqlite.getFromHTTPRequest({ url, overwrite: mOverwrite });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isDatabaseEncrypted(database: string): Promise<capSQLiteResult> {\n    if (database.endsWith('.db')) database = database.slice(0, -3);\n    try {\n      const res = await this.sqlite.isDatabaseEncrypted({ database: database });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isInConfigEncryption(): Promise<capSQLiteResult> {\n    try {\n      const res = await this.sqlite.isInConfigEncryption();\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isInConfigBiometricAuth(): Promise<capSQLiteResult> {\n    try {\n      const res = await this.sqlite.isInConfigBiometricAuth();\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isDatabase(database: string): Promise<capSQLiteResult> {\n    if (database.endsWith('.db')) database = database.slice(0, -3);\n    try {\n      const res = await this.sqlite.isDatabase({ database: database });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getDatabaseList(): Promise<capSQLiteValues> {\n    try {\n      const res = await this.sqlite.getDatabaseList();\n      const values: string[] = res.values;\n      values.sort();\n      const ret = { values: values };\n      return Promise.resolve(ret);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getMigratableDbList(folderPath?: string): Promise<capSQLiteValues> {\n    const path: string = folderPath ? folderPath : 'default';\n    try {\n      const res = await this.sqlite.getMigratableDbList({\n        folderPath: path,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async addSQLiteSuffix(folderPath?: string, dbNameList?: string[]): Promise<void> {\n    const path: string = folderPath ? folderPath : 'default';\n    const dbList: string[] = dbNameList ? dbNameList : [];\n    try {\n      const res = await this.sqlite.addSQLiteSuffix({\n        folderPath: path,\n        dbNameList: dbList,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async deleteOldDatabases(folderPath?: string, dbNameList?: string[]): Promise<void> {\n    const path: string = folderPath ? folderPath : 'default';\n    const dbList: string[] = dbNameList ? dbNameList : [];\n    try {\n      const res = await this.sqlite.deleteOldDatabases({\n        folderPath: path,\n        dbNameList: dbList,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async moveDatabasesAndAddSuffix(folderPath?: string, dbNameList?: string[]): Promise<void> {\n    const path: string = folderPath ? folderPath : 'default';\n    const dbList: string[] = dbNameList ? dbNameList : [];\n    return this.sqlite.moveDatabasesAndAddSuffix({\n      folderPath: path,\n      dbNameList: dbList,\n    });\n  }\n}\n\n/**\n * SQLiteDBConnection Interface\n */\nexport interface ISQLiteDBConnection {\n  /**\n   * Get SQLite DB Connection DB name\n   * @returns string\n   * @since 2.9.0 refactor\n   */\n  getConnectionDBName(): string;\n\n  /**\n   * Get SQLite DB Connection read-only mode\n   * @returns boolean\n   * @since 4.1.0\n   */\n  getConnectionReadOnly(): boolean;\n\n  /**\n   * Open a SQLite DB Connection\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  open(): Promise<void>;\n  /**\n   * Close a SQLite DB Connection\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  close(): Promise<void>;\n  /**\n   * Begin Database Transaction\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  beginTransaction(): Promise<capSQLiteChanges>;\n  /**\n   * Commit Database Transaction\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  commitTransaction(): Promise<capSQLiteChanges>;\n  /**\n   * Rollback Database Transaction\n   * @returns capSQLiteChanges\n   * @since 5.0.7\n   */\n  rollbackTransaction(): Promise<capSQLiteChanges>;\n  /**\n   * Is Database Transaction Active\n   * @returns capSQLiteResult\n   * @since 5.0.7\n   */\n  isTransactionActive(): Promise<capSQLiteResult>;\n  /**\n   * Get Database Url\n   * @returns Promise<capSQLiteUrl>\n   * @since 3.3.3-4\n   */\n  getUrl(): Promise<capSQLiteUrl>;\n  /**\n   * Get the a SQLite DB Version\n   * @returns Promise<capVersionResult>\n   * @since 3.2.0\n   */\n  getVersion(): Promise<capVersionResult>;\n  /**\n   * Load a SQlite extension\n   * @param path :SQlite extension path\n   * @returns Promise<void>\n   * @since 5.0.6\n   */\n  loadExtension(path: string): Promise<void>;\n  /**\n   * Enable Or Disable Extension Loading\n   * @param toggle true:on false:off\n   * @returns Promise<void>\n   * @since 5.0.6\n   */\n  enableLoadExtension(toggle: boolean): Promise<void>;\n  /**\n   * Execute SQLite DB Connection Statements\n   * @param statements\n   * @param transaction (optional)\n   * @param isSQL92 (optional)\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.9.0 refactor\n   */\n  execute(statements: string, transaction?: boolean, isSQL92?: boolean): Promise<capSQLiteChanges>;\n  /**\n   * Execute SQLite DB Connection Query\n   * @param statement\n   * @param values (optional)\n   * @param isSQL92 (optional)\n   * @returns Promise<Promise<DBSQLiteValues>\n   * @since 2.9.0 refactor\n   */\n  query(statement: string, values?: any[], isSQL92?: boolean): Promise<DBSQLiteValues>;\n  /**\n   * Execute SQLite DB Connection Raw Statement\n   * @param statement\n   * @param values (optional)\n   * @param transaction (optional)\n   * @param returnMode (optional)\n   * @param isSQL92 (optional)\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.9.0 refactor\n   */\n  run(\n    statement: string,\n    values?: any[],\n    transaction?: boolean,\n    returnMode?: string,\n    isSQL92?: boolean,\n  ): Promise<capSQLiteChanges>;\n  /**\n   * Execute SQLite DB Connection Set\n   * @param set\n   * @param transaction (optional)\n   * @param returnMode (optional)\n   * @param isSQL92 (optional)\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.9.0 refactor\n   */\n  executeSet(\n    set: capSQLiteSet[],\n    transaction?: boolean,\n    returnMode?: string,\n    isSQL92?: boolean,\n  ): Promise<capSQLiteChanges>;\n  /**\n   * Check if a SQLite DB Connection exists\n   * @returns Promise<capSQLiteResult>\n   * @since 2.9.0 refactor\n   */\n  isExists(): Promise<capSQLiteResult>;\n  /**\n   * Check if a SQLite database is opened\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isDBOpen(): Promise<capSQLiteResult>;\n  /**\n   * Check if a table exists\n   * @returns Promise<capSQLiteResult>\n   * @since 3.0.0-beta.5\n   */\n  isTable(table: string): Promise<capSQLiteResult>;\n  /**\n   * Get database's table list\n   * @since 3.4.2-3\n   */\n  getTableList(): Promise<DBSQLiteValues>;\n  /**\n   * Delete a SQLite DB Connection\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  delete(): Promise<void>;\n  /**\n   * Create a synchronization table\n   * @returns Promise<capSQLiteChanges>\n   * @since 2.9.0 refactor\n   */\n  createSyncTable(): Promise<capSQLiteChanges>;\n  /**\n   * Set the synchronization date\n   * @param syncdate\n   * @returns Promise<void>\n   * @since 2.9.0 refactor\n   */\n  setSyncDate(syncdate: string): Promise<void>;\n  /**\n   * Get the synchronization date\n   * @returns Promise<capSQLiteSyncDate>\n   * @since 2.9.0 refactor\n   */\n  getSyncDate(): Promise<string>;\n  /**\n   * Export the given database to a JSON Object\n   * @param mode\n   * @param encrypted (optional) since 5.0.8 not for Web platform\n   * @returns Promise<capSQLiteJson>\n   * @since 2.9.0 refactor\n   */\n  exportToJson(mode: string, encrypted?: boolean): Promise<capSQLiteJson>;\n  /**\n   * Remove rows with sql_deleted = 1 after an export\n   * @returns Promise<void>\n   * @since 3.4.3-2\n   */\n  deleteExportedRows(): Promise<void>;\n\n  /**\n   *\n   * @param txn\n   * @param isSQL92\n   * @returns Promise<capSQLiteChanges> since 5.0.7\n   * @since 3.4.0\n   */\n  executeTransaction(txn: capTask[], isSQL92: boolean): Promise<capSQLiteChanges>;\n}\n/**\n * SQLiteDBConnection Class\n */\nexport class SQLiteDBConnection implements ISQLiteDBConnection {\n  constructor(\n    private dbName: string,\n    private readonly: boolean,\n    private sqlite: any,\n  ) {}\n\n  getConnectionDBName(): string {\n    return this.dbName;\n  }\n  getConnectionReadOnly(): boolean {\n    return this.readonly;\n  }\n\n  async open(): Promise<void> {\n    try {\n      await this.sqlite.open({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async close(): Promise<void> {\n    try {\n      await this.sqlite.close({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async beginTransaction(): Promise<capSQLiteChanges> {\n    try {\n      const changes: capSQLiteChanges = await this.sqlite.beginTransaction({\n        database: this.dbName,\n      });\n      return Promise.resolve(changes);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async commitTransaction(): Promise<capSQLiteChanges> {\n    try {\n      const changes: capSQLiteChanges = await this.sqlite.commitTransaction({\n        database: this.dbName,\n      });\n      return Promise.resolve(changes);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async rollbackTransaction(): Promise<capSQLiteChanges> {\n    try {\n      const changes: capSQLiteChanges = await this.sqlite.rollbackTransaction({\n        database: this.dbName,\n      });\n      return Promise.resolve(changes);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isTransactionActive(): Promise<capSQLiteResult> {\n    try {\n      const result: capSQLiteResult = await this.sqlite.isTransactionActive({\n        database: this.dbName,\n      });\n      return Promise.resolve(result);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async loadExtension(path: string): Promise<void> {\n    try {\n      await this.sqlite.loadExtension({\n        database: this.dbName,\n        path: path,\n        readonly: this.readonly,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async enableLoadExtension(toggle: boolean): Promise<void> {\n    try {\n      await this.sqlite.enableLoadExtension({\n        database: this.dbName,\n        toggle: toggle,\n        readonly: this.readonly,\n      });\n      return Promise.resolve();\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async getUrl(): Promise<capSQLiteUrl> {\n    try {\n      const res: capSQLiteUrl = await this.sqlite.getUrl({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getVersion(): Promise<capVersionResult> {\n    try {\n      const version: capVersionResult = await this.sqlite.getVersion({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(version);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getTableList(): Promise<DBSQLiteValues> {\n    try {\n      const res: any = await this.sqlite.getTableList({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async execute(statements: string, transaction = true, isSQL92 = true): Promise<capSQLiteChanges> {\n    try {\n      if (!this.readonly) {\n        const res: any = await this.sqlite.execute({\n          database: this.dbName,\n          statements: statements,\n          transaction: transaction,\n          readonly: false,\n          isSQL92: isSQL92,\n        });\n        return Promise.resolve(res);\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async query(statement: string, values?: any[], isSQL92 = true): Promise<DBSQLiteValues> {\n    let res: any;\n    try {\n      if (values && values.length > 0) {\n        res = await this.sqlite.query({\n          database: this.dbName,\n          statement: statement,\n          values: values,\n          readonly: this.readonly,\n          isSQL92: true,\n        });\n      } else {\n        res = await this.sqlite.query({\n          database: this.dbName,\n          statement: statement,\n          values: [],\n          readonly: this.readonly,\n          isSQL92: isSQL92,\n        });\n      }\n\n      // reorder rows for ios\n      res = await this.reorderRows(res);\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async run(\n    statement: string,\n    values?: any[],\n    transaction = true,\n    returnMode = 'no',\n    isSQL92 = true,\n  ): Promise<capSQLiteChanges> {\n    let res: any;\n    try {\n      if (!this.readonly) {\n        if (values && values.length > 0) {\n          res = await this.sqlite.run({\n            database: this.dbName,\n            statement: statement,\n            values: values,\n            transaction: transaction,\n            readonly: false,\n            returnMode: returnMode,\n            isSQL92: true,\n          });\n        } else {\n          res = await this.sqlite.run({\n            database: this.dbName,\n            statement: statement,\n            values: [],\n            transaction: transaction,\n            readonly: false,\n            returnMode: returnMode,\n            isSQL92: isSQL92,\n          });\n        }\n        // reorder rows for ios\n        res.changes = await this.reorderRows(res.changes);\n        return Promise.resolve(res);\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async executeSet(\n    set: capSQLiteSet[],\n    transaction = true,\n    returnMode = 'no',\n    isSQL92 = true,\n  ): Promise<capSQLiteChanges> {\n    let res: any;\n    try {\n      if (!this.readonly) {\n        res = await this.sqlite.executeSet({\n          database: this.dbName,\n          set: set,\n          transaction: transaction,\n          readonly: false,\n          returnMode: returnMode,\n          isSQL92: isSQL92,\n        });\n        //      }\n        // reorder rows for ios\n        res.changes = await this.reorderRows(res.changes);\n        return Promise.resolve(res);\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isExists(): Promise<capSQLiteResult> {\n    try {\n      const res: any = await this.sqlite.isDBExists({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isTable(table: string): Promise<capSQLiteResult> {\n    try {\n      const res: capSQLiteResult = await this.sqlite.isTableExists({\n        database: this.dbName,\n        table: table,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async isDBOpen(): Promise<capSQLiteResult> {\n    try {\n      const res: capSQLiteResult = await this.sqlite.isDBOpen({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async delete(): Promise<void> {\n    try {\n      if (!this.readonly) {\n        await this.sqlite.deleteDatabase({\n          database: this.dbName,\n          readonly: false,\n        });\n        return Promise.resolve();\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async createSyncTable(): Promise<capSQLiteChanges> {\n    try {\n      if (!this.readonly) {\n        const res: any = await this.sqlite.createSyncTable({\n          database: this.dbName,\n          readonly: false,\n        });\n        return Promise.resolve(res);\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async setSyncDate(syncdate: string): Promise<void> {\n    try {\n      if (!this.readonly) {\n        await this.sqlite.setSyncDate({\n          database: this.dbName,\n          syncdate: syncdate,\n          readonly: false,\n        });\n        return Promise.resolve();\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async getSyncDate(): Promise<string> {\n    try {\n      const res: any = await this.sqlite.getSyncDate({\n        database: this.dbName,\n        readonly: this.readonly,\n      });\n      let retDate = '';\n      if (res.syncDate > 0) retDate = new Date(res.syncDate * 1000).toISOString();\n      return Promise.resolve(retDate);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async exportToJson(mode: string, encrypted = false): Promise<capSQLiteJson> {\n    try {\n      const res: any = await this.sqlite.exportToJson({\n        database: this.dbName,\n        jsonexportmode: mode,\n        readonly: this.readonly,\n        encrypted: encrypted,\n      });\n      return Promise.resolve(res);\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n  async deleteExportedRows(): Promise<void> {\n    try {\n      if (!this.readonly) {\n        await this.sqlite.deleteExportedRows({\n          database: this.dbName,\n          readonly: false,\n        });\n        return Promise.resolve();\n      } else {\n        return Promise.reject('not allowed in read-only mode');\n      }\n    } catch (err) {\n      return Promise.reject(err);\n    }\n  }\n\n  async executeTransaction(txn: capTask[], isSQL92 = true): Promise<capSQLiteChanges> {\n    let changes = 0;\n    let isActive = false;\n    if (!this.readonly) {\n      await this.sqlite.beginTransaction({\n        database: this.dbName,\n      });\n      isActive = await this.sqlite.isTransactionActive({\n        database: this.dbName,\n      });\n      if (!isActive) {\n        return Promise.reject('After Begin Transaction, no transaction active');\n      }\n      try {\n        for (const task of txn) {\n          if (typeof task !== 'object' || !('statement' in task)) {\n            throw new Error('Error a task.statement must be provided');\n          }\n          if ('values' in task && task.values && task.values.length > 0) {\n            const retMode = task.statement.toUpperCase().includes('RETURNING') ? 'all' : 'no';\n            const ret = await this.sqlite.run({\n              database: this.dbName,\n              statement: task.statement,\n              values: task.values,\n              transaction: false,\n              readonly: false,\n              returnMode: retMode,\n              isSQL92: isSQL92,\n            });\n            if (ret.changes.changes < 0) {\n              throw new Error('Error in transaction method run ');\n            }\n            changes += ret.changes.changes;\n          } else {\n            const ret = await this.sqlite.execute({\n              database: this.dbName,\n              statements: task.statement,\n              transaction: false,\n              readonly: false,\n            });\n            if (ret.changes.changes < 0) {\n              throw new Error('Error in transaction method execute ');\n            }\n            changes += ret.changes.changes;\n          }\n        }\n        // commit\n        const retC = await this.sqlite.commitTransaction({\n          database: this.dbName,\n        });\n        changes += retC.changes.changes;\n        const retChanges = { changes: { changes: changes } };\n        return Promise.resolve(retChanges);\n      } catch (err: any) {\n        // rollback\n        const msg = err.message ? err.message : err;\n        await this.sqlite.rollbackTransaction({\n          database: this.dbName,\n        });\n        return Promise.reject(msg);\n      }\n    } else {\n      return Promise.reject('not allowed in read-only mode');\n    }\n  }\n  private async reorderRows(res: any): Promise<any> {\n    const retRes: any = res;\n    if (res?.values && typeof res.values[0] === 'object') {\n      if (Object.keys(res.values[0]).includes('ios_columns')) {\n        const columnList: string[] = res.values[0]['ios_columns'];\n        const iosRes: any[] = [];\n        for (let i = 1; i < res.values.length; i++) {\n          const rowJson: any = res.values[i];\n          const resRowJson: any = {};\n          for (const item of columnList) {\n            resRowJson[item] = rowJson[item];\n          }\n          iosRes.push(resRowJson);\n        }\n        retRes['values'] = iosRes;\n      }\n    }\n\n    return Promise.resolve(retRes);\n  }\n}\n", "import { registerPlugin } from '@capacitor/core';\n\nimport type { CapacitorSQLitePlugin } from './definitions';\n\nconst CapacitorSQLite = registerPlugin<CapacitorSQLitePlugin>('CapacitorSQLite', {\n  web: () => import('./web').then((m) => new m.CapacitorSQLiteWeb()),\n  electron: () => (window as any).CapacitorCustomPlatform.plugins.CapacitorSQLite,\n});\n\nexport { CapacitorSQLite };\nexport * from './definitions';\n"], "mappings": ";;;;;;AA2zCM,IAAO,mBAAP,MAAuB;EAE3B,YAAoB,QAAW;AAAX,SAAA,SAAA;AADZ,SAAA,kBAAmD,oBAAI,IAAG;EAChC;EAElC,MAAM,eAAY;AAChB,QAAI;AACF,YAAM,KAAK,OAAO,aAAY;AAC9B,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,YAAY,UAAgB;AAChC,QAAI;AACF,YAAM,KAAK,OAAO,YAAY,EAAE,SAAQ,CAAE;AAC1C,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,gBAAgB,UAAgB;AACpC,QAAI;AACF,YAAM,KAAK,OAAO,gBAAgB,EAAE,SAAQ,CAAE;AAC9C,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,wBAAwB,WAAmB;AAC/C,UAAM,aAAsB,aAAa,OAAO,YAAY;AAE5D,QAAI;AACF,YAAM,KAAK,OAAO,wBAAwB,EAAE,WAAW,WAAU,CAAE;AACnE,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,KAAK,OAAa;AACtB,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,KAAK,EAAE,MAAK,CAAE;AAC5C,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,iBAAc;AAClB,QAAI;AACF,YAAM,MAAuB,MAAM,KAAK,OAAO,eAAc;AAC7D,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,oBAAoB,YAAkB;AAC1C,QAAI;AACF,YAAM,KAAK,OAAO,oBAAoB,EAAE,WAAsB,CAAE;AAChE,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,uBAAuB,YAAoB,eAAqB;AACpE,QAAI;AACF,YAAM,KAAK,OAAO,uBAAuB;QACvC;QACA;OACD;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,wBAAqB;AACzB,QAAI;AACF,YAAM,KAAK,OAAO,sBAAqB;AACvC,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,sBAAsB,YAAkB;AAC5C,QAAI;AACF,YAAM,MAAuB,MAAM,KAAK,OAAO,sBAAsB;QACnE;OACD;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,oBAAoB,UAAkB,SAAkC;AAC5E,QAAI;AACF,UAAI,SAAS,SAAS,KAAK;AAAG,mBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,YAAM,KAAK,OAAO,oBAAoB;QACpC;QACA;OACD;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,iBACJ,UACA,WACA,MACA,SACA,UAAiB;AAEjB,QAAI;AACF,UAAI,SAAS,SAAS,KAAK;AAAG,mBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,YAAM,KAAK,OAAO,iBAAiB;QACjC;QACA;QACA;QACA;QACA;OACD;AACD,YAAM,OAAO,IAAI,mBAAmB,UAAU,UAAU,KAAK,MAAM;AACnE,YAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,WAAK,gBAAgB,IAAI,UAAU,IAAI;AAOvC,aAAO,QAAQ,QAAQ,IAAI;aACpB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,gBAAgB,UAAkB,UAAiB;AACvD,QAAI;AACF,UAAI,SAAS,SAAS,KAAK;AAAG,mBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,YAAM,KAAK,OAAO,gBAAgB,EAAE,UAAU,SAAQ,CAAE;AACxD,YAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,WAAK,gBAAgB,OAAO,QAAQ;AAMpC,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,aAAa,UAAkB,UAAiB;AACpD,UAAM,MAAuB,CAAA;AAC7B,QAAI,SAAS,SAAS,KAAK;AAAG,iBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,UAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,QAAI,SAAS,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,WAAO,QAAQ,QAAQ,GAAG;EAC5B;EACA,MAAM,mBAAmB,UAAkB,UAAiB;AAC1D,QAAI,SAAS,SAAS,KAAK;AAAG,iBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,UAAM,WAAW,WAAW,MAAM,QAAQ,KAAK,MAAM,QAAQ;AAC7D,QAAI,KAAK,gBAAgB,IAAI,QAAQ,GAAG;AACtC,YAAM,OAAO,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,UAAI,OAAO,QAAQ;AAAa,eAAO,QAAQ,QAAQ,IAAI;WACtD;AACH,eAAO,QAAQ,OAAO,cAAc,QAAQ,eAAe;;WAExD;AACL,aAAO,QAAQ,OAAO,cAAc,QAAQ,iBAAiB;;EAEjE;EACA,MAAM,kBAAkB,MAAc,UAAgB;AACpD,QAAI;AACF,YAAM,eAAwC,MAAM,KAAK,OAAO,kBAAkB;QAChF;QACA;OACD;AACD,aAAO,QAAQ,QAAQ,YAAY;aAC5B,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,mBAAmB,cAAsB,SAAe;AAC5D,QAAI;AACF,YAAM,KAAK,OAAO,mBAAmB;QACnC;QACA;OACD;AACD,YAAM,OAAO,IAAI,mBAAmB,cAAc,MAAM,KAAK,MAAM;AACnE,YAAM,WAAW,MAAM,YAAY;AACnC,WAAK,gBAAgB,IAAI,UAAU,IAAI;AACvC,aAAO,QAAQ,QAAQ,IAAI;aACpB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,kBAAkB,cAAoB;AAC1C,QAAI;AACF,YAAM,KAAK,OAAO,kBAAkB,EAAE,aAAY,CAAE;AACpD,YAAM,WAAW,MAAM,YAAY;AACnC,WAAK,gBAAgB,OAAO,QAAQ;AACpC,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,eAAe,cAAoB;AACvC,UAAM,MAAuB,CAAA;AAC7B,UAAM,WAAW,MAAM,YAAY;AACnC,QAAI,SAAS,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,WAAO,QAAQ,QAAQ,GAAG;EAC5B;EACA,MAAM,qBAAqB,cAAoB;AAC7C,QAAI,KAAK,gBAAgB,IAAI,YAAY,GAAG;AAC1C,YAAM,WAAW,MAAM,YAAY;AACnC,YAAM,OAAO,KAAK,gBAAgB,IAAI,QAAQ;AAC9C,UAAI,OAAO,QAAQ;AAAa,eAAO,QAAQ,QAAQ,IAAI;WACtD;AACH,eAAO,QAAQ,OAAO,cAAc,YAAY,eAAe;;WAE5D;AACL,aAAO,QAAQ,OAAO,cAAc,YAAY,iBAAiB;;EAErE;EACA,MAAM,aAAa,cAAoB;AACrC,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,aAAa,EAAE,aAAY,CAAE;AAC3D,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,yBAAsB;AAC1B,WAAO,KAAK;EACd;EACA,MAAM,sBAAmB;AACvB,UAAM,UAAkD,oBAAI,IAAG;AAC/D,QAAI;AAMF,iBAAW,OAAO,KAAK,gBAAgB,KAAI,GAAI;AAC7C,cAAM,WAAW,IAAI,UAAU,CAAC;AAChC,cAAM,WAAW,IAAI,UAAU,GAAG,CAAC,MAAM,QAAQ,OAAO;AACxD,cAAM,KAAK,OAAO,gBAAgB,EAAE,UAAU,SAAQ,CAAE;AACxD,gBAAQ,IAAI,KAAK,IAAI;;AAGvB,iBAAW,OAAO,QAAQ,KAAI,GAAI;AAChC,aAAK,gBAAgB,OAAO,GAAG;;AAEjC,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,8BAA2B;AAC/B,QAAI;AACF,YAAM,OAAO,CAAC,GAAG,KAAK,gBAAgB,KAAI,CAAE;AAC5C,YAAM,YAAsB,CAAA;AAC5B,YAAM,UAAoB,CAAA;AAC1B,iBAAW,OAAO,MAAM;AACtB,kBAAU,KAAK,IAAI,UAAU,GAAG,CAAC,CAAC;AAClC,gBAAQ,KAAK,IAAI,UAAU,CAAC,CAAC;;AAE/B,YAAM,MAAuB,MAAM,KAAK,OAAO,4BAA4B;QACzE;QACA;OACD;AACD,UAAI,CAAC,IAAI;AAAQ,aAAK,kBAAkB,oBAAI,IAAG;AAC/C,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,WAAK,kBAAkB,oBAAI,IAAG;AAC9B,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,eAAe,YAAkB;AACrC,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,eAAe,EAAE,WAAsB,CAAE;AACvE,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,YAAY,YAAkB;AAClC,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,YAAY,EAAE,WAAsB,CAAE;AACpE,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,eAAe,WAAmB;AACtC,UAAM,aAAsB,aAAa,OAAO,YAAY;AAE5D,QAAI;AACF,YAAM,KAAK,OAAO,eAAe,EAAE,WAAW,WAAU,CAAE;AAC1D,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,mBAAmB,KAAa,WAAmB;AACvD,UAAM,aAAsB,aAAa,OAAO,YAAY;AAC5D,QAAI;AACF,YAAM,KAAK,OAAO,mBAAmB,EAAE,KAAK,WAAW,WAAU,CAAE;AACnE,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,oBAAoB,UAAgB;AACxC,QAAI,SAAS,SAAS,KAAK;AAAG,iBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,oBAAoB,EAAE,SAAkB,CAAE;AACxE,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,uBAAoB;AACxB,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,qBAAoB;AAClD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,0BAAuB;AAC3B,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,wBAAuB;AACrD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,WAAW,UAAgB;AAC/B,QAAI,SAAS,SAAS,KAAK;AAAG,iBAAW,SAAS,MAAM,GAAG,EAAE;AAC7D,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,WAAW,EAAE,SAAkB,CAAE;AAC/D,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,kBAAe;AACnB,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,gBAAe;AAC7C,YAAM,SAAmB,IAAI;AAC7B,aAAO,KAAI;AACX,YAAM,MAAM,EAAE,OAAc;AAC5B,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,oBAAoB,YAAmB;AAC3C,UAAM,OAAe,aAAa,aAAa;AAC/C,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,oBAAoB;QAChD,YAAY;OACb;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,gBAAgB,YAAqB,YAAqB;AAC9D,UAAM,OAAe,aAAa,aAAa;AAC/C,UAAM,SAAmB,aAAa,aAAa,CAAA;AACnD,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,gBAAgB;QAC5C,YAAY;QACZ,YAAY;OACb;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,mBAAmB,YAAqB,YAAqB;AACjE,UAAM,OAAe,aAAa,aAAa;AAC/C,UAAM,SAAmB,aAAa,aAAa,CAAA;AACnD,QAAI;AACF,YAAM,MAAM,MAAM,KAAK,OAAO,mBAAmB;QAC/C,YAAY;QACZ,YAAY;OACb;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,0BAA0B,YAAqB,YAAqB;AACxE,UAAM,OAAe,aAAa,aAAa;AAC/C,UAAM,SAAmB,aAAa,aAAa,CAAA;AACnD,WAAO,KAAK,OAAO,0BAA0B;MAC3C,YAAY;MACZ,YAAY;KACb;EACH;;AAgNI,IAAO,qBAAP,MAAyB;EAC7B,YACU,QACA,UACA,QAAW;AAFX,SAAA,SAAA;AACA,SAAA,WAAA;AACA,SAAA,SAAA;EACP;EAEH,sBAAmB;AACjB,WAAO,KAAK;EACd;EACA,wBAAqB;AACnB,WAAO,KAAK;EACd;EAEA,MAAM,OAAI;AACR,QAAI;AACF,YAAM,KAAK,OAAO,KAAK;QACrB,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,QAAK;AACT,QAAI;AACF,YAAM,KAAK,OAAO,MAAM;QACtB,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,mBAAgB;AACpB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,OAAO,iBAAiB;QACnE,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,OAAO;aACvB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,oBAAiB;AACrB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,OAAO,kBAAkB;QACpE,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,OAAO;aACvB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,sBAAmB;AACvB,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,OAAO,oBAAoB;QACtE,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,OAAO;aACvB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,sBAAmB;AACvB,QAAI;AACF,YAAM,SAA0B,MAAM,KAAK,OAAO,oBAAoB;QACpE,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,MAAM;aACtB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,cAAc,MAAY;AAC9B,QAAI;AACF,YAAM,KAAK,OAAO,cAAc;QAC9B,UAAU,KAAK;QACf;QACA,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,oBAAoB,QAAe;AACvC,QAAI;AACF,YAAM,KAAK,OAAO,oBAAoB;QACpC,UAAU,KAAK;QACf;QACA,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAO;aACf,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,SAAM;AACV,QAAI;AACF,YAAM,MAAoB,MAAM,KAAK,OAAO,OAAO;QACjD,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,aAAU;AACd,QAAI;AACF,YAAM,UAA4B,MAAM,KAAK,OAAO,WAAW;QAC7D,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,OAAO;aACvB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,eAAY;AAChB,QAAI;AACF,YAAM,MAAW,MAAM,KAAK,OAAO,aAAa;QAC9C,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,QAAQ,YAAoB,cAAc,MAAM,UAAU,MAAI;AAClE,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,MAAW,MAAM,KAAK,OAAO,QAAQ;UACzC,UAAU,KAAK;UACf;UACA;UACA,UAAU;UACV;SACD;AACD,eAAO,QAAQ,QAAQ,GAAG;aACrB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,MAAM,WAAmB,QAAgB,UAAU,MAAI;AAC3D,QAAI;AACJ,QAAI;AACF,UAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,cAAM,MAAM,KAAK,OAAO,MAAM;UAC5B,UAAU,KAAK;UACf;UACA;UACA,UAAU,KAAK;UACf,SAAS;SACV;aACI;AACL,cAAM,MAAM,KAAK,OAAO,MAAM;UAC5B,UAAU,KAAK;UACf;UACA,QAAQ,CAAA;UACR,UAAU,KAAK;UACf;SACD;;AAIH,YAAM,MAAM,KAAK,YAAY,GAAG;AAChC,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,IACJ,WACA,QACA,cAAc,MACd,aAAa,MACb,UAAU,MAAI;AAEd,QAAI;AACJ,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,YAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,gBAAM,MAAM,KAAK,OAAO,IAAI;YAC1B,UAAU,KAAK;YACf;YACA;YACA;YACA,UAAU;YACV;YACA,SAAS;WACV;eACI;AACL,gBAAM,MAAM,KAAK,OAAO,IAAI;YAC1B,UAAU,KAAK;YACf;YACA,QAAQ,CAAA;YACR;YACA,UAAU;YACV;YACA;WACD;;AAGH,YAAI,UAAU,MAAM,KAAK,YAAY,IAAI,OAAO;AAChD,eAAO,QAAQ,QAAQ,GAAG;aACrB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,WACJ,KACA,cAAc,MACd,aAAa,MACb,UAAU,MAAI;AAEd,QAAI;AACJ,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,MAAM,KAAK,OAAO,WAAW;UACjC,UAAU,KAAK;UACf;UACA;UACA,UAAU;UACV;UACA;SACD;AAGD,YAAI,UAAU,MAAM,KAAK,YAAY,IAAI,OAAO;AAChD,eAAO,QAAQ,QAAQ,GAAG;aACrB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,WAAQ;AACZ,QAAI;AACF,YAAM,MAAW,MAAM,KAAK,OAAO,WAAW;QAC5C,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,QAAQ,OAAa;AACzB,QAAI;AACF,YAAM,MAAuB,MAAM,KAAK,OAAO,cAAc;QAC3D,UAAU,KAAK;QACf;QACA,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,WAAQ;AACZ,QAAI;AACF,YAAM,MAAuB,MAAM,KAAK,OAAO,SAAS;QACtD,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,SAAM;AACV,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,KAAK,OAAO,eAAe;UAC/B,UAAU,KAAK;UACf,UAAU;SACX;AACD,eAAO,QAAQ,QAAO;aACjB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,kBAAe;AACnB,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,MAAW,MAAM,KAAK,OAAO,gBAAgB;UACjD,UAAU,KAAK;UACf,UAAU;SACX;AACD,eAAO,QAAQ,QAAQ,GAAG;aACrB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,YAAY,UAAgB;AAChC,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,KAAK,OAAO,YAAY;UAC5B,UAAU,KAAK;UACf;UACA,UAAU;SACX;AACD,eAAO,QAAQ,QAAO;aACjB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,cAAW;AACf,QAAI;AACF,YAAM,MAAW,MAAM,KAAK,OAAO,YAAY;QAC7C,UAAU,KAAK;QACf,UAAU,KAAK;OAChB;AACD,UAAI,UAAU;AACd,UAAI,IAAI,WAAW;AAAG,kBAAU,IAAI,KAAK,IAAI,WAAW,GAAI,EAAE,YAAW;AACzE,aAAO,QAAQ,QAAQ,OAAO;aACvB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,aAAa,MAAc,YAAY,OAAK;AAChD,QAAI;AACF,YAAM,MAAW,MAAM,KAAK,OAAO,aAAa;QAC9C,UAAU,KAAK;QACf,gBAAgB;QAChB,UAAU,KAAK;QACf;OACD;AACD,aAAO,QAAQ,QAAQ,GAAG;aACnB,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EACA,MAAM,qBAAkB;AACtB,QAAI;AACF,UAAI,CAAC,KAAK,UAAU;AAClB,cAAM,KAAK,OAAO,mBAAmB;UACnC,UAAU,KAAK;UACf,UAAU;SACX;AACD,eAAO,QAAQ,QAAO;aACjB;AACL,eAAO,QAAQ,OAAO,+BAA+B;;aAEhD,KAAK;AACZ,aAAO,QAAQ,OAAO,GAAG;;EAE7B;EAEA,MAAM,mBAAmB,KAAgB,UAAU,MAAI;AACrD,QAAI,UAAU;AACd,QAAI,WAAW;AACf,QAAI,CAAC,KAAK,UAAU;AAClB,YAAM,KAAK,OAAO,iBAAiB;QACjC,UAAU,KAAK;OAChB;AACD,iBAAW,MAAM,KAAK,OAAO,oBAAoB;QAC/C,UAAU,KAAK;OAChB;AACD,UAAI,CAAC,UAAU;AACb,eAAO,QAAQ,OAAO,gDAAgD;;AAExE,UAAI;AACF,mBAAW,QAAQ,KAAK;AACtB,cAAI,OAAO,SAAS,YAAY,EAAE,eAAe,OAAO;AACtD,kBAAM,IAAI,MAAM,yCAAyC;;AAE3D,cAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,OAAO,SAAS,GAAG;AAC7D,kBAAM,UAAU,KAAK,UAAU,YAAW,EAAG,SAAS,WAAW,IAAI,QAAQ;AAC7E,kBAAM,MAAM,MAAM,KAAK,OAAO,IAAI;cAChC,UAAU,KAAK;cACf,WAAW,KAAK;cAChB,QAAQ,KAAK;cACb,aAAa;cACb,UAAU;cACV,YAAY;cACZ;aACD;AACD,gBAAI,IAAI,QAAQ,UAAU,GAAG;AAC3B,oBAAM,IAAI,MAAM,kCAAkC;;AAEpD,uBAAW,IAAI,QAAQ;iBAClB;AACL,kBAAM,MAAM,MAAM,KAAK,OAAO,QAAQ;cACpC,UAAU,KAAK;cACf,YAAY,KAAK;cACjB,aAAa;cACb,UAAU;aACX;AACD,gBAAI,IAAI,QAAQ,UAAU,GAAG;AAC3B,oBAAM,IAAI,MAAM,sCAAsC;;AAExD,uBAAW,IAAI,QAAQ;;;AAI3B,cAAM,OAAO,MAAM,KAAK,OAAO,kBAAkB;UAC/C,UAAU,KAAK;SAChB;AACD,mBAAW,KAAK,QAAQ;AACxB,cAAM,aAAa,EAAE,SAAS,EAAE,QAAgB,EAAE;AAClD,eAAO,QAAQ,QAAQ,UAAU;eAC1B,KAAU;AAEjB,cAAM,MAAM,IAAI,UAAU,IAAI,UAAU;AACxC,cAAM,KAAK,OAAO,oBAAoB;UACpC,UAAU,KAAK;SAChB;AACD,eAAO,QAAQ,OAAO,GAAG;;WAEtB;AACL,aAAO,QAAQ,OAAO,+BAA+B;;EAEzD;EACQ,MAAM,YAAY,KAAQ;AAChC,UAAM,SAAc;AACpB,SAAI,2BAAK,WAAU,OAAO,IAAI,OAAO,CAAC,MAAM,UAAU;AACpD,UAAI,OAAO,KAAK,IAAI,OAAO,CAAC,CAAC,EAAE,SAAS,aAAa,GAAG;AACtD,cAAM,aAAuB,IAAI,OAAO,CAAC,EAAE,aAAa;AACxD,cAAM,SAAgB,CAAA;AACtB,iBAAS,IAAI,GAAG,IAAI,IAAI,OAAO,QAAQ,KAAK;AAC1C,gBAAM,UAAe,IAAI,OAAO,CAAC;AACjC,gBAAM,aAAkB,CAAA;AACxB,qBAAW,QAAQ,YAAY;AAC7B,uBAAW,IAAI,IAAI,QAAQ,IAAI;;AAEjC,iBAAO,KAAK,UAAU;;AAExB,eAAO,QAAQ,IAAI;;;AAIvB,WAAO,QAAQ,QAAQ,MAAM;EAC/B;;;;ACn2EF,IAAM,kBAAkB,eAAsC,mBAAmB;EAC/E,KAAK,MAAM,OAAO,mBAAO,EAAE,KAAK,CAAC,MAAM,IAAI,EAAE,mBAAkB,CAAE;EACjE,UAAU,MAAO,OAAe,wBAAwB,QAAQ;CACjE;", "names": []}