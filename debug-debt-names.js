// Debug script to check debt data and client names
console.log('🔍 Debugging Debt Names Issue');
console.log('=' .repeat(50));

// Function to check localStorage debt data
function checkDebtData() {
  console.log('\n📊 Checking localStorage debt data...');
  
  try {
    // Check CSV format debt data
    const csvDebts = localStorage.getItem('smartboutique_csv_debts');
    if (csvDebts) {
      console.log('✅ CSV debt data found');
      console.log('Raw CSV data length:', csvDebts.length);
      
      // Try to parse CSV data (simplified)
      const lines = csvDebts.split('\n');
      console.log('CSV lines count:', lines.length);
      
      if (lines.length > 1) {
        console.log('CSV header:', lines[0]);
        console.log('First data row:', lines[1]);
        
        // Check if nomClient column exists
        const headers = lines[0].split(',');
        const nomClientIndex = headers.findIndex(h => h.includes('nomClient'));
        console.log('nomClient column index:', nomClientIndex);
        
        if (nomClientIndex >= 0 && lines.length > 1) {
          const firstRowData = lines[1].split(',');
          console.log('First debt client name:', firstRowData[nomClientIndex]);
        }
      }
    } else {
      console.log('❌ No CSV debt data found');
    }
    
    // Check JSON format debt data (legacy)
    const jsonDebts = localStorage.getItem('smartboutique_debts');
    if (jsonDebts) {
      console.log('✅ JSON debt data found (legacy)');
      try {
        const debts = JSON.parse(jsonDebts);
        console.log('Number of debts:', debts.length);
        if (debts.length > 0) {
          console.log('First debt:', debts[0]);
          console.log('First debt client name:', debts[0].nomClient);
        }
      } catch (error) {
        console.log('❌ Error parsing JSON debt data:', error);
      }
    } else {
      console.log('ℹ️ No JSON debt data found');
    }
    
  } catch (error) {
    console.log('❌ Error checking debt data:', error);
  }
}

// Function to initialize sample debt data
function initializeSampleDebts() {
  console.log('\n🔧 Initializing sample debt data...');
  
  const now = new Date();
  const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
  const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
  
  const sampleDebts = [
    {
      id: 'DET-001',
      venteId: 'VTE-CREDIT-001',
      nomClient: 'Jean Baptiste Mukendi',
      telephoneClient: '+243 812 345 678',
      adresseClient: 'Avenue Lumumba, Kinshasa',
      montantTotalCDF: 150000,
      montantTotalUSD: 53.57,
      montantPayeCDF: 0,
      montantPayeUSD: 0,
      montantRestantCDF: 150000,
      montantRestantUSD: 53.57,
      dateCreation: oneWeekAgo.toISOString(),
      dateEcheance: oneMonthFromNow.toISOString(),
      statut: 'active',
      statutPaiement: 'impaye',
      paiements: [],
      notes: 'Vente à crédit - Produits électroniques'
    },
    {
      id: 'DET-002',
      venteId: 'VTE-CREDIT-002',
      nomClient: 'Marie Kabila Tshisekedi',
      telephoneClient: '+243 823 456 789',
      adresseClient: 'Boulevard du 30 Juin, Kinshasa',
      montantTotalCDF: 75000,
      montantTotalUSD: 26.79,
      montantPayeCDF: 25000,
      montantPayeUSD: 8.93,
      montantRestantCDF: 50000,
      montantRestantUSD: 17.86,
      dateCreation: oneWeekAgo.toISOString(),
      dateEcheance: oneMonthFromNow.toISOString(),
      statut: 'active',
      statutPaiement: 'impaye',
      paiements: [
        {
          id: 'PAY-001',
          montantCDF: 25000,
          montantUSD: 8.93,
          methodePaiement: 'cash',
          datePaiement: now.toISOString(),
          notes: 'Paiement partiel'
        }
      ],
      notes: 'Dette partiellement payée'
    }
  ];
  
  // Store as JSON for testing
  localStorage.setItem('smartboutique_debts', JSON.stringify(sampleDebts));
  
  // Also create CSV format
  const csvHeaders = 'id,venteId,nomClient,telephoneClient,adresseClient,montantTotalCDF,montantTotalUSD,montantPayeCDF,montantPayeUSD,montantRestantCDF,montantRestantUSD,dateCreation,dateEcheance,statut,statutPaiement,paiements,notes';
  const csvRows = sampleDebts.map(debt => {
    return [
      debt.id,
      debt.venteId,
      debt.nomClient,
      debt.telephoneClient || '',
      debt.adresseClient || '',
      debt.montantTotalCDF,
      debt.montantTotalUSD || '',
      debt.montantPayeCDF,
      debt.montantPayeUSD || '',
      debt.montantRestantCDF,
      debt.montantRestantUSD || '',
      debt.dateCreation,
      debt.dateEcheance,
      debt.statut,
      debt.statutPaiement,
      JSON.stringify(debt.paiements),
      debt.notes || ''
    ].join(',');
  });
  
  const csvData = csvHeaders + '\n' + csvRows.join('\n');
  localStorage.setItem('smartboutique_csv_debts', csvData);
  
  console.log('✅ Sample debt data initialized');
  console.log('Sample debts created:', sampleDebts.length);
  sampleDebts.forEach((debt, index) => {
    console.log(`  ${index + 1}. ${debt.nomClient} - ${debt.montantTotalCDF} CDF`);
  });
}

// Function to clear debt data
function clearDebtData() {
  console.log('\n🗑️ Clearing existing debt data...');
  localStorage.removeItem('smartboutique_debts');
  localStorage.removeItem('smartboutique_csv_debts');
  console.log('✅ Debt data cleared');
}

// Main execution
console.log('Starting debt data debugging...');
checkDebtData();

console.log('\n' + '='.repeat(50));
console.log('🔧 FIXING DEBT DATA');
console.log('='.repeat(50));

clearDebtData();
initializeSampleDebts();
checkDebtData();

console.log('\n✅ Debt data debugging complete!');
console.log('📝 Instructions:');
console.log('1. Refresh the SmartBoutique application');
console.log('2. Navigate to Gestion des Dettes');
console.log('3. Client names should now be visible');
