/**
 * Electron SQLite Bridge Service
 * Handles communication between renderer and main process for SQLite operations
 */

import { Product, User, Sale, Debt, Expense } from '../types';

// Check if we're in Electron renderer process
const isElectronRenderer = typeof window !== 'undefined' && window.electronAPI;

export class ElectronSQLiteBridge {
  private isAvailable: boolean = false;

  constructor() {
    this.isAvailable = isElectronRenderer;
    if (!this.isAvailable) {
      console.warn('Electron SQLite bridge not available - not in Electron renderer process');
    }
  }

  private async invoke(channel: string, ...args: any[]): Promise<any> {
    if (!this.isAvailable) {
      throw new Error('Electron SQLite bridge not available');
    }

    try {
      // For now, we'll use a fallback approach since IPC isn't set up yet
      // This will be implemented when we add IPC handlers to the main process
      throw new Error('IPC not implemented yet - using fallback storage');
    } catch (error) {
      console.warn('SQLite IPC failed, using fallback:', error);
      throw error;
    }
  }

  // Products operations
  async getProducts(): Promise<Product[]> {
    return this.invoke('sqlite:getProducts');
  }

  async setProducts(products: Product[]): Promise<void> {
    return this.invoke('sqlite:setProducts', products);
  }

  async addProduct(product: Product): Promise<void> {
    return this.invoke('sqlite:addProduct', product);
  }

  async updateProduct(product: Product): Promise<void> {
    return this.invoke('sqlite:updateProduct', product);
  }

  async deleteProduct(id: string): Promise<void> {
    return this.invoke('sqlite:deleteProduct', id);
  }

  // Users operations
  async getUsers(): Promise<User[]> {
    return this.invoke('sqlite:getUsers');
  }

  async setUsers(users: User[]): Promise<void> {
    return this.invoke('sqlite:setUsers', users);
  }

  // Sales operations
  async getSales(): Promise<Sale[]> {
    return this.invoke('sqlite:getSales');
  }

  async setSales(sales: Sale[]): Promise<void> {
    return this.invoke('sqlite:setSales', sales);
  }

  // Migration operations
  async migrateFromCSV(csvData: any): Promise<any> {
    return this.invoke('sqlite:migrateFromCSV', csvData);
  }

  async getStats(): Promise<any> {
    return this.invoke('sqlite:getStats');
  }

  // Check if SQLite is available
  isReady(): boolean {
    return this.isAvailable;
  }
}

export const electronSQLiteBridge = new ElectronSQLiteBridge();
