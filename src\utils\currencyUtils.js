/**
 * Centralized Currency and Financial Calculation Utilities for SmartBoutique
 * Provides standardized currency conversion, formatting, and validation functions
 * with consistent rounding and French number formatting
 */

// Standard exchange rate constant
export const DEFAULT_EXCHANGE_RATE = 2800;

/**
 * Standardized rounding function for all financial calculations
 * Rounds to 2 decimal places using consistent method
 */
export const roundCurrency = (value) => {
  // Handle null, undefined, or NaN values
  if (value == null || isNaN(value)) {
    return 0;
  }
  return Math.round(value * 100) / 100;
};

/**
 * Centralized currency conversion function
 * Ensures consistency across all components
 */
export const convertCurrency = (amount, fromCurrency, toCurrency, exchangeRate = DEFAULT_EXCHANGE_RATE) => {
  // Input validation
  if (amount == null || isNaN(amount)) {
    return 0;
  }
  
  if (exchangeRate == null || isNaN(exchangeRate) || exchangeRate <= 0) {
    console.warn('Invalid exchange rate provided, using default:', DEFAULT_EXCHANGE_RATE);
    exchangeRate = DEFAULT_EXCHANGE_RATE;
  }

  // Same currency, no conversion needed
  if (fromCurrency === toCurrency) {
    return roundCurrency(amount);
  }

  let convertedAmount;
  
  if (fromCurrency === 'CDF' && toCurrency === 'USD') {
    convertedAmount = amount / exchangeRate;
  } else if (fromCurrency === 'USD' && toCurrency === 'CDF') {
    convertedAmount = amount * exchangeRate;
  } else {
    console.error('Unsupported currency conversion:', fromCurrency, 'to', toCurrency);
    return 0;
  }

  return roundCurrency(convertedAmount);
};

/**
 * Convert CDF to USD with standardized rounding
 */
export const convertCDFToUSD = (amountCDF, exchangeRate = DEFAULT_EXCHANGE_RATE) => {
  return convertCurrency(amountCDF, 'CDF', 'USD', exchangeRate);
};

/**
 * Convert USD to CDF with standardized rounding
 */
export const convertUSDToCDF = (amountUSD, exchangeRate = DEFAULT_EXCHANGE_RATE) => {
  return convertCurrency(amountUSD, 'USD', 'CDF', exchangeRate);
};

/**
 * French number formatting with spaces as thousands separators
 */
export const formatNumberFrench = (value, minimumFractionDigits = 0, maximumFractionDigits = 2) => {
  if (value == null || isNaN(value)) {
    return '0';
  }
  
  return value.toLocaleString('fr-FR', {
    minimumFractionDigits,
    maximumFractionDigits
  });
};

/**
 * Format currency with French number formatting
 */
export const formatCurrency = (amount, currency, options = {}) => {
  const {
    showSymbol = true,
    minimumFractionDigits = currency === 'USD' ? 2 : 0,
    maximumFractionDigits = currency === 'USD' ? 2 : 2
  } = options;

  const safeAmount = (amount == null || isNaN(amount)) ? 0 : amount;
  const formattedNumber = formatNumberFrench(safeAmount, minimumFractionDigits, maximumFractionDigits);

  if (!showSymbol) {
    return formattedNumber;
  }

  if (currency === 'USD') {
    return `$${formattedNumber}`;
  } else if (currency === 'CDF') {
    return `${formattedNumber} CDF`;
  }
  
  return `${formattedNumber} ${currency}`;
};

/**
 * Format dual currency display (CDF primary, USD secondary)
 */
export const formatDualCurrency = (amountCDF, exchangeRate = DEFAULT_EXCHANGE_RATE, options = {}) => {
  const {
    showSymbols = true,
    compact = false,
    primaryOnly = false
  } = options;

  const safeCDF = (amountCDF == null || isNaN(amountCDF)) ? 0 : amountCDF;
  const safeRate = (exchangeRate == null || isNaN(exchangeRate) || exchangeRate <= 0) ? DEFAULT_EXCHANGE_RATE : exchangeRate;
  
  const amountUSD = convertCDFToUSD(safeCDF, safeRate);
  
  const cdfFormatted = formatCurrency(safeCDF, 'CDF', { showSymbol: showSymbols });
  
  if (primaryOnly) {
    return cdfFormatted;
  }
  
  const usdFormatted = formatCurrency(amountUSD, 'USD', { showSymbol: showSymbols });
  
  if (compact) {
    return `${cdfFormatted} (≈ ${usdFormatted})`;
  }
  
  return {
    primary: cdfFormatted,
    secondary: usdFormatted,
    primaryAmount: safeCDF,
    secondaryAmount: amountUSD
  };
};

/**
 * Input validation for financial values
 */
export const validateFinancialInput = (value, fieldName = 'valeur', options = {}) => {
  const {
    allowZero = false,
    allowNegative = false,
    minValue = null,
    maxValue = null
  } = options;

  const errors = [];

  // Check if value is a valid number
  if (value == null || isNaN(value)) {
    errors.push(`${fieldName} doit être un nombre valide`);
    return { isValid: false, errors };
  }

  // Check for negative values
  if (!allowNegative && value < 0) {
    errors.push(`${fieldName} ne peut pas être négatif`);
  }

  // Check for zero values
  if (!allowZero && value === 0) {
    errors.push(`${fieldName} doit être supérieur à zéro`);
  }

  // Check minimum value
  if (minValue !== null && value < minValue) {
    errors.push(`${fieldName} doit être supérieur ou égal à ${minValue}`);
  }

  // Check maximum value
  if (maxValue !== null && value > maxValue) {
    errors.push(`${fieldName} doit être inférieur ou égal à ${maxValue}`);
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate exchange rate
 */
export const validateExchangeRate = (rate) => {
  return validateFinancialInput(rate, 'Le taux de change', {
    allowZero: false,
    allowNegative: false,
    minValue: 1
  });
};

/**
 * Validate pricing (purchase and selling prices)
 */
export const validatePricing = (prixAchat, prixVente, fieldNames = {}) => {
  const {
    purchaseFieldName = 'Le prix d\'achat',
    sellingFieldName = 'Le prix de vente'
  } = fieldNames;

  // Validate individual prices
  const purchaseValidation = validateFinancialInput(prixAchat, purchaseFieldName);
  const sellingValidation = validateFinancialInput(prixVente, sellingFieldName);

  const errors = [...purchaseValidation.errors, ...sellingValidation.errors];

  // Check if selling price is greater than purchase price
  if (purchaseValidation.isValid && sellingValidation.isValid && prixVente <= prixAchat) {
    errors.push('Le prix de vente doit être supérieur au prix d\'achat');
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Safe calculation wrapper with error handling
 */
export const safeCalculation = (calculationFn, fallbackValue = 0, errorMessage = 'Erreur de calcul financier') => {
  try {
    const result = calculationFn();
    return (result == null || isNaN(result)) ? fallbackValue : result;
  } catch (error) {
    console.error(errorMessage, error);
    return fallbackValue;
  }
};

/**
 * Calculate profit margin percentage
 */
export const calculateProfitMargin = (prixVente, prixAchat) => {
  return safeCalculation(() => {
    if (prixVente === 0) return 0;
    return ((prixVente - prixAchat) / prixVente) * 100;
  }, 0, 'Erreur lors du calcul de la marge bénéficiaire');
};

/**
 * Calculate profit amount
 */
export const calculateProfit = (prixVente, prixAchat, quantity = 1) => {
  return safeCalculation(() => {
    return (prixVente - prixAchat) * quantity;
  }, 0, 'Erreur lors du calcul du bénéfice');
};

/**
 * Calculate revenue (total sales amount)
 */
export const calculateRevenue = (prixVente, quantity = 1) => {
  return safeCalculation(() => {
    return prixVente * quantity;
  }, 0, 'Erreur lors du calcul du chiffre d\'affaires');
};

/**
 * REVENUE CALCULATIONS (Chiffre d'affaires - money coming in from sales)
 */

/**
 * Calculate total revenue from sales (Prix de vente × Quantité vendue)
 */
export const calculateChiffresAffaires = (sales, products = []) => {
  return safeCalculation(() => {
    if (!Array.isArray(sales)) return 0;

    return sales.reduce((total, sale) => {
      if (!sale.produits || !Array.isArray(sale.produits)) return total;

      const saleRevenue = sale.produits.reduce((saleTotal, item) => {
        return saleTotal + (item.prixUnitaireCDF * item.quantite);
      }, 0);

      return total + saleRevenue;
    }, 0);
  }, 0, 'Erreur lors du calcul du chiffre d\'affaires');
};

/**
 * Calculate revenue for a specific period
 */
export const getRevenusParPeriode = (sales, startDate, endDate) => {
  return safeCalculation(() => {
    if (!Array.isArray(sales)) return 0;

    const filteredSales = sales.filter(sale => {
      const saleDate = new Date(sale.datevente);
      return saleDate >= startDate && saleDate <= endDate;
    });

    return calculateChiffresAffaires(filteredSales);
  }, 0, 'Erreur lors du calcul des revenus par période');
};

/**
 * PROFIT CALCULATIONS (Bénéfice - actual gain after costs)
 */

/**
 * Calculate profit for a single product
 */
export const calculateBeneficeUnitaire = (prixVente, prixAchat) => {
  return safeCalculation(() => {
    return prixVente - prixAchat;
  }, 0, 'Erreur lors du calcul du bénéfice unitaire');
};

/**
 * Calculate total profit from sales (Revenue - Purchase costs)
 */
export const calculateBenefice = (sales, products = []) => {
  return safeCalculation(() => {
    if (!Array.isArray(sales) || !Array.isArray(products)) return 0;

    return sales.reduce((totalProfit, sale) => {
      if (!sale.produits || !Array.isArray(sale.produits)) return totalProfit;

      const saleProfit = sale.produits.reduce((saleTotal, item) => {
        const product = products.find(p => p.id === item.produitId);
        if (!product) return saleTotal;

        const unitProfit = calculateBeneficeUnitaire(item.prixUnitaireCDF, product.prixAchatCDF || 0);
        return saleTotal + (unitProfit * item.quantite);
      }, 0);

      return totalProfit + saleProfit;
    }, 0);
  }, 0, 'Erreur lors du calcul du bénéfice total');
};

/**
 * Calculate profit margin percentage
 */
export const calculateMargeProfit = (totalRevenue, totalProfit) => {
  return safeCalculation(() => {
    if (totalRevenue === 0) return 0;
    return (totalProfit / totalRevenue) * 100;
  }, 0, 'Erreur lors du calcul de la marge de profit');
};

/**
 * Calculate inventory potential profit (if all stock sold)
 */
export const calculatePotentialProfit = (products) => {
  return safeCalculation(() => {
    if (!Array.isArray(products)) return 0;

    return products.reduce((total, product) => {
      const unitProfit = calculateBeneficeUnitaire(product.prixCDF || 0, product.prixAchatCDF || 0);
      return total + (unitProfit * (product.stock || 0));
    }, 0);
  }, 0, 'Erreur lors du calcul du profit potentiel');
};

/**
 * DUAL CURRENCY CALCULATIONS
 */

/**
 * Calculate financial metrics in both currencies
 */
export const calculateDualCurrencyMetrics = (amountCDF, exchangeRate = DEFAULT_EXCHANGE_RATE) => {
  const safeCDF = (amountCDF == null || isNaN(amountCDF)) ? 0 : amountCDF;
  const safeRate = (exchangeRate == null || isNaN(exchangeRate) || exchangeRate <= 0) ? DEFAULT_EXCHANGE_RATE : exchangeRate;

  return {
    CDF: roundCurrency(safeCDF),
    USD: convertCDFToUSD(safeCDF, safeRate),
    formatted: {
      CDF: formatCurrency(safeCDF, 'CDF'),
      USD: formatCurrency(convertCDFToUSD(safeCDF, safeRate), 'USD'),
      dual: formatDualCurrency(safeCDF, safeRate)
    }
  };
};

// French error messages for common validation scenarios
export const ERROR_MESSAGES = {
  NEGATIVE_PRICE: 'Le prix ne peut pas être négatif',
  ZERO_PRICE: 'Le prix doit être supérieur à zéro',
  INVALID_EXCHANGE_RATE: 'Le taux de change doit être supérieur à zéro',
  NEGATIVE_QUANTITY: 'La quantité doit être un nombre positif',
  SELLING_PRICE_TOO_LOW: 'Le prix de vente doit être supérieur au prix d\'achat',
  INVALID_NUMBER: 'Veuillez saisir un nombre valide',
  REQUIRED_FIELD: 'Ce champ est requis'
};
