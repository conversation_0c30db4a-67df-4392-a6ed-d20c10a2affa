/**
 * CSV Encoding Verification Script for SmartBoutique
 * Tests that UTF-8 BOM is properly added to CSV exports
 */

// Simulate the CSVUtils functionality
const UTF8_BOM = '\uFEFF';

// Test data with French accented characters
const testData = [
  {
    nom: 'Café Premium',
    description: 'Café de qualité supérieure',
    categorie: 'Boissons',
    prix: '2500',
    creePar: 'Créé par Admin'
  },
  {
    nom: 'Thé vert bio',
    description: 'Thé biologique importé',
    categorie: 'Boissons', 
    prix: '1800',
    creePar: 'Créé par Vendeur'
  },
  {
    nom: 'Équipement électronique',
    description: 'Réparation nécessaire',
    categorie: 'Électronique',
    prix: '45000',
    creePar: 'Créé par Technicien'
  }
];

const columns = [
  { key: 'nom', header: 'Nom du Produit', type: 'string' },
  { key: 'description', header: 'Description', type: 'string' },
  { key: 'categorie', header: 'Catégorie', type: 'string' },
  { key: 'prix', header: 'Prix (CDF)', type: 'number' },
  { key: 'creePar', header: 'Créé par', type: 'string' }
];

function arrayToCSV(data, columns, includeBOM = true) {
  if (!data || data.length === 0) {
    const emptyCSV = columns.map(col => col.header).join(',') + '\n';
    return includeBOM ? UTF8_BOM + emptyCSV : emptyCSV;
  }

  // Create header row
  const headers = columns.map(col => col.header);
  const headerRow = headers.join(',');

  // Create data rows
  const dataRows = data.map(item => {
    return columns.map(col => {
      let value = item[col.key];
      
      // Handle different data types
      if (value === null || value === undefined) {
        value = '';
      } else if (col.type === 'date' && value) {
        value = new Date(value).toISOString().split('T')[0];
      } else if (col.type === 'boolean') {
        value = value ? 'Oui' : 'Non';
      } else if (typeof value === 'object') {
        value = JSON.stringify(value);
      }

      // Escape CSV special characters
      value = String(value);
      if (value.includes(',') || value.includes('"') || value.includes('\n')) {
        value = '"' + value.replace(/"/g, '""') + '"';
      }

      return value;
    }).join(',');
  });

  const csvContent = [headerRow, ...dataRows].join('\n');
  
  // Add UTF-8 BOM for Excel compatibility
  return includeBOM ? UTF8_BOM + csvContent : csvContent;
}

function testEncoding() {
  console.log('🧪 Testing CSV Encoding for SmartBoutique\n');
  
  // Test 1: CSV without BOM
  console.log('📄 Test 1: CSV without BOM (problematic)');
  const csvWithoutBOM = arrayToCSV(testData, columns, false);
  console.log('First few characters (hex):', 
    csvWithoutBOM.substring(0, 20).split('').map(c => 
      c.charCodeAt(0).toString(16).padStart(2, '0')
    ).join(' ')
  );
  console.log('Content preview:', csvWithoutBOM.substring(0, 50) + '...\n');
  
  // Test 2: CSV with BOM
  console.log('📄 Test 2: CSV with BOM (solution)');
  const csvWithBOM = arrayToCSV(testData, columns, true);
  console.log('First few characters (hex):', 
    csvWithBOM.substring(0, 20).split('').map(c => 
      c.charCodeAt(0).toString(16).padStart(2, '0')
    ).join(' ')
  );
  console.log('Content preview:', csvWithBOM.substring(0, 50) + '...\n');
  
  // Verification
  console.log('🔍 Verification:');
  console.log('✅ BOM present:', csvWithBOM.startsWith(UTF8_BOM));
  console.log('✅ BOM absent in non-BOM version:', !csvWithoutBOM.startsWith(UTF8_BOM));
  console.log('✅ Content identical (except BOM):', 
    csvWithBOM.substring(1) === csvWithoutBOM);
  
  // French character test
  console.log('\n🇫🇷 French Character Test:');
  const frenchChars = ['é', 'è', 'à', 'ç', 'ê', 'ô', 'û', 'î', 'ï', 'ù'];
  frenchChars.forEach(char => {
    const included = csvWithBOM.includes(char);
    console.log(`${included ? '✅' : '❌'} Character '${char}' found: ${included}`);
  });
  
  console.log('\n🎯 Summary:');
  console.log('- CSV with BOM will display correctly in Excel');
  console.log('- CSV without BOM will show garbled characters in Excel');
  console.log('- Both versions contain the same data');
  console.log('- BOM adds only 3 bytes (EF BB BF) to file size');
  
  return {
    withBOM: csvWithBOM,
    withoutBOM: csvWithoutBOM,
    bomPresent: csvWithBOM.startsWith(UTF8_BOM),
    contentMatch: csvWithBOM.substring(1) === csvWithoutBOM
  };
}

// Run the test
if (typeof module !== 'undefined' && module.exports) {
  // Node.js environment
  module.exports = { testEncoding, arrayToCSV, UTF8_BOM };
} else {
  // Browser environment
  window.testEncoding = testEncoding;
  window.arrayToCSV = arrayToCSV;
  window.UTF8_BOM = UTF8_BOM;
}

// Auto-run test if in Node.js
if (typeof require !== 'undefined' && require.main === module) {
  testEncoding();
}
