import { useEffect } from 'react';

/**
 * Simplified hook to fix input field focus issues in Electron environment
 * This addresses Electron-specific input focus problems without interfering with normal browser behavior
 */
export const useFocusFix = () => {
  useEffect(() => {
    // Minimal fix for Electron input focus issues
    const fixInputFocus = () => {
      // Enable all input fields to be focusable and responsive
      const inputs = document.querySelectorAll('input, textarea, [contenteditable]');

      inputs.forEach((input) => {
        const element = input as HTMLElement;

        // Ensure proper styling and interaction (CSS-only fixes)
        element.style.pointerEvents = 'auto';
        element.style.userSelect = 'text';

        // Set proper tabindex if not already set
        if (!element.hasAttribute('tabindex') || element.tabIndex < 0) {
          element.tabIndex = 0;
        }
      });
    };

    // Apply fix immediately
    fixInputFocus();

    // Apply fix on route changes only (removed aggressive MutationObserver)
    const handleRouteChange = () => {
      setTimeout(fixInputFocus, 50);
    };

    // Listen for hash changes (since we use HashRouter)
    window.addEventListener('hashchange', handleRouteChange);

    // Cleanup
    return () => {
      window.removeEventListener('hashchange', handleRouteChange);
    };
  }, []);
};

// Removed usePostLoginFocusFix hook as it was causing input delays
// The simplified useFocusFix hook above should handle all focus issues
