/**
 * Desktop Storage Service for SmartBoutique
 * Handles data persistence using localStorage with CSV format
 */

import {
  CSVUtils,
  PRODUCT_COLUMNS,
  USER_COLUMNS,
  SALES_COLUMNS,
  DEBT_COLUMNS,
  EXPENSE_COLUMNS,
  EMPLOYEE_COLUMNS,
  EMPLOYEE_PAYMENT_COLUMNS,
  SETTINGS_COLUMNS,
  settingsToCSVArray,
  csvArrayToSettings
} from './csv-utils';

class StorageService {
  private prefix = 'smartboutique_csv_';

  // Generic methods
  private getKey(key: string): string {
    return `${this.prefix}${key}`;
  }

  /**
   * Store CSV data in localStorage
   */
  private setCSV(key: string, data: any[], columns: any[]): void {
    try {
      const csvString = CSVUtils.arrayToCSV(data, columns);
      localStorage.setItem(this.getKey(key), csvString);
    } catch (error) {
      console.error(`Erreur lors de la sauvegarde CSV ${key}:`, error);
    }
  }

  /**
   * Retrieve CSV data from localStorage
   */
  private getCSV(key: string, columns: any[], defaultData: any[] = []): any[] {
    try {
      const csvString = localStorage.getItem(this.getKey(key));
      if (csvString) {
        return CSVUtils.csvToArray(csvString, columns);
      }
      return defaultData;
    } catch (error) {
      console.error(`Erreur lors de la lecture CSV ${key}:`, error);
      return defaultData;
    }
  }

  // Legacy generic methods for backward compatibility
  set<T>(key: string, value: T): void {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(this.getKey(key), serializedValue);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
    }
  }

  get<T>(key: string, defaultValue: T): T {
    try {
      const item = localStorage.getItem(this.getKey(key));
      if (item === null) {
        return defaultValue;
      }
      return JSON.parse(item) as T;
    } catch (error) {
      console.error('Erreur lors de la lecture:', error);
      return defaultValue;
    }
  }

  remove(key: string): void {
    localStorage.removeItem(this.getKey(key));
  }

  clear(): void {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(this.prefix)) {
        localStorage.removeItem(key);
      }
    });
  }

  // Specific data methods using CSV format
  getUsers() {
    return this.getCSV('users', USER_COLUMNS, []);
  }

  setUsers(users: any[]) {
    this.setCSV('users', users, USER_COLUMNS);
  }

  getProducts() {
    const products = this.getCSV('products', PRODUCT_COLUMNS, []);
    // Ensure all product amounts are valid numbers
    return products.map((product: any) => {
      // Legacy support: if no purchase price, use selling price as purchase price
      if (!product.prixAchatCDF && product.prixCDF) {
        product.prixAchatCDF = product.prixCDF * 0.7; // Assume 30% margin for legacy products
      }

      product.prixAchatCDF = Number(product.prixAchatCDF) || 0;
      product.prixAchatUSD = product.prixAchatUSD ? Number(product.prixAchatUSD) : undefined;
      product.prixCDF = Number(product.prixCDF) || 0;
      product.prixUSD = product.prixUSD ? Number(product.prixUSD) : undefined;

      // Calculate profit fields if they don't exist or are invalid
      if (!product.beneficeUnitaireCDF || isNaN(Number(product.beneficeUnitaireCDF))) {
        product.beneficeUnitaireCDF = product.prixCDF - product.prixAchatCDF;
      } else {
        product.beneficeUnitaireCDF = Number(product.beneficeUnitaireCDF);
      }

      if (!product.beneficeUnitaireUSD || isNaN(Number(product.beneficeUnitaireUSD))) {
        // Calculate USD profit using default exchange rate
        const DEFAULT_EXCHANGE_RATE = 2800;
        product.beneficeUnitaireUSD = Math.round((product.beneficeUnitaireCDF / DEFAULT_EXCHANGE_RATE) * 100) / 100;
      } else {
        product.beneficeUnitaireUSD = Number(product.beneficeUnitaireUSD);
      }

      product.stock = Number(product.stock) || 0;
      product.stockMin = Number(product.stockMin) || 0;

      // Ensure valid dates
      const now = new Date().toISOString();
      if (!product.dateCreation || isNaN(new Date(product.dateCreation).getTime())) {
        product.dateCreation = now;
      }
      if (!product.dateModification || isNaN(new Date(product.dateModification).getTime())) {
        product.dateModification = now;
      }

      return product;
    });
  }

  setProducts(products: any[]) {
    this.setCSV('products', products, PRODUCT_COLUMNS);
  }

  getSales() {
    try {
      const sales = this.getCSV('sales', SALES_COLUMNS, []);

      // Filter out any invalid sales data
      const validSales = sales.filter((sale: any) => sale && typeof sale === 'object');

      // Ensure all sale amounts are valid numbers and map CSV field names to interface field names
      return validSales.map((sale: any) => {
        // Map CSV field names to interface field names
        const mappedSale = {
          ...sale,
          // Map 'date' to 'datevente' for interface compatibility
          datevente: sale.date || sale.datevente || new Date().toISOString(),
          // Map 'typePaiement' to 'methodePaiement' for interface compatibility
          methodePaiement: sale.typePaiement || sale.methodePaiement || 'cash',
          // Map 'client' to 'nomClient' for interface compatibility
          nomClient: sale.client || sale.nomClient || 'Client',
        };

        mappedSale.totalCDF = Number(mappedSale.totalCDF) || 0;
        mappedSale.totalUSD = mappedSale.totalUSD ? Number(mappedSale.totalUSD) : undefined;

        // Parse produits from JSON string if needed, then ensure all item amounts are numbers
        let produits = mappedSale.produits || [];

        // If produits is a string (from CSV), parse it as JSON
        if (typeof produits === 'string') {
          try {
            produits = JSON.parse(produits);
          } catch (error) {
            console.warn('Error parsing produits JSON:', error);
            produits = [];
          }
        }

        // Ensure produits is an array and process each item
        if (Array.isArray(produits)) {
          mappedSale.produits = produits.map((item: any) => ({
            ...item,
            quantite: Number(item.quantite) || 0,
            prixUnitaireCDF: Number(item.prixUnitaireCDF) || 0,
            prixUnitaireUSD: item.prixUnitaireUSD ? Number(item.prixUnitaireUSD) : undefined,
            totalCDF: Number(item.totalCDF) || 0,
            totalUSD: item.totalUSD ? Number(item.totalUSD) : undefined
          }));
        } else {
          mappedSale.produits = [];
        }

        return mappedSale;
      });
    } catch (error) {
      console.error('Error getting sales data:', error);
      return [];
    }
  }

  setSales(sales: any[]) {
    // Map interface field names to CSV field names before saving
    const mappedSales = sales.map((sale: any) => ({
      ...sale,
      // Map interface field names to CSV column names
      date: sale.datevente || sale.date,
      typePaiement: sale.methodePaiement || sale.typePaiement,
      client: sale.nomClient || sale.client,
      // Ensure produits is stored as JSON string
      produits: typeof sale.produits === 'string' ? sale.produits : JSON.stringify(sale.produits || [])
    }));

    this.setCSV('sales', mappedSales, SALES_COLUMNS);
  }

  getDebts() {
    const debts = this.getCSV('debts', DEBT_COLUMNS, []);
    // Migrate existing debts to include statutPaiement field and ensure all amounts are numbers
    return debts.map((debt: any) => {
      if (!debt.statutPaiement) {
        // Set default payment status based on current status
        debt.statutPaiement = debt.statut === 'paid' ? 'paye' : 'impaye';
      }

      // Ensure all amount fields are valid numbers - preserve actual values, don't default to 0
      debt.montantTotalCDF = debt.montantTotalCDF !== undefined && debt.montantTotalCDF !== null && debt.montantTotalCDF !== ''
        ? Number(debt.montantTotalCDF)
        : 0;
      debt.montantTotalUSD = debt.montantTotalUSD ? Number(debt.montantTotalUSD) : undefined;
      debt.montantPayeCDF = debt.montantPayeCDF !== undefined && debt.montantPayeCDF !== null && debt.montantPayeCDF !== ''
        ? Number(debt.montantPayeCDF)
        : 0;
      debt.montantPayeUSD = debt.montantPayeUSD ? Number(debt.montantPayeUSD) : undefined;
      debt.montantRestantCDF = debt.montantRestantCDF !== undefined && debt.montantRestantCDF !== null && debt.montantRestantCDF !== ''
        ? Number(debt.montantRestantCDF)
        : (debt.montantTotalCDF - debt.montantPayeCDF); // Calculate if missing
      debt.montantRestantUSD = debt.montantRestantUSD ? Number(debt.montantRestantUSD) : undefined;

      // Parse paiements from JSON string if needed, then ensure all payment amounts are numbers
      let paiements = debt.paiements || [];

      // If paiements is a string (from CSV), parse it as JSON
      if (typeof paiements === 'string') {
        try {
          paiements = JSON.parse(paiements);
        } catch (error) {
          console.warn('Error parsing paiements JSON:', error);
          paiements = [];
        }
      }

      // Ensure paiements is an array and process each payment
      if (Array.isArray(paiements)) {
        debt.paiements = paiements.map((payment: any) => ({
          ...payment,
          montantCDF: Number(payment.montantCDF) || 0,
          montantUSD: payment.montantUSD ? Number(payment.montantUSD) : undefined
        }));
      } else {
        debt.paiements = [];
      }

      return debt;
    });
  }

  setDebts(debts: any[]) {
    this.setCSV('debts', debts, DEBT_COLUMNS);
  }

  // Legacy methods for backward compatibility
  getDettes() {
    return this.getDebts();
  }

  setDettes(debts: any[]) {
    this.setDebts(debts);
  }

  getCreances() {
    return this.getDebts();
  }

  setCreances(debts: any[]) {
    this.setDebts(debts);
  }

  getExpenses() {
    return this.getCSV('expenses', EXPENSE_COLUMNS, []);
  }

  setExpenses(expenses: any[]) {
    this.setCSV('expenses', expenses, EXPENSE_COLUMNS);
  }

  // Employee Payments methods
  getEmployeePayments() {
    return this.getCSV('employee_payments', EMPLOYEE_PAYMENT_COLUMNS, []);
  }

  setEmployeePayments(payments: any[]) {
    this.setCSV('employee_payments', payments, EMPLOYEE_PAYMENT_COLUMNS);
  }

  addEmployeePayment(payment: any) {
    const payments = this.getEmployeePayments();
    payments.push(payment);
    this.setEmployeePayments(payments);
  }

  updateEmployeePayment(updatedPayment: any) {
    const payments = this.getEmployeePayments();
    const index = payments.findIndex(p => p.id === updatedPayment.id);
    if (index !== -1) {
      payments[index] = updatedPayment;
      this.setEmployeePayments(payments);
    }
  }

  deleteEmployeePayment(id: string) {
    const payments = this.getEmployeePayments();
    const filteredPayments = payments.filter(p => p.id !== id);
    this.setEmployeePayments(filteredPayments);
  }

  // Employee methods
  getEmployees() {
    return this.getCSV('employees', EMPLOYEE_COLUMNS, []);
  }

  setEmployees(employees: any[]) {
    this.setCSV('employees', employees, EMPLOYEE_COLUMNS);
  }

  addEmployee(employee: any) {
    const employees = this.getEmployees();
    employees.push(employee);
    this.setEmployees(employees);
  }

  updateEmployee(updatedEmployee: any) {
    const employees = this.getEmployees();
    const index = employees.findIndex(emp => emp.id === updatedEmployee.id);
    if (index !== -1) {
      employees[index] = updatedEmployee;
      this.setEmployees(employees);
    }
  }

  deleteEmployee(id: string) {
    const employees = this.getEmployees();
    const filteredEmployees = employees.filter(emp => emp.id !== id);
    this.setEmployees(filteredEmployees);
  }

  getSettings() {
    return this.get('settings', {
      tauxChangeUSDCDF: 2800,
      seuilStockBas: 10,
      categories: [
        { id: '1', nom: 'Électronique', description: 'Appareils électroniques', couleur: '#2196F3' },
        { id: '2', nom: 'Vêtements', description: 'Vêtements et accessoires', couleur: '#4CAF50' },
        { id: '3', nom: 'Alimentation', description: 'Produits alimentaires', couleur: '#FF9800' },
        { id: '4', nom: 'Maison', description: 'Articles pour la maison', couleur: '#9C27B0' },
        { id: '5', nom: 'Beauté', description: 'Produits de beauté', couleur: '#E91E63' },
        { id: '6', nom: 'Boissons', description: 'Boissons et breuvages', couleur: '#00BCD4' },
        { id: '7', nom: 'Épicerie', description: 'Produits d\'épicerie', couleur: '#795548' },
        { id: '8', nom: 'Livres', description: 'Livres et éducation', couleur: '#607D8B' },
        { id: '9', nom: 'Sport', description: 'Articles de sport', couleur: '#FF5722' },
        { id: '10', nom: 'Santé', description: 'Produits de santé', couleur: '#8BC34A' }
      ],
      entreprise: {
        nom: 'SmartBoutique',
        adresse: 'Kinshasa, RDC',
        telephone: '+243 000 000 000',
        email: '<EMAIL>',
        rccm: '', // Registre de Commerce et du Crédit Mobilier
        idNat: '', // Identification Nationale
        logo: '' // Base64 encoded logo image data
      }
    });
  }

  setSettings(settings: any) {
    this.set('settings', settings);
  }

  getCurrentUser() {
    return this.get('currentUser', null);
  }

  setCurrentUser(user: any) {
    this.set('currentUser', user);
  }

  // Initialize default data
  initializeDefaultData() {
    // Initialize users if empty
    const users = this.getUsers();
    if (users.length === 0) {
      const defaultUsers = [
        {
          id: '1',
          nom: 'Super Admin',
          email: '<EMAIL>',
          role: 'super_admin',
          motDePasse: 'admin123',
          dateCreation: new Date().toISOString(),
          actif: true
        },
        {
          id: '2',
          nom: 'Gestionnaire',
          email: '<EMAIL>',
          role: 'admin',
          motDePasse: 'manager123',
          dateCreation: new Date().toISOString(),
          actif: true
        },
        {
          id: '3',
          nom: 'Employé',
          email: '<EMAIL>',
          role: 'employee',
          motDePasse: 'employee123',
          dateCreation: new Date().toISOString(),
          actif: true
        }
      ];
      this.setUsers(defaultUsers);
    }

    // Initialize comprehensive product catalog if empty
    const products = this.getProducts();
    if (products.length === 0) {
      this.initializeProductCatalog();
    }

    // Initialize sample sales if empty
    const sales = this.getSales();
    if (sales.length === 0) {
      this.initializeSampleSales();
    }

    // Initialize sample debts if empty
    const debts = this.getDebts();
    if (debts.length === 0) {
      this.initializeSampleDebts();
    }
  }

  // Clear all data (for debugging)
  clearAllData() {
    localStorage.clear();
    console.log('All localStorage data cleared');
  }

  // Initialize comprehensive product catalog
  private initializeProductCatalog() {
    const sampleProducts = [
      // Existing products (preserved)
      {
        id: '1',
        nom: 'iPhone 15',
        description: 'Smartphone Apple iPhone 15 128GB',
        prixAchatCDF: 1680000, // Purchase price
        prixAchatUSD: 600,
        prixCDF: 2240000, // Selling price
        prixUSD: 800,
        beneficeUnitaireCDF: 560000, // Profit margin
        beneficeUnitaireUSD: 200,
        codeQR: 'SB12345678ABCD',
        categorie: 'Électronique',
        stock: 25,
        stockMin: 5,
        codeBarres: '1234567890123',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '2',
        nom: 'T-shirt Nike',
        description: 'T-shirt Nike en coton, taille M',
        prixAchatCDF: 70000, // Purchase price
        prixAchatUSD: 25,
        prixCDF: 98000, // Selling price
        prixUSD: 35,
        beneficeUnitaireCDF: 28000, // Profit margin
        beneficeUnitaireUSD: 10,
        codeQR: 'SB12345679EFGH',
        categorie: 'Vêtements',
        stock: 50,
        stockMin: 10,
        codeBarres: '1234567890124',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '3',
        nom: 'Café Arabica',
        description: 'Café Arabica premium 500g',
        prixAchatCDF: 25200, // Purchase price
        prixAchatUSD: 9,
        prixCDF: 33600, // Selling price
        prixUSD: 12,
        beneficeUnitaireCDF: 8400, // Profit margin
        beneficeUnitaireUSD: 3,
        codeQR: 'SB12345680IJKL',
        categorie: 'Alimentation',
        stock: 8,
        stockMin: 15,
        codeBarres: '1234567890125',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Alimentation & Épicerie
      {
        id: '4',
        nom: 'Sucre',
        description: 'Sucre blanc cristallisé 1kg',
        prixAchatCDF: 6300, // Purchase price
        prixAchatUSD: 2.25,
        prixCDF: 8400, // Selling price
        prixUSD: 3,
        beneficeUnitaireCDF: 2100, // Profit margin
        beneficeUnitaireUSD: 0.75,
        codeQR: 'SB12345681MNOP',
        categorie: 'Épicerie',
        stock: 120,
        stockMin: 20,
        codeBarres: '1234567890126',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '5',
        nom: 'Riz',
        description: 'Riz blanc parfumé 5kg',
        prixAchatCDF: 33600, // Purchase price
        prixAchatUSD: 12,
        prixCDF: 42000, // Selling price
        prixUSD: 15,
        beneficeUnitaireCDF: 8400, // Profit margin
        beneficeUnitaireUSD: 3,
        codeQR: 'SB12345682QRST',
        categorie: 'Alimentation',
        stock: 80,
        stockMin: 15,
        codeBarres: '1234567890127',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '6',
        nom: 'Sel',
        description: 'Sel de cuisine iodé 500g',
        prixCDF: 2800,
        prixUSD: 1,
        codeQR: 'SB12345683UVWX',
        categorie: 'Épicerie',
        stock: 200,
        stockMin: 30,
        codeBarres: '1234567890128',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Boissons
      {
        id: '7',
        nom: 'Lait',
        description: 'Lait entier UHT 1 litre',
        prixCDF: 5600,
        prixUSD: 2,
        codeQR: 'SB12345684YZAB',
        categorie: 'Boissons',
        stock: 60,
        stockMin: 12,
        codeBarres: '1234567890129',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },
      {
        id: '8',
        nom: 'Thé',
        description: 'Thé noir en sachets, boîte de 25',
        prixCDF: 11200,
        prixUSD: 4,
        codeQR: 'SB12345685CDEF',
        categorie: 'Boissons',
        stock: 45,
        stockMin: 10,
        codeBarres: '1234567890130',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Vêtements
      {
        id: '9',
        nom: 'Vestes',
        description: 'Veste en jean unisexe, taille L',
        prixCDF: 140000,
        prixUSD: 50,
        codeQR: 'SB12345686GHIJ',
        categorie: 'Vêtements',
        stock: 30,
        stockMin: 5,
        codeBarres: '1234567890131',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      },

      // REQUESTED PRODUCTS - Livres
      {
        id: '10',
        nom: 'Livres',
        description: 'Roman français "Le Petit Prince"',
        prixCDF: 22400,
        prixUSD: 8,
        codeQR: 'SB12345687KLMN',
        categorie: 'Livres',
        stock: 25,
        stockMin: 5,
        codeBarres: '1234567890132',
        dateCreation: new Date().toISOString(),
        dateModification: new Date().toISOString()
      }
    ];

    this.setProducts(sampleProducts);
  }

  // Initialize sample sales data
  private initializeSampleSales() {
    const now = new Date();
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000);

    const sampleSales = [
      {
        id: '1',
        datevente: yesterday.toISOString(),
        nomClient: 'Jean Mukendi',
        telephoneClient: '+243 900 000 001',
        produits: [
          {
            produitId: '1',
            nomProduit: 'iPhone 15',
            quantite: 1,
            prixUnitaireCDF: 2240000,
            prixUnitaireUSD: 800,
            totalCDF: 2240000,
            totalUSD: 800
          }
        ],
        totalCDF: 2240000,
        totalUSD: 800,
        methodePaiement: 'cash',
        typeVente: 'cash',
        vendeur: 'Super Admin',
        notes: 'Vente comptant'
      },
      {
        id: '2',
        datevente: yesterday.toISOString(),
        nomClient: 'Marie Kabila',
        telephoneClient: '+243 900 000 002',
        produits: [
          {
            produitId: '2',
            nomProduit: 'T-shirt Nike',
            quantite: 2,
            prixUnitaireCDF: 98000,
            prixUnitaireUSD: 35,
            totalCDF: 196000,
            totalUSD: 70
          },
          {
            produitId: '4',
            nomProduit: 'Sucre',
            quantite: 3,
            prixUnitaireCDF: 8400,
            prixUnitaireUSD: 3,
            totalCDF: 25200,
            totalUSD: 9
          }
        ],
        totalCDF: 221200,
        totalUSD: 79,
        methodePaiement: 'mobile_money',
        typeVente: 'cash',
        vendeur: 'Gestionnaire',
        notes: 'Paiement mobile money'
      },
      {
        id: '3',
        datevente: twoDaysAgo.toISOString(),
        nomClient: 'Paul Tshisekedi',
        telephoneClient: '+243 900 000 003',
        produits: [
          {
            produitId: '5',
            nomProduit: 'Riz',
            quantite: 2,
            prixUnitaireCDF: 42000,
            prixUnitaireUSD: 15,
            totalCDF: 84000,
            totalUSD: 30
          }
        ],
        totalCDF: 84000,
        totalUSD: 30,
        methodePaiement: 'card',
        typeVente: 'cash',
        vendeur: 'Employé',
        notes: 'Paiement par carte'
      }
    ];

    this.setSales(sampleSales);
  }

  // Initialize sample debt data
  private initializeSampleDebts() {
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
    const oneMonthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
    const twoWeeksFromNow = new Date(now.getTime() + 14 * 24 * 60 * 60 * 1000);

    const sampleDebts = [
      // New unpaid debt (0% paid)
      {
        id: 'DET-001',
        venteId: 'VTE-CREDIT-001',
        nomClient: 'Jean Baptiste Mukendi',
        telephoneClient: '+243 812 345 678',
        adresseClient: 'Avenue Lumumba, Kinshasa',
        montantTotalCDF: 150000,
        montantTotalUSD: 53.57,
        montantPayeCDF: 0,
        montantPayeUSD: 0,
        montantRestantCDF: 150000,
        montantRestantUSD: 53.57,
        dateCreation: oneWeekAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [],
        notes: 'Vente à crédit - Produits électroniques'
      },
      // Partially paid debt (40% paid)
      {
        id: 'DET-002',
        venteId: 'VTE-CREDIT-002',
        nomClient: 'Marie Kabila Tshisekedi',
        telephoneClient: '+243 998 765 432',
        adresseClient: 'Boulevard du 30 Juin, Kinshasa',
        montantTotalCDF: 250000,
        montantTotalUSD: 89.29,
        montantPayeCDF: 100000,
        montantPayeUSD: 35.71,
        montantRestantCDF: 150000,
        montantRestantUSD: 53.57,
        dateCreation: twoWeeksAgo.toISOString(),
        dateEcheance: twoWeeksFromNow.toISOString(),
        statut: 'active',
        statutPaiement: 'impaye',
        paiements: [
          {
            id: 'PAY-001',
            montantCDF: 100000,
            montantUSD: 35.71,
            methodePaiement: 'cash',
            datePaiement: new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement partiel en espèces'
          }
        ],
        notes: 'Paiement partiel effectué - Vêtements et accessoires'
      },
      // Fully paid debt (100% paid)
      {
        id: 'DET-003',
        venteId: 'VTE-CREDIT-003',
        nomClient: 'Joseph Kabila Kabange',
        telephoneClient: '+243 811 222 333',
        adresseClient: 'Avenue de la Paix, Lubumbashi',
        montantTotalCDF: 180000,
        montantTotalUSD: 64.29,
        montantPayeCDF: 180000,
        montantPayeUSD: 64.29,
        montantRestantCDF: 0,
        montantRestantUSD: 0,
        dateCreation: twoWeeksAgo.toISOString(),
        dateEcheance: oneMonthFromNow.toISOString(),
        statut: 'paid',
        statutPaiement: 'paye',
        paiements: [
          {
            id: 'PAY-002',
            montantCDF: 180000,
            montantUSD: 64.29,
            methodePaiement: 'mobile_money',
            datePaiement: new Date(now.getTime() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement complet via Mobile Money'
          }
        ],
        notes: 'Dette entièrement payée - Alimentation et boissons'
      },
      // Overdue debt (past due date)
      {
        id: 'DET-004',
        venteId: 'VTE-CREDIT-004',
        nomClient: 'Fatou Diallo Sankara',
        telephoneClient: '+243 977 888 999',
        adresseClient: 'Quartier Matonge, Kinshasa',
        montantTotalCDF: 320000,
        montantTotalUSD: 114.29,
        montantPayeCDF: 80000,
        montantPayeUSD: 28.57,
        montantRestantCDF: 240000,
        montantRestantUSD: 85.71,
        dateCreation: new Date(now.getTime() - 45 * 24 * 60 * 60 * 1000).toISOString(),
        dateEcheance: new Date(now.getTime() - 15 * 24 * 60 * 60 * 1000).toISOString(), // Past due
        statut: 'overdue',
        statutPaiement: 'impaye',
        paiements: [
          {
            id: 'PAY-003',
            montantCDF: 80000,
            montantUSD: 28.57,
            methodePaiement: 'cash',
            datePaiement: new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000).toISOString(),
            notes: 'Paiement partiel initial'
          }
        ],
        notes: 'Dette en retard - Nécessite suivi'
      }
    ];

    this.setDebts(sampleDebts);
  }

  // Export data for backup (CSV format)
  exportData() {
    const users = this.getUsers();
    const products = this.getProducts();
    const sales = this.getSales();
    const debts = this.getDebts();
    const expenses = this.getExpenses();
    const employeePayments = this.getEmployeePayments();
    const settings = this.getSettings();

    const exportData = {
      exportDate: new Date().toISOString(),
      products: CSVUtils.arrayToCSV(products, PRODUCT_COLUMNS),
      users: CSVUtils.arrayToCSV(users, USER_COLUMNS),
      sales: CSVUtils.arrayToCSV(sales, SALES_COLUMNS),
      debts: CSVUtils.arrayToCSV(debts, DEBT_COLUMNS),
      expenses: CSVUtils.arrayToCSV(expenses, EXPENSE_COLUMNS),
      employeePayments: CSVUtils.arrayToCSV(employeePayments, EMPLOYEE_PAYMENT_COLUMNS),
      settings: CSVUtils.arrayToCSV(settingsToCSVArray(settings), SETTINGS_COLUMNS)
    };

    // Create a comprehensive backup file
    const backupContent = `SmartBoutique - Sauvegarde Complète (Desktop)
Date d'exportation: ${exportData.exportDate}

=== PRODUITS ===
${exportData.products}

=== UTILISATEURS ===
${exportData.users}

=== VENTES ===
${exportData.sales}

=== DETTES ===
${exportData.debts}

=== DÉPENSES ===
${exportData.expenses}

=== PAIEMENTS EMPLOYÉS ===
${exportData.employeePayments}

=== PARAMÈTRES ===
${exportData.settings}
`;

    return {
      csvData: backupContent,
      exportDate: exportData.exportDate
    };
  }

  // Import data from backup (supports both JSON and CSV formats)
  importData(data: any) {
    try {
      // Handle CSV format (new format)
      if (data.csvData && typeof data.csvData === 'string') {
        return this.importFromCSVBackup(data.csvData);
      }

      // Handle legacy JSON format for backward compatibility
      if (data.users) this.setUsers(data.users);
      if (data.products) this.setProducts(data.products);
      if (data.sales) this.setSales(data.sales);
      if (data.debts) this.setDebts(data.debts);
      if (data.expenses) this.setExpenses(data.expenses);
      if (data.employeePayments) this.setEmployeePayments(data.employeePayments);
      if (data.settings) this.setSettings(data.settings);
      return true;
    } catch (error) {
      console.error('Erreur lors de l\'importation:', error);
      return false;
    }
  }

  // Import from CSV backup format
  private importFromCSVBackup(csvBackup: string): boolean {
    try {
      // Parse the backup file sections
      const sections = this.parseCSVBackup(csvBackup);

      if (sections.products) {
        const products = CSVUtils.csvToArray(sections.products, PRODUCT_COLUMNS);
        this.setProducts(products);
      }

      if (sections.users) {
        const users = CSVUtils.csvToArray(sections.users, USER_COLUMNS);
        this.setUsers(users);
      }

      if (sections.sales) {
        const sales = CSVUtils.csvToArray(sections.sales, SALES_COLUMNS);
        this.setSales(sales);
      }

      if (sections.debts) {
        const debts = CSVUtils.csvToArray(sections.debts, DEBT_COLUMNS);
        this.setDebts(debts);
      }

      if (sections.expenses) {
        const expenses = CSVUtils.csvToArray(sections.expenses, EXPENSE_COLUMNS);
        this.setExpenses(expenses);
      }

      if (sections.employeePayments) {
        const employeePayments = CSVUtils.csvToArray(sections.employeePayments, EMPLOYEE_PAYMENT_COLUMNS);
        this.setEmployeePayments(employeePayments);
      }

      if (sections.settings) {
        const settingsArray = CSVUtils.csvToArray(sections.settings, SETTINGS_COLUMNS);
        const settings = csvArrayToSettings(settingsArray);
        this.setSettings(settings);
      }

      return true;
    } catch (error) {
      console.error('Erreur lors de l\'importation CSV:', error);
      return false;
    }
  }

  // Parse CSV backup file into sections
  private parseCSVBackup(csvBackup: string): any {
    const sections: any = {};
    const lines = csvBackup.split('\n');
    let currentSection = '';
    let currentData: string[] = [];

    for (const line of lines) {
      if (line.startsWith('=== ') && line.endsWith(' ===')) {
        // Save previous section
        if (currentSection && currentData.length > 0) {
          sections[currentSection] = currentData.join('\n');
        }

        // Start new section
        const sectionName = line.replace(/=== | ===/g, '').toLowerCase();
        switch (sectionName) {
          case 'produits':
            currentSection = 'products';
            break;
          case 'utilisateurs':
            currentSection = 'users';
            break;
          case 'ventes':
            currentSection = 'sales';
            break;
          case 'dettes':
            currentSection = 'debts';
            break;
          case 'dépenses':
            currentSection = 'expenses';
            break;
          case 'paiements employés':
            currentSection = 'employeePayments';
            break;
          case 'paramètres':
            currentSection = 'settings';
            break;
          default:
            currentSection = '';
        }
        currentData = [];
      } else if (currentSection && line.trim() !== '') {
        currentData.push(line);
      }
    }

    // Save last section
    if (currentSection && currentData.length > 0) {
      sections[currentSection] = currentData.join('\n');
    }

    return sections;
  }

  // CSV Export methods for desktop
  exportCSV(dataType: 'products' | 'users' | 'sales' | 'debts' | 'expenses' | 'employee_payments'): string {
    let data: any[] = [];
    let columns: any[] = [];

    switch (dataType) {
      case 'products':
        data = this.getProducts();
        columns = PRODUCT_COLUMNS;
        break;
      case 'users':
        data = this.getUsers();
        columns = USER_COLUMNS;
        break;
      case 'sales':
        data = this.getSales();
        columns = SALES_COLUMNS;
        break;
      case 'debts':
        data = this.getDebts();
        columns = DEBT_COLUMNS;
        break;
      case 'expenses':
        data = this.getExpenses();
        columns = EXPENSE_COLUMNS;
        break;
      case 'employee_payments':
        data = this.getEmployeePayments();
        columns = EMPLOYEE_PAYMENT_COLUMNS;
        break;
    }

    return CSVUtils.arrayToCSV(data, columns);
  }

  // CSV Import methods for desktop
  importCSV(dataType: 'products' | 'users', csvContent: string, replaceExisting: boolean = false): { success: boolean; message: string; errors: string[] } {
    try {
      let columns: any[] = [];
      let currentData: any[] = [];

      switch (dataType) {
        case 'products':
          columns = PRODUCT_COLUMNS;
          currentData = replaceExisting ? [] : this.getProducts();
          break;
        case 'users':
          columns = USER_COLUMNS;
          currentData = replaceExisting ? [] : this.getUsers();
          break;
        default:
          return { success: false, message: 'Type de données non supporté', errors: [] };
      }

      const importedData = CSVUtils.csvToArray(csvContent, columns);
      const validation = CSVUtils.validateCSVData(importedData, columns);

      if (!validation.isValid) {
        return { success: false, message: 'Données invalides', errors: validation.errors };
      }

      // Merge or replace data
      let finalData = importedData;
      if (!replaceExisting && currentData.length > 0) {
        const existingIds = new Set(currentData.map((item: any) => item.id));
        const newItems = importedData.filter((item: any) => !existingIds.has(item.id));
        finalData = [...currentData, ...newItems];
      }

      // Save data
      switch (dataType) {
        case 'products':
          this.setProducts(finalData);
          break;
        case 'users':
          this.setUsers(finalData);
          break;
      }

      return { success: true, message: `${importedData.length} éléments importés avec succès`, errors: [] };
    } catch (error) {
      return { success: false, message: 'Erreur lors de l\'importation: ' + (error as Error).message, errors: [(error as Error).message] };
    }
  }
}

export const storageService = new StorageService();
