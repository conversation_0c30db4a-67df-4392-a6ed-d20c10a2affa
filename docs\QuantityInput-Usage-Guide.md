# Guide d'utilisation du composant QuantityInput

## Vue d'ensemble

Le composant `QuantityInput` de SmartBoutique a été optimisé pour privilégier la saisie directe au clavier tout en conservant les boutons d'incrémentation/décrémentation comme éléments d'interface optionnels.

## Fonctionnalités principales

### 1. Saisie directe prioritaire
- **Méthode principale** : Saisie directe au clavier
- **Méthode secondaire** : Boutons +/- (avec opacité réduite)
- **Support décimal** : Accepte les nombres décimaux mais les arrondit pour les quantités
- **Validation en temps réel** : Validation immédiate lors de la saisie

### 2. Interface utilisateur optimisée
- **Placeholder** : "Tapez la quantité..."
- **Texte d'aide** : "Tapez directement la quantité (recommandé) ou utilisez +/-"
- **Focus visuel** : Arrière-plan légèrement coloré lors du focus
- **Boutons discrets** : Opacité 0.6 par défaut, 0.8 au survol

### 3. Validation et contraintes
- **Valeurs min/max** : Respect des limites définies
- **Nombres entiers** : Conversion automatique des décimaux
- **Gestion des erreurs** : Retour à une valeur valide si saisie incorrecte

## Utilisation recommandée

```tsx
<QuantityInput
  value={quantity}
  onChange={setQuantity}
  min={1}
  max={999}
  size="small"
  showButtons={true}
  allowDirectInput={true}
  label="Quantité"
  helperText="Tapez directement la quantité désirée"
/>
```

## Paramètres de configuration

| Paramètre | Type | Défaut | Description |
|-----------|------|--------|-------------|
| `value` | number | - | Valeur actuelle (requis) |
| `onChange` | function | - | Callback de changement (requis) |
| `min` | number | 1 | Valeur minimum |
| `max` | number | 999999 | Valeur maximum |
| `size` | 'small' \| 'medium' | 'small' | Taille du composant |
| `showButtons` | boolean | true | Afficher les boutons +/- |
| `allowDirectInput` | boolean | true | Permettre la saisie directe |
| `label` | string | - | Libellé du champ |
| `helperText` | string | - | Texte d'aide |
| `disabled` | boolean | false | Désactiver le composant |
| `error` | boolean | false | État d'erreur |

## Modes d'utilisation

### Mode complet (recommandé)
```tsx
// Saisie directe + boutons (par défaut)
<QuantityInput
  value={quantity}
  onChange={setQuantity}
  showButtons={true}
  allowDirectInput={true}
/>
```

### Mode saisie uniquement
```tsx
// Saisie directe seulement
<QuantityInput
  value={quantity}
  onChange={setQuantity}
  showButtons={false}
  allowDirectInput={true}
/>
```

### Mode boutons uniquement (déconseillé)
```tsx
// Boutons seulement (pour compatibilité)
<QuantityInput
  value={quantity}
  onChange={setQuantity}
  showButtons={true}
  allowDirectInput={false}
/>
```

## Implémentations actuelles

### 1. Page des ventes (SalesPage)
- **Ajout de produit** : Saisie de quantité avec stock maximum
- **Modification panier** : Ajustement des quantités dans le panier
- **Configuration** : Boutons + saisie directe activés

### 2. Page des produits (ProductsPage)
- **Stock actuel** : Saisie du stock disponible
- **Stock minimum** : Définition du seuil d'alerte
- **Configuration** : Boutons + saisie directe activés

## Bonnes pratiques

### 1. Toujours privilégier la saisie directe
```tsx
// ✅ Bon
<QuantityInput allowDirectInput={true} showButtons={true} />

// ❌ Éviter
<QuantityInput allowDirectInput={false} showButtons={true} />
```

### 2. Définir des limites appropriées
```tsx
// ✅ Bon - limites réalistes
<QuantityInput min={1} max={selectedProduct?.stock || 999} />

// ❌ Éviter - limites trop restrictives
<QuantityInput min={1} max={10} />
```

### 3. Fournir un contexte utilisateur
```tsx
// ✅ Bon - avec aide contextuelle
<QuantityInput
  label="Quantité"
  helperText="Stock disponible: 25"
/>
```

## Accessibilité

- **Navigation clavier** : Support complet des touches de navigation
- **Raccourcis** : Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X supportés
- **Validation visuelle** : Indication claire des erreurs
- **Texte d'aide** : Instructions en français

## Migration depuis TextField

Si vous utilisez encore des `TextField` avec `type="number"` pour les quantités :

```tsx
// Ancien code
<TextField
  type="number"
  value={stock}
  onChange={(e) => setStock(parseInt(e.target.value) || 0)}
/>

// Nouveau code
<QuantityInput
  value={stock}
  onChange={setStock}
  min={0}
  label="Stock"
/>
```

## Support et maintenance

Le composant `QuantityInput` est maintenu dans `/src/components/QuantityInput/` et exporté via l'index pour une utilisation facile dans toute l'application.
